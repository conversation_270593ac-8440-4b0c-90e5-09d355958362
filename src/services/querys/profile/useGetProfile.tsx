// src/hooks/useCategoryQuery.js
import { useQuery } from "@tanstack/react-query";
import { getProfile } from "../../apis/profile";

const useGetProfile = (
  id: string | undefined,
  deviceInfo: { deviceId: string; deviceType: string } | null
) => {
  return useQuery({
    queryKey: ["profile", id, deviceInfo],
    queryFn: () => getProfile(id, deviceInfo),
    enabled: !!id,
  });
};

export default useGetProfile;
