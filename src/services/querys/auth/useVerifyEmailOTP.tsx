// src/hooks/useLoginMutation.js
import { showToast } from "@/src/_helper/toast/showToast";
import { useLanguage } from "@/src/context/LanguageContext";
import { useMutation } from "@tanstack/react-query";
import { isAxiosError } from "axios";
import { router } from "expo-router";
import { verifyEmailOTP } from "../../apis/auth";

const useVerifyEmailOTP = () => {
  const { t } = useLanguage();
  return useMutation({
    mutationFn: ({
      email,
      otp,
      type,
      setIsOtpVisible,
      userType,
    }: {
      email: string;
      otp: string;
      type: string;
      setIsOtpVisible: (value: boolean) => void;
      userType: "client" | "vendor";
    }) => verifyEmailOTP(email, otp, type, userType),
    onSuccess: (_, { setIsOtpVisible, type, userType }) => {
      showToast(t("backend.success"), t("backend.otp_verified"), "success");
      setIsOtpVisible(false);
      if (type === "created-account") {
        router.replace({
          pathname: "/(user-entry)/signin",
          params: {
            type: userType,
          },
        });
      }
    },

    onError: (error: unknown) => {
      if (isAxiosError(error)) {
        const message =
          error.response?.data?.message || "backend.unexpectedError";
        showToast(t("backend.error"), t(message), "error");
      } else {
        showToast(t("backend.error"), t("backend.unexpectedError"), "error");
      }
    },
  });
};

export default useVerifyEmailOTP;
