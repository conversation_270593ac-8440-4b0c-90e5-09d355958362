// src/hooks/useLoginMutation.js
import { showToast } from "@/src/_helper/toast/showToast";
import { useLanguage } from "@/src/context/LanguageContext";
import { useMutation } from "@tanstack/react-query";
import { isAxiosError } from "axios";
import { router } from "expo-router";
import { resetPassword } from "../../apis/auth";

const useResetPassword = () => {
  const { t } = useLanguage();
  return useMutation({
    mutationFn: ({
      email,
      password,
      type,
      otp,
    }: {
      email: string;
      password: string;
      type: "client" | "vendor";
      otp: string;
    }) => resetPassword(email, password, type, otp),
    onSuccess: (_, { type }) => {
      showToast(t("backend.success"), t("backend.password_reset"), "success");
      router.replace({
        pathname: "/(user-entry)/signin",
        params: { type },
      });
    },
    onError: (error: unknown) => {
      if (isAxiosError(error)) {
        const message =
          error.response?.data?.message || "backend.unexpectedError";
        showToast(t("backend.error"), t(message), "error");
      } else {
        showToast(t("backend.error"), t("backend.unexpectedError"), "error");
      }
    },
  });
};

export default useResetPassword;
