// src/hooks/useLoginMutation.js
import { showToast } from "@/src/_helper/toast/showToast";
import { useLanguage } from "@/src/context/LanguageContext";
import { SignupParams } from "@/src/types";
import { useMutation } from "@tanstack/react-query";
import { isAxiosError } from "axios";
import { useRouter } from "expo-router";
import { signup } from "../../apis/auth";

const useSignupMutation = () => {
  const router = useRouter();
  const { t } = useLanguage();
  return useMutation({
    mutationFn: ({ fullName, email, password, type }: SignupParams) =>
      signup(fullName, email, password, type),
    onSuccess: (_, { email, type }) => {
      router.replace({
        pathname: "/(user-entry)/verify-email",
        params: { email: email, type },
      });
    },

    onError: (error: unknown) => {
      if (isAxiosError(error)) {
        const message =
          error.response?.data?.message || "backend.unexpectedError";
        showToast(t("backend.error"), t(message), "error");
      } else {
        showToast(t("backend.error"), t("backend.unexpectedError"), "error");
      }
    },
  });
};

export default useSignupMutation;
