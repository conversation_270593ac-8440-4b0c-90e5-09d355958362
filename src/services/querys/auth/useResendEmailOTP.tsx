// src/hooks/useLoginMutation.js
import { showToast } from "@/src/_helper/toast/showToast";
import { useLanguage } from "@/src/context/LanguageContext";
import { useMutation } from "@tanstack/react-query";
import { isAxiosError } from "axios";
import { resendEmailOTP } from "../../apis/auth";

const useResendEmailOTP = () => {
  const { t } = useLanguage();
  return useMutation({
    mutationFn: ({
      email,
      type,
      userType,
    }: {
      email: string;
      type: string;
      userType: "client" | "vendor";
    }) => resendEmailOTP(email, type, userType),
    onSuccess: (_) => {
      showToast(t("backend.success"), t("backend.otp_sent"), "success");
    },
    onError: (error: unknown) => {
      if (isAxiosError(error)) {
        const message =
          error.response?.data?.message || "backend.unexpectedError";
        showToast(t("backend.error"), t(message), "error");
      } else {
        showToast(t("backend.error"), t("backend.unexpectedError"), "error");
      }
    },
  });
};

export default useResendEmailOTP;
