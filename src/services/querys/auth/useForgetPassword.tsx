// src/hooks/useLoginMutation.js
import { showToast } from "@/src/_helper/toast/showToast";
import { useLanguage } from "@/src/context/LanguageContext";
import { useMutation } from "@tanstack/react-query";
import { isAxiosError } from "axios";
import { router } from "expo-router";
import { forgetPassword } from "../../apis/auth";

const useForgetPassword = () => {
  const { t } = useLanguage();
  return useMutation({
    mutationFn: ({
      email,
      type,
    }: {
      email: string;
      type: "client" | "vendor";
    }) => forgetPassword(email, type),
    onSuccess: (_, { email, type }) => {
      showToast(t("backend.success"), t("backend.otp_sent"), "success");
      router.replace({
        pathname: "/(user-entry)/reset-password",
        params: {
          email,
          type,
        },
      });
    },
    onError: (error: unknown) => {
      if (isAxiosError(error)) {
        const message =
          error.response?.data?.message || "backend.unexpectedError";
        showToast(t("backend.error"), t(message), "error");
      } else {
        showToast(t("backend.error"), t("backend.unexpectedError"), "error");
      }
    },
  });
};

export default useForgetPassword;
