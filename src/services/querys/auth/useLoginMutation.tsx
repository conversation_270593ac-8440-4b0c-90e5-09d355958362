// src/hooks/useLoginMutation.js
import { showToast } from "@/src/_helper/toast/showToast";
import { useSession } from "@/src/context/AuthContext";
import { useLanguage } from "@/src/context/LanguageContext";
import { LoginParams } from "@/src/types";
import { useMutation } from "@tanstack/react-query";
import { isAxiosError } from "axios";
import { useRouter } from "expo-router";
import { login } from "../../apis/auth";

const useLoginMutation = () => {
  const { signIn } = useSession();
  const router = useRouter();
  const { t } = useLanguage();
  return useMutation({
    mutationFn: ({ email, password, type }: LoginParams) =>
      login(email, password, type),
    onSuccess: (data) => {
      if (data.status === "Verify") {
        router.replace({
          pathname: "/(user-entry)/verify-email",
          params: { email: data.email, type: data.type },
        });
        return;
      }
      signIn({
        token: data.data.token,
        role: data.data.role,
        userId: data.data.userId,
        name: data.data.name,
      });
      if (data.data.role === "client") {
        router.replace("/(client)/(tabs)");
      } else if (data.data.role === "vendor") {
        router.replace("/(vendor)/(tabs)");
      }
    },
    onError: (error: unknown) => {
      if (isAxiosError(error)) {
        const message =
          error.response?.data?.message || "backend.unexpectedError";
        showToast(t("backend.error"), t(message), "error");
      } else {
        showToast(t("backend.error"), t("backend.unexpectedError"), "error");
      }
    },
  });
};

export default useLoginMutation;
