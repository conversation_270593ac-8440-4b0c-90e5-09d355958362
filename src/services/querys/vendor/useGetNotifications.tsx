import { INotification } from "@/src/types/notifications";
import { useInfiniteQuery } from "@tanstack/react-query";
import { getNotifications } from "../../apis/vendor";

type MessagePage = {
  data: {
    items: INotification[];
    totalItems: number;
    currentPage: number;
    totalPages: number;
    hasNextPage: boolean;
    hasPreviousPage: boolean;
  };
};
const useGetNotifications = () => {
  return useInfiniteQuery<MessagePage, Error>({
    queryKey: ["notifications"],
    queryFn: async ({ pageParam }) => {
      return await getNotifications({
        page: pageParam as number,
      });
    },
    getNextPageParam: (lastPage) => {
      return lastPage.data.hasNextPage
        ? lastPage.data.currentPage + 1
        : undefined;
    },
    initialPageParam: 1,
  });
};
export default useGetNotifications;
