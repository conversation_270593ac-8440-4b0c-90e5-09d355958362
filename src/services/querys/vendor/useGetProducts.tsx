import { IVendorProduct, MessagePage } from "@/src/types";
import { useInfiniteQuery } from "@tanstack/react-query";
import { getVendorProducts } from "../../apis/vendor";

const useGetProducts = () => {
  return useInfiniteQuery<MessagePage<IVendorProduct>, Error>({
    queryKey: ["vendor-products"],
    queryFn: async ({ pageParam }) => {
      return await getVendorProducts({
        page: pageParam as number,
      });
    },
    getNextPageParam: (lastPage) => {
      return lastPage.data.hasNextPage
        ? lastPage.data.currentPage + 1
        : undefined;
    },
    initialPageParam: 1,
  });
};
export default useGetProducts;
