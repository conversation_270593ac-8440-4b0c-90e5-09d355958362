import { ICategory, MessagePage } from "@/src/types";
import { useInfiniteQuery } from "@tanstack/react-query";
import { getCategories } from "../../apis/vendor";

const useGetCategories = () => {
  return useInfiniteQuery<MessagePage<ICategory>, Error>({
    queryKey: ["categories"],
    queryFn: async ({ pageParam }) => {
      return await getCategories({
        page: pageParam as number,
      });
    },
    getNextPageParam: (lastPage) => {
      return lastPage.data.hasNextPage
        ? lastPage.data.currentPage + 1
        : undefined;
    },
    initialPageParam: 1,
  });
};
export default useGetCategories;
