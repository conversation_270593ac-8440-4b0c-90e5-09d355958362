// src/hooks/useAddBrandMutation.ts
import { showToast } from "@/src/_helper/toast/showToast";
import { useLanguage } from "@/src/context/LanguageContext";
import { BrandFormValues } from "@/src/types";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { isAxiosError } from "axios";
import { router } from "expo-router";
import { addNewBrand } from "../../apis/vendor";

import { AddBrandPayload } from "@/src/types";

const useAddBrandMutation = () => {
  const { t } = useLanguage();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (brandData: BrandFormValues) => {
      // Send the data in the exact same structure as the form
      const payload: AddBrandPayload = {
        name: brandData.name,
        description: brandData.description,
        logo: brandData.logo,
        email: brandData.email,
        phone: brandData.phone,
        location: {
          address: brandData.location.address,
          latitude: brandData.location.latitude,
          longitude: brandData.location.longitude,
          city: brandData.location.city,
          state: brandData.location.state,
          country: brandData.location.country,
          zipCode: brandData.location.zipCode,
        },
        category: brandData.category,
      };

      return addNewBrand(payload);
    },
    onSuccess: (data) => {
      // Invalidate and refetch brand queries
      queryClient.invalidateQueries({
        queryKey: ["vendor-brands"],
      });
      queryClient.invalidateQueries({
        queryKey: ["dashboard-analytics"],
      });
      router.back();
      // Show success toast
      showToast(
        t("backend.success"),
        t("backend.brandAddedSuccessfully"),
        "success"
      );
    },
    onError: (error: unknown) => {
      if (isAxiosError(error)) {
        const message =
          error.response?.data?.message || "backend.unexpectedError";
        showToast(t("backend.error"), t(message), "error");
      } else {
        showToast(t("backend.error"), t("backend.unexpectedError"), "error");
      }
    },
  });
};

export default useAddBrandMutation;
