// src/hooks/useAddBrandMutation.ts
import { showToast } from "@/src/_helper/toast/showToast";
import { useLanguage } from "@/src/context/LanguageContext";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { isAxiosError } from "axios";
import { router } from "expo-router";
import { deleteBrand } from "../../apis/vendor";

const useDeleteBrandMutation = () => {
  const { t } = useLanguage();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id }: { id: string }) => {
      return deleteBrand(id);
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({
        queryKey: ["vendor-brands"],
      });
      queryClient.invalidateQueries({
        queryKey: ["dashboard-analytics"],
      });
      router.back();
      // Show success toast
      showToast(
        t("backend.success"),
        t("backend.brandAddedSuccessfully"),
        "success"
      );
    },
    onError: (error: unknown) => {
      if (isAxiosError(error)) {
        const message =
          error.response?.data?.message || "backend.unexpectedError";
        showToast(t("backend.error"), t(message), "error");
      } else {
        showToast(t("backend.error"), t("backend.unexpectedError"), "error");
      }
    },
  });
};

export default useDeleteBrandMutation;
