import { useQuery } from "@tanstack/react-query";
import { getDashboardAnalytics } from "../../apis/vendor";
import { DashboardAnalyticsResponse } from "@/src/types/dashboard";

const useGetDashboardAnalytics = () => {
  return useQuery<DashboardAnalyticsResponse, Error>({
    queryKey: ["dashboard-analytics"],
    queryFn: () => getDashboardAnalytics(),
    staleTime: 1000 * 60 * 5, // 5 minutes cache
    refetchOnMount: true,
    refetchOnWindowFocus: false,
    retry: 2,
  });
};

export default useGetDashboardAnalytics;
