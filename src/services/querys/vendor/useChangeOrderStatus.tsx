// src/hooks/useAddBrandMutation.ts
import { showToast } from "@/src/_helper/toast/showToast";
import { useLanguage } from "@/src/context/LanguageContext";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { isAxiosError } from "axios";
import { updateOrderStatus } from "../../apis/vendor";

export interface EditProductPayload {
  id: string;
  name: string;
  description: string;
  category: string;
  brand: string;
  price: string;
  images: string[];
}

const useChangeOrderStatus = () => {
  const { t } = useLanguage();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, status }: { id: string; status: string }) => {
      // Send the data in the exact same structure as the form

      return updateOrderStatus(id, status);
    },
    onSuccess: (data, { id }) => {
      // Invalidate and refetch brand queries
      queryClient.invalidateQueries({
        queryKey: ["vendor-orders"],
      });
      queryClient.invalidateQueries({
        queryKey: ["vendor-order-details", id],
      });
      queryClient.invalidateQueries({
        queryKey: ["dashboard-analytics"],
      });
    },
    onError: (error: unknown) => {
      if (isAxiosError(error)) {
        const message =
          error.response?.data?.message || "backend.unexpectedError";
        showToast(t("backend.error"), t(message), "error");
      } else {
        showToast(t("backend.error"), t("backend.unexpectedError"), "error");
      }
    },
  });
};

export default useChangeOrderStatus;
