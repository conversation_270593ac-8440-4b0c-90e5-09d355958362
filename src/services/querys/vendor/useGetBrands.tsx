import { IVendorBrand, MessagePage } from "@/src/types";
import { useInfiniteQuery } from "@tanstack/react-query";
import { getVendorBrands } from "../../apis/vendor";

const useGetBrands = () => {
  return useInfiniteQuery<MessagePage<IVendorBrand>, Error>({
    queryKey: ["vendor-brands"],
    queryFn: async ({ pageParam }) => {
      return await getVendorBrands({
        page: pageParam as number,
      });
    },
    getNextPageParam: (lastPage) => {
      return lastPage.data.hasNextPage
        ? lastPage.data.currentPage + 1
        : undefined;
    },
    initialPageParam: 1,
  });
};
export default useGetBrands;
