import { useInfiniteQuery } from "@tanstack/react-query";
import { getVendorOrders } from "../../apis/vendor";

export interface IOrderProduct {
  product: {
    _id: string;
    name: string;
    price?: number;
    images?: string[];
    brand?: {
      _id: string;
      name: string;
    };
  };
  quantity: number;
}

export interface IOrder {
  _id: string;
  products: IOrderProduct[];
  client: {
    _id: string;
    name: string;
    phone: string;
    // Add other client fields you might need
  };
  brand: {
    _id: string;
    name: string;
    // Add other brand fields you might need
  };
  location: {
    _id: string;
    // Add location fields you might need
  };
  orderDate: string; // ISO date string from API
  orderStatus: "Pending" | "Accepted" | "Delivered" | "Cancelled";
  totalPrice: number;
  createdAt: string; // From timestamps
  updatedAt: string; // From timestamps
}

type MessagePage = {
  data: {
    items: IOrder[];
    totalItems: number;
    currentPage: number;
    totalPages: number;
    hasNextPage: boolean;
    hasPreviousPage: boolean;
  };
};

const useGetOrders = () => {
  return useInfiniteQuery<MessagePage, Error>({
    queryKey: ["vendor-orders"],
    queryFn: async ({ pageParam }) => {
      return await getVendorOrders({
        page: pageParam as number,
      });
    },
    getNextPageParam: (lastPage) => {
      return lastPage.data.hasNextPage
        ? lastPage.data.currentPage + 1
        : undefined;
    },
    initialPageParam: 1,
  });
};

export default useGetOrders;
