// src/hooks/useAddBrandMutation.ts
import { showToast } from "@/src/_helper/toast/showToast";
import { useLanguage } from "@/src/context/LanguageContext";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { isAxiosError } from "axios";
import { rmFromFavorites } from "../../apis/client";

const useRemoveFromWishlist = () => {
  const { t } = useLanguage();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, type }: { id: string; type: "brand" | "product" }) => {
      return rmFromFavorites(id, type);
    },
    onSuccess: (data, { id }) => {
      // Invalidate and refetch brand queries
      queryClient.invalidateQueries({
        queryKey: ["favorite-brands"],
      });
      queryClient.invalidateQueries({
        queryKey: ["favorite-products"],
      });
      queryClient.invalidateQueries({
        queryKey: ["brand-details", id],
      });
      queryClient.invalidateQueries({
        queryKey: ["product-details", id],
      });
    },
    onError: (error: unknown) => {
      if (isAxiosError(error)) {
        const message =
          error.response?.data?.message || "backend.unexpectedError";
        showToast(t("backend.error"), t(message), "error");
      } else {
        showToast(t("backend.error"), t("backend.unexpectedError"), "error");
      }
    },
  });
};

export default useRemoveFromWishlist;
