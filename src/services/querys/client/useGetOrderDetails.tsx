// src/hooks/useCategoryQuery.js
import { useQuery } from "@tanstack/react-query";
import { getOrderDetails } from "../../apis/client";

const useGetOrderDetails = (id: string) => {
  return useQuery({
    queryKey: ["order-details", id],
    queryFn: () => getOrderDetails(id),
    staleTime: 0, // so it becomes stale right away
    refetchOnMount: true,
    refetchOnWindowFocus: true,
  });
};

export default useGetOrderDetails;
