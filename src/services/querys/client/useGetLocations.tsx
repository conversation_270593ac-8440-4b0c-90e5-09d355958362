import { ILocation } from "@/src/types/location";
import { useInfiniteQuery } from "@tanstack/react-query";
import { getLocations } from "../../apis/client";

type MessagePage = {
  data: {
    items: ILocation[];
    totalItems: number;
    currentPage: number;
    totalPages: number;
    hasNextPage: boolean;
    hasPreviousPage: boolean;
  };
};
const useGetLocations = () => {
  return useInfiniteQuery<MessagePage, Error>({
    queryKey: ["locations"],
    queryFn: async ({ pageParam }) => {
      return await getLocations({
        page: pageParam as number,
      });
    },
    getNextPageParam: (lastPage) => {
      return lastPage.data.hasNextPage
        ? lastPage.data.currentPage + 1
        : undefined;
    },
    initialPageParam: 1,
  });
};
export default useGetLocations;
