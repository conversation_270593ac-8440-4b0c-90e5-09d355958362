// src/hooks/useAddBrandMutation.ts
import { showToast } from "@/src/_helper/toast/showToast";
import { useLanguage } from "@/src/context/LanguageContext";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { isAxiosError } from "axios";
import { createOrder } from "../../apis/client";

const useCreateOrderMutation = () => {
  const { t } = useLanguage();
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: ({
      products,
      brandId,
      locationId,
      totalPrice,
    }: {
      products: {
        product: string; // Just the ObjectId string
        quantity: number;
      }[];
      brandId: string;
      locationId: string;
      totalPrice: number;
    }) => {
      return createOrder(products, brandId, locationId, totalPrice);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["orders"],
      });
      showToast(t("backend.success"), t("cart.orderCreated"), "success");
    },
    onError: (error: unknown) => {
      if (isAxiosError(error)) {
        const message =
          error.response?.data?.message || "backend.unexpectedError";
        showToast(t("backend.error"), t(message), "error");
      } else {
        showToast(t("backend.error"), t("backend.unexpectedError"), "error");
      }
    },
  });
};

export default useCreateOrderMutation;
