import { ISearchResult, MessagePage } from "@/src/types";
import { useInfiniteQuery } from "@tanstack/react-query";
import { smartSearch } from "../../apis/client";

const useSearch = (type: string, q: string) => {
  return useInfiniteQuery<MessagePage<ISearchResult>, Error>({
    queryKey: ["search", type, q],
    queryFn: async ({ pageParam }) => {
      return await smartSearch(type, q, pageParam as number);
    },
    getNextPageParam: (lastPage) => {
      return lastPage.data.hasNextPage
        ? lastPage.data.currentPage + 1
        : undefined;
    },
    initialPageParam: 1,
    enabled: !!q.trim(), // Only run query when there's a search term
  });
};
export default useSearch;
