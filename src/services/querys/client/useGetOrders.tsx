import { IOrder, MessagePage } from "@/src/types";
import { useInfiniteQuery } from "@tanstack/react-query";
import { getOrders } from "../../apis/client";

const useGetOrders = () => {
  return useInfiniteQuery<MessagePage<IOrder>, Error>({
    queryKey: ["orders"],
    queryFn: async ({ pageParam }) => {
      return await getOrders({
        page: pageParam as number,
      });
    },
    getNextPageParam: (lastPage) => {
      return lastPage.data.hasNextPage
        ? lastPage.data.currentPage + 1
        : undefined;
    },
    initialPageParam: 1,
  });
};
export default useGetOrders;
