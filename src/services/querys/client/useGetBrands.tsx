import { IClientBrand, MessagePage } from "@/src/types";
import { useInfiniteQuery } from "@tanstack/react-query";
import { getBrands } from "../../apis/client";

const useGetBrands = () => {
  return useInfiniteQuery<MessagePage<IClientBrand>, Error>({
    queryKey: ["brands"],
    queryFn: async ({ pageParam }) => {
      return await getBrands({
        page: pageParam as number,
      });
    },
    getNextPageParam: (lastPage) => {
      return lastPage.data.hasNextPage
        ? lastPage.data.currentPage + 1
        : undefined;
    },
    initialPageParam: 1,
  });
};
export default useGetBrands;
