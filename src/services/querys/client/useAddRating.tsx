// src/hooks/useAddBrandMutation.ts
import { showToast } from "@/src/_helper/toast/showToast";
import { useLanguage } from "@/src/context/LanguageContext";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { isAxiosError } from "axios";
import { addRating } from "../../apis/client";

const useAddRating = () => {
  const { t } = useLanguage();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({
      id,
      rating,
      comment,
      productId,
    }: {
      id: string;
      rating: number;
      comment: string;
      productId?: string;
    }) => {
      return addRating(id, rating, comment, productId);
    },
    onSuccess: (_, { id, productId }) => {
      // Invalidate and refetch brand queries
      queryClient.invalidateQueries({
        queryKey: ["brand-details", id],
      });

      // If it's a product review, also invalidate product queries
      if (productId) {
        queryClient.invalidateQueries({
          queryKey: ["product-details", productId],
        });
      }
      // Show success toast
    },
    onError: (error: unknown) => {
      if (isAxiosError(error)) {
        const message =
          error.response?.data?.message || "backend.unexpectedError";
        showToast(t("backend.error"), t(message), "error");
      } else {
        showToast(t("backend.error"), t("backend.unexpectedError"), "error");
      }
    },
  });
};

export default useAddRating;
