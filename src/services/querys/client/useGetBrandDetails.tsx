// src/hooks/useCategoryQuery.js
import { useQuery } from "@tanstack/react-query";
import { getBrandDetails } from "../../apis/client";

const useGetBrandDetails = (id: string) => {
  return useQuery({
    queryKey: ["brand-details", id],
    queryFn: () => getBrandDetails(id),
    staleTime: 0, // so it becomes stale right away
    refetchOnMount: true,
    refetchOnWindowFocus: true,
  });
};

export default useGetBrandDetails;
