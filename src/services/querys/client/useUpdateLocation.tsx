import { showToast } from "@/src/_helper/toast/showToast";
import { useLanguage } from "@/src/context/LanguageContext";
import { ILocation } from "@/src/types/location";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { isAxiosError } from "axios";
import { updateLocation } from "../../apis/client";

const useUpdateLocationMutation = () => {
  const { t } = useLanguage();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, location }: { id: string; location: ILocation }) => {
      return updateLocation(id, location);
    },
    onSuccess: () => {
      // Invalidate and refetch brand queries
      queryClient.invalidateQueries({
        queryKey: ["locations"],
      });
    },
    onError: (error: unknown) => {
      if (isAxiosError(error)) {
        const message =
          error.response?.data?.message || "backend.unexpectedError";
        showToast(t("backend.error"), t(message), "error");
      } else {
        showToast(t("backend.error"), t("backend.unexpectedError"), "error");
      }
    },
  });
};

export default useUpdateLocationMutation;
