import { IClientProduct, MessagePage } from "@/src/types";
import { useInfiniteQuery } from "@tanstack/react-query";
import { getProducts } from "../../apis/client";

const useGetProducts = () => {
  return useInfiniteQuery<MessagePage<IClientProduct>, Error>({
    queryKey: ["products"],
    queryFn: async ({ pageParam }) => {
      return await getProducts({
        page: pageParam as number,
      });
    },
    getNextPageParam: (lastPage) => {
      return lastPage.data.hasNextPage
        ? lastPage.data.currentPage + 1
        : undefined;
    },
    initialPageParam: 1,
  });
};
export default useGetProducts;
