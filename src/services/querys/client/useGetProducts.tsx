import { IClientProduct, MessagePage } from "@/src/types";
import { useInfiniteQuery } from "@tanstack/react-query";
import { getProducts } from "../../apis/client";

const useGetProducts = (categoryId?: string) => {
  return useInfiniteQuery<MessagePage<IClientProduct>, Error>({
    queryKey: ["products", categoryId],
    queryFn: async ({ pageParam }) => {
      return await getProducts({
        page: pageParam as number,
        categoryId,
      });
    },
    getNextPageParam: (lastPage) => {
      return lastPage.data.hasNextPage
        ? lastPage.data.currentPage + 1
        : undefined;
    },
    initialPageParam: 1,
  });
};
export default useGetProducts;
