// src/hooks/useCategoryQuery.js
import { useQuery } from "@tanstack/react-query";
import { getProductDetails } from "../../apis/client";

const useGetProductDetails = (id: string) => {
  return useQuery({
    queryKey: ["product-details", id],
    queryFn: () => getProductDetails(id),
    staleTime: 0, // so it becomes stale right away
    refetchOnMount: true,
    refetchOnWindowFocus: true,
  });
};

export default useGetProductDetails;
