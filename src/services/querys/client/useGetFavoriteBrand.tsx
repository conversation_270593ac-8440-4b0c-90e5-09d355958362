import { IClientBrand, MessagePage } from "@/src/types";
import { useInfiniteQuery } from "@tanstack/react-query";
import { getFavoriteBrands } from "../../apis/client";

const useGetFavoriteBrand = () => {
  return useInfiniteQuery<MessagePage<IClientBrand>, Error>({
    queryKey: ["favorite-brands"],
    queryFn: async ({ pageParam }) => {
      return await getFavoriteBrands({
        page: pageParam as number,
      });
    },
    getNextPageParam: (lastPage) => {
      return lastPage.data.hasNextPage
        ? lastPage.data.currentPage + 1
        : undefined;
    },
    initialPageParam: 1,
  });
};
export default useGetFavoriteBrand;
