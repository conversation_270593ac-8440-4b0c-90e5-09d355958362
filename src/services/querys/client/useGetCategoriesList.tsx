// src/hooks/useCategoryQuery.js
import { useQuery } from "@tanstack/react-query";
import { getCategoriesList } from "../../apis/client";

const useGetCategoriesList = () => {
  return useQuery({
    queryKey: ["categories-list"],
    queryFn: () => getCategoriesList(),
    staleTime: 0, // so it becomes stale right away
    refetchOnMount: true,
    refetchOnWindowFocus: true,
  });
};

export default useGetCategoriesList;
