// src/hooks/useAddBrandMutation.ts
import { showToast } from "@/src/_helper/toast/showToast";
import { useLanguage } from "@/src/context/LanguageContext";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { isAxiosError } from "axios";
import { deleteOrder } from "../../apis/client";

const useDeleteOrderMutation = () => {
  const { t } = useLanguage();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id }: { id: string }) => {
      return deleteOrder(id);
    },
    onSuccess: (data, { id }) => {
      // Invalidate and refetch brand queries
      queryClient.invalidateQueries({
        queryKey: ["orders"],
      });
      queryClient.invalidateQueries({
        queryKey: ["order-details", id],
      });
    },
    onError: (error: unknown) => {
      if (isAxiosError(error)) {
        const message =
          error.response?.data?.message || "backend.unexpectedError";
        showToast(t("backend.error"), t(message), "error");
      } else {
        showToast(t("backend.error"), t("backend.unexpectedError"), "error");
      }
    },
  });
};

export default useDeleteOrderMutation;
