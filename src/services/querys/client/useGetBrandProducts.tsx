import { IClientProduct, MessagePage } from "@/src/types";
import { useInfiniteQuery } from "@tanstack/react-query";
import { getBrandProducts } from "../../apis/client";

const useGetBrandProducts = (id: string) => {
  return useInfiniteQuery<MessagePage<IClientProduct>, Error>({
    queryKey: ["brand-products", id],
    queryFn: async ({ pageParam }) => {
      return await getBrandProducts({
        page: pageParam as number,
        id,
      });
    },
    getNextPageParam: (lastPage) => {
      return lastPage.data.hasNextPage
        ? lastPage.data.currentPage + 1
        : undefined;
    },
    initialPageParam: 1,
  });
};
export default useGetBrandProducts;
