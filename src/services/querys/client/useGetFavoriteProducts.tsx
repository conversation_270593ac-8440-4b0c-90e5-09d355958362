import { useInfiniteQuery } from "@tanstack/react-query";
import { getFavoriteProducts } from "../../apis/client";

export interface IProduct {
  _id: string;
  name: string;
  images: string[];
  brand: {
    _id: string;
    name: string;
  };
}
type MessagePage = {
  data: {
    items: IProduct[];
    totalItems: number;
    currentPage: number;
    totalPages: number;
    hasNextPage: boolean;
    hasPreviousPage: boolean;
  };
};
const useGetFavoriteProducts = () => {
  return useInfiniteQuery<MessagePage, Error>({
    queryKey: ["favorite-products"],
    queryFn: async ({ pageParam }) => {
      return await getFavoriteProducts({
        page: pageParam as number,
      });
    },
    getNextPageParam: (lastPage) => {
      return lastPage.data.hasNextPage
        ? lastPage.data.currentPage + 1
        : undefined;
    },
    initialPageParam: 1,
  });
};
export default useGetFavoriteProducts;
