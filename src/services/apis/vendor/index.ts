import {
  AddBrandPayload,
  AddProductPayload,
  EditProductPayload,
} from "@/src/types";
import { client } from "../../api";

export const getVendorBrands = async ({ page }: { page: number }) => {
  const response = await client.get(`/vendor/get-brands?page=${page}`);
  return response.data;
};
export const addNewBrand = async (brandData: AddBrandPayload) => {
  const formData = new FormData();

  // Add all text fields to FormData
  formData.append("name", brandData.name);
  formData.append("description", brandData.description || "");
  formData.append("email", brandData.email || "");
  formData.append("phone", brandData.phone || "");
  formData.append("category", brandData.category);

  // Add location data as nested fields
  formData.append("location[address]", brandData.location.address);
  formData.append("location[city]", brandData.location.city);
  formData.append("location[state]", brandData.location.state);
  formData.append("location[country]", brandData.location.country);
  formData.append("location[zipCode]", brandData.location.zipCode);

  // Add coordinates if available
  if (brandData.location.latitude) {
    formData.append(
      "location[latitude]",
      brandData.location.latitude.toString()
    );
  }
  if (brandData.location.longitude) {
    formData.append(
      "location[longitude]",
      brandData.location.longitude.toString()
    );
  }

  // Handle logo file upload
  if (brandData.logo) {
    // If logo is a file URI (from image picker), convert it to a file object
    const logoFile = {
      uri: brandData.logo,
      type: "image/jpeg", // or detect the actual type
      name: `brand_logo_${Date.now()}.jpg`,
    };

    // For React Native, append the file object directly
    formData.append("logo", logoFile as any);
  }

  const response = await client.post(`/vendor/add-brand`, formData, {
    headers: {
      "Content-Type": "multipart/form-data",
    },
  });

  return response.data;
};
export const getBrandDetail = async (id: string) => {
  const response = await client.get(`/vendor/get-brand/${id}`);
  return response.data;
};
export const deleteBrand = async (id: string) => {
  const response = await client.delete(`/vendor/delete-brand/${id}`);
  return response.data;
};
export const deleteProduct = async (id: string) => {
  const response = await client.delete(`/vendor/delete-product/${id}`);
  return response.data;
};

export const getDashboardAnalytics = async () => {
  const response = await client.get(`/vendor/dashboard-analytics`);
  return response.data;
};
export const editBrand = async (id: string, brandData: AddBrandPayload) => {
  const formData = new FormData();

  // Add all text fields to FormData
  formData.append("name", brandData.name);
  formData.append("description", brandData.description || "");
  formData.append("email", brandData.email || "");
  formData.append("phone", brandData.phone || "");
  formData.append("category", brandData.category);

  // Add location data as nested fields
  formData.append("location[address]", brandData.location.address);
  formData.append("location[city]", brandData.location.city);
  formData.append("location[state]", brandData.location.state);
  formData.append("location[country]", brandData.location.country);
  formData.append("location[zipCode]", brandData.location.zipCode);

  // Add coordinates if available
  if (brandData.location.latitude) {
    formData.append(
      "location[latitude]",
      brandData.location.latitude.toString()
    );
  }
  if (brandData.location.longitude) {
    formData.append(
      "location[longitude]",
      brandData.location.longitude.toString()
    );
  }

  // Handle logo file upload
  if (brandData.logo) {
    // If logo is a file URI (from image picker), convert it to a file object
    const logoFile = {
      uri: brandData.logo,
      type: "image/jpeg", // or detect the actual type
      name: `brand_logo_${Date.now()}.jpg`,
    };

    // For React Native, append the file object directly
    formData.append("logo", logoFile as any);
  }

  const response = await client.post(`/vendor/edit-brand/${id}`, formData, {
    headers: {
      "Content-Type": "multipart/form-data",
    },
  });

  return response.data;
};
export const getVendorProducts = async ({ page }: { page: number }) => {
  const response = await client.get(`/vendor/get-products?page=${page}`);
  return response.data;
};
export const getVendorOrders = async ({ page }: { page: number }) => {
  const response = await client.get(`/vendor/get-orders?page=${page}`);
  return response.data;
};
export const getCategories = async ({ page }: { page: number }) => {
  const response = await client.get(`/vendor/get-categories?page=${page}`);
  return response.data;
};
export const addNewProduct = async (productData: AddProductPayload) => {
  const formData = new FormData();

  // Add all text fields to FormData
  formData.append("name", productData.name);
  formData.append("description", productData.description || "");
  formData.append("category", productData.category);
  formData.append("brand", productData.brand);
  formData.append("price", productData.price);
  // Handle logo file upload
  if (productData.images.length > 0) {
    // Correct field name: "images" — no square brackets
    for (let i = 0; i < productData.images.length; i++) {
      const imageFile = {
        uri: productData.images[i],
        type: "image/jpeg",
        name: `product_image_${Date.now()}_${i}.jpg`,
      };
      formData.append("images", imageFile as any); // ✅ This matches Multer config
    }
  }

  const response = await client.post(`/vendor/add-product`, formData, {
    headers: {
      "Content-Type": "multipart/form-data",
    },
  });

  return response.data;
};

export const editProduct = async (productData: EditProductPayload) => {
  const formData = new FormData();

  // Add all text fields to FormData
  formData.append("name", productData.name);
  formData.append("description", productData.description || "");
  formData.append("category", productData.category);
  formData.append("brand", productData.brand);
  formData.append("price", productData.price);
  // Handle logo file upload
  if (productData.images.length > 0) {
    // Correct field name: "images" — no square brackets
    for (let i = 0; i < productData.images.length; i++) {
      const imageFile = {
        uri: productData.images[i],
        type: "image/jpeg",
        name: `product_image_${Date.now()}_${i}.jpg`,
      };
      formData.append("images", imageFile as any); // ✅ This matches Multer config
    }
  }

  const response = await client.post(
    `/vendor/edit-product/${productData.id}`,
    formData,
    {
      headers: {
        "Content-Type": "multipart/form-data",
      },
    }
  );

  return response.data;
};
export const getProductDetails = async (id: string) => {
  const response = await client.get(`/vendor/get-product/${id}`);
  return response.data;
};
export const getOrderDetails = async (id: string) => {
  const response = await client.get(`/vendor/get-order/${id}`);
  return response.data;
};
export const updateOrderStatus = async (id: string, status: string) => {
  const response = await client.post(`/vendor/update-order-status/${id}`, {
    status,
  });
  return response.data;
};
export const getNotifications = async ({ page }: { page: number }) => {
  const response = await client.get(`/vendor/get-notifications?page=${page}`);
  return response.data;
};
