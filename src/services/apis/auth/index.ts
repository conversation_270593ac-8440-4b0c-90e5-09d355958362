import { auth } from "../../api";
export const login = async (email: string, password: string, type: string) => {
  const response = await auth.post("/login", { email, password, type });

  return response.data;
};

export const signup = async (
  fullName: string,
  email: string,
  password: string,
  type: "client" | "vendor"
) => {
  const response = await auth.post("/register", {
    name: fullName,
    email,
    password,
    type,
  });
  return response.data;
};

export const forgetPassword = async (
  email: string,
  type: "client" | "vendor"
) => {
  const response = await auth.post("/forget-password", {
    email,
    type,
  });
  return response.data;
};
export const verifyEmailOTP = async (
  email: string,
  otp: string,
  type: string,
  userType: "client" | "vendor"
) => {
  const response = await auth.post("/validate", {
    email,
    otp,
    type,
    userType,
  });
  return response.data;
};
export const resendEmailOTP = async (
  email: string,
  type: string,
  userType: "client" | "vendor"
) => {
  const response = await auth.post("/resend-email-otp", {
    email,
    type,
    userType,
  });
  return response.data;
};
export const resetPassword = async (
  email: string,
  password: string,
  type: "client" | "vendor",
  otp: string
) => {
  const response = await auth.post("/reset-password", {
    email,
    newPassword: password,
    type,
    otp,
  });
  return response.data;
};
