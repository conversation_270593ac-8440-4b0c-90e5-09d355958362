import { ILocation } from "@/src/types/location";
import { client } from "../../api";

export const getCategoriesList = async () => {
  const response = await client.get(`/profile/get-categories-list`);
  return response.data;
};

export const getBrands = async ({ page }: { page: number }) => {
  const response = await client.get(`/client/get-brands?page=${page}`);
  return response.data;
};
export const getBrandDetails = async (id: string) => {
  const response = await client.get(`/client/get-brand/${id}`);
  return response.data;
};
export const getProductDetails = async (id: string) => {
  const response = await client.get(`/client/get-product/${id}`);
  return response.data;
};
export const getProducts = async ({
  page,
  categoryId,
}: {
  page: number;
  categoryId?: string;
}) => {
  const params = new URLSearchParams({ page: page.toString() });
  if (categoryId) {
    params.append("category", categoryId);
  }
  const response = await client.get(
    `/client/get-products?${params.toString()}`
  );
  return response.data;
};
export const getBrandProducts = async ({
  page,
  id,
}: {
  page: number;
  id: string;
}) => {
  const response = await client.get(
    `/client/get-brand-products/${id}?page=${page}`
  );
  return response.data;
};

export const getFavoriteProducts = async ({ page }: { page: number }) => {
  const response = await client.get(
    `/client/get-products-wishlist?page=${page}`
  );
  return response.data;
};
export const getFavoriteBrands = async ({ page }: { page: number }) => {
  const response = await client.get(`/client/get-brands-wishlist?page=${page}`);
  return response.data;
};
export const addToFavorites = async (itemId: string, type: string) => {
  const response = await client.post(`/client/add-to-wishlist`, {
    itemId,
    type,
  });
  return response.data;
};

export const rmFromFavorites = async (itemId: string, type: string) => {
  const response = await client.post(`/client/remove-from-wishlist`, {
    itemId,
    type,
  });
  return response.data;
};
export const addRating = async (
  brandId: string,
  rating: number,
  comment: string,
  productId?: string
) => {
  const response = await client.post(`/client/add-review`, {
    brandId,
    rating,
    comment,
    ...(productId && { productId }),
  });
  return response.data;
};
export const addLocation = async (location: ILocation) => {
  const response = await client.post(`/client/add-location`, {
    ...location,
  });
  return response.data;
};

export const getLocations = async ({ page }: { page: number }) => {
  const response = await client.get(`/client/get-locations?page=${page}`);
  return response.data;
};
export const deleteLocation = async (id: string) => {
  const response = await client.delete(`/client/remove-location/${id}`);
  return response.data;
};
export const updateLocation = async (id: string, location: ILocation) => {
  const response = await client.put(`/client/update-location/${id}`, {
    ...location,
  });
  return response.data;
};
export const smartSearch = async (
  type: string,
  q: string,
  page: number = 1
) => {
  const response = await client.get(
    `/client/smart-search?type=${type}&q=${q}&page=${page}`
  );
  return response.data;
};

export const createOrder = async (
  products: {
    product: string; // Just the ObjectId string
    quantity: number;
  }[],
  brandId: string,
  locationId: string,
  totalPrice: number
) => {
  const response = await client.post(`/client/create-order`, {
    products,
    brandId,
    locationId,
    totalPrice,
  });
  return response.data;
};

export const deleteOrder = async (id: string) => {
  const response = await client.delete(`/client/delete-order/${id}`);
  return response.data;
};
export const getOrders = async ({ page }: { page: number }) => {
  const response = await client.get(`/client/get-orders?page=${page}`);
  return response.data;
};
export const getOrderDetails = async (id: string) => {
  const response = await client.get(`/client/get-order/${id}`);
  return response.data;
};

export const getNotifications = async ({ page }: { page: number }) => {
  const response = await client.get(`/client/get-notifications?page=${page}`);
  return response.data;
};
export const getFeatured = async () => {
  const response = await client.get(`/client/get-featured`);
  return response.data;
};
