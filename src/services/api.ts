import axios, { AxiosInstance } from "axios";

const auth: AxiosInstance = axios.create({
  baseURL: "http://192.168.145.35:86/auth",
  headers: { "Content-Type": "application/json" },
  timeout: 10000, // 10 second timeout
});
const client: AxiosInstance = axios.create({
  baseURL: "http://192.168.145.35:86/locasa",
  headers: {
    "Content-Type": "application/json",
    //   "Cache-Control": "no-cache"
  },
  timeout: 10000,
});

let requestInterceptorIdClient: number | null = null;
let responseInterceptorIdClient: number | null = null;

export const addInterceptors = (accessToken: string) => {
  // Clear existing interceptors first to prevent duplicates
  removeInterceptors();

  // Add interceptors to client instance
  requestInterceptorIdClient = client.interceptors.request.use((config) => {
    config.headers.Authorization = `Bearer ${accessToken}`;
    return config;
  });
  responseInterceptorIdClient = client.interceptors.response.use(
    (response) => response,
    (error) => {
      if (error.code === "ECONNABORTED") {
        return Promise.reject(
          new Error("Request timeout - server might be down")
        );
      }
      return Promise.reject(error);
    }
  );
};

export const removeInterceptors = () => {
  // Remove client interceptors
  if (requestInterceptorIdClient !== null) {
    client.interceptors.request.eject(requestInterceptorIdClient);
    requestInterceptorIdClient = null;
  }
  if (responseInterceptorIdClient !== null) {
    client.interceptors.response.eject(responseInterceptorIdClient);
    responseInterceptorIdClient = null;
  }
};

export { auth, client };
