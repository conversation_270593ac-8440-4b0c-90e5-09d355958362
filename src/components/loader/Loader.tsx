import { Colors } from "@/src/constants/Colors";
import { useTheme } from "@/src/context/ThemeContext";
import React, { useEffect, useRef } from "react";
import { Animated, Easing, StyleSheet, View } from "react-native";
import { ThemedView } from "../ui/ThemedView";

export interface DefaultButtonColorProps {
  color?: string;
  size?: number;
}

const Loader: React.FC<DefaultButtonColorProps> = ({ color, size = 50 }) => {
  const bounce1 = useRef(new Animated.Value(0)).current;
  const bounce2 = useRef(new Animated.Value(0)).current;
  const bounce3 = useRef(new Animated.Value(0)).current;
  const { currentTheme } = useTheme();
  const theme = currentTheme ?? "dark";
  const loaderColor = color || Colors[theme].primary;

  useEffect(() => {
    const createBounceAnimation = (
      animValue: Animated.Value,
      delay: number
    ) => {
      return Animated.loop(
        Animated.sequence([
          Animated.delay(delay),
          Animated.timing(animValue, {
            toValue: 1,
            duration: 600,
            easing: Easing.inOut(Easing.ease),
            useNativeDriver: true,
          }),
          Animated.timing(animValue, {
            toValue: 0,
            duration: 600,
            easing: Easing.inOut(Easing.ease),
            useNativeDriver: true,
          }),
        ])
      );
    };

    Animated.parallel([
      createBounceAnimation(bounce1, 0),
      createBounceAnimation(bounce2, 200),
      createBounceAnimation(bounce3, 400),
    ]).start();
  }, [bounce1, bounce2, bounce3]);

  const dotSize = size * 0.25;

  return (
    <ThemedView style={styles.container}>
      <View style={styles.dotsContainer}>
        {[bounce1, bounce2, bounce3].map((bounce, index) => (
          <Animated.View
            key={index}
            style={[
              styles.dot,
              {
                width: dotSize,
                height: dotSize,
                backgroundColor: loaderColor,
                transform: [
                  {
                    translateY: bounce.interpolate({
                      inputRange: [0, 1],
                      outputRange: [0, -size * 0.3],
                    }),
                  },
                ],
              },
            ]}
          />
        ))}
      </View>
    </ThemedView>
  );
};

const styles = StyleSheet.create({
  container: {
    height: "100%",
    width: "100%",
    justifyContent: "center",
    alignItems: "center",
  },
  dotsContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    width: 60,
  },
  dot: {
    borderRadius: 10,
    marginHorizontal: 2,
  },
});

export default Loader;
