import { scale } from "@/src/_helper/Scaler";
import { Colors } from "@/src/constants/Colors";
import { useTheme } from "@/src/context/ThemeContext";
import React from "react";
import { StyleSheet, View } from "react-native";
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withRepeat,
  withTiming,
} from "react-native-reanimated";

interface SkeletonProductCardProps {
  count?: number;
}

const SkeletonProductCard: React.FC<SkeletonProductCardProps> = ({
  count = 1,
}) => {
  const { currentTheme } = useTheme();
  const pulse = useSharedValue(0.5);

  React.useEffect(() => {
    pulse.value = withRepeat(
      withTiming(1, { duration: 700 }),
      -1,
      true // reverse animation
    );
  }, []);

  const animatedStyle = useAnimatedStyle(() => ({
    opacity: pulse.value,
  }));

  const background = Colors[currentTheme ?? "dark"].border;

  return (
    <>
      {Array(count)
        .fill(null)
        .map((_, idx) => (
          <View key={idx} style={styles.container}>
            <View
              style={[
                styles.imageContainer,
                { backgroundColor: Colors[currentTheme ?? "dark"].border },
              ]}
            >
              <Animated.View
                style={[
                  styles.imagePlaceholder,
                  { backgroundColor: background },
                  animatedStyle,
                ]}
              />
            </View>

            <View style={styles.textContainer}>
              <Animated.View
                style={[
                  styles.titlePlaceholder,
                  { backgroundColor: background },
                  animatedStyle,
                ]}
              />

              <Animated.View
                style={[
                  styles.subtitlePlaceholder,
                  { backgroundColor: background },
                  animatedStyle,
                ]}
              />

              <Animated.View
                style={[
                  styles.buttonPlaceholder,
                  { backgroundColor: background },
                  animatedStyle,
                ]}
              />
            </View>
          </View>
        ))}
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    width: scale(160),
    borderRadius: scale(16),
    paddingBottom: scale(10),
    marginRight: scale(15),
  },
  imageContainer: {
    justifyContent: "center",
    alignItems: "center",
    borderRadius: scale(13),
  },
  imagePlaceholder: {
    width: scale(140),
    height: scale(140),
    borderRadius: scale(12),
  },
  textContainer: {
    marginTop: scale(8),
    paddingHorizontal: scale(7),
  },
  titlePlaceholder: {
    height: scale(18),
    width: scale(120),
    borderRadius: scale(4),
    marginBottom: scale(4),
  },
  subtitlePlaceholder: {
    height: scale(16),
    width: scale(100),
    borderRadius: scale(4),
    marginBottom: scale(8),
  },
  buttonPlaceholder: {
    height: scale(26),
    width: scale(60),
    borderRadius: scale(20),
    alignSelf: "flex-start",
  },
});

export default SkeletonProductCard;
