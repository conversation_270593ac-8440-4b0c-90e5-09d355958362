import { scale } from "@/src/_helper/Scaler";
import { Colors } from "@/src/constants/Colors";
import { useTheme } from "@/src/context/ThemeContext";
import React, { useEffect } from "react";
import { StyleSheet, View } from "react-native";
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withRepeat,
  withTiming,
} from "react-native-reanimated";

const SkeletonProductDetails = () => {
  const { currentTheme } = useTheme();
  const theme = currentTheme ?? "dark";
  const pulse = useSharedValue(0.5);

  useEffect(() => {
    pulse.value = withRepeat(withTiming(1, { duration: 700 }), -1, true);
  }, []);

  const animatedStyle = useAnimatedStyle(() => ({
    opacity: pulse.value,
  }));

  const background = Colors[theme].border;

  return (
    <View style={styles.container}>
      {/* Image Carousel */}
      <View style={styles.imageCarouselContainer}>
        <View style={styles.flatListContent}>
          <Animated.View
            style={[
              styles.imageContainer,
              { backgroundColor: background },
              animatedStyle,
            ]}
          />
          <Animated.View
            style={[
              styles.imageContainer,
              { backgroundColor: background },
              animatedStyle,
            ]}
          />
        </View>
      </View>

      {/* Product Info */}
      <View style={styles.productInfo}>
        {/* Product Name and Heart Button Container */}
        <View style={styles.productNameContainer}>
          <Animated.View
            style={[
              styles.productName,
              { backgroundColor: background },
              animatedStyle,
            ]}
          />
          <Animated.View
            style={[
              styles.heartButton,
              { backgroundColor: background },
              animatedStyle,
            ]}
          />
        </View>

        {/* Product Description */}
        <Animated.View
          style={[
            styles.productDescription,
            { backgroundColor: background },
            animatedStyle,
          ]}
        />
        <Animated.View
          style={[
            styles.productDescriptionLine2,
            { backgroundColor: background },
            animatedStyle,
          ]}
        />

        {/* Product Price */}
        <Animated.View
          style={[
            styles.productPrice,
            { backgroundColor: background },
            animatedStyle,
          ]}
        />
      </View>

      {/* Bottom Action Button */}
      <View style={styles.bottomActions}>
        <Animated.View
          style={[
            styles.actionButton,
            { backgroundColor: background },
            animatedStyle,
          ]}
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  imageCarouselContainer: {
    marginBottom: scale(20),
    height: scale(240),
  },
  flatListContent: {
    alignItems: "center",
    flexDirection: "row",
  },
  imageContainer: {
    width: scale(240),
    height: scale(240),
    borderRadius: scale(15),
    marginHorizontal: scale(15),
    overflow: "hidden",
    justifyContent: "center",
    alignItems: "center",
  },
  productInfo: {
    marginBottom: scale(15),
  },
  productNameContainer: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    marginBottom: scale(10),
  },
  productName: {
    height: scale(24),
    width: "70%",
    borderRadius: scale(12),
  },
  heartButton: {
    width: scale(34),
    height: scale(34),
    borderRadius: scale(17),
  },
  productDescription: {
    height: scale(16),
    width: "100%",
    borderRadius: scale(8),
    marginBottom: scale(8),
  },
  productDescriptionLine2: {
    height: scale(16),
    width: "85%",
    borderRadius: scale(8),
    marginBottom: scale(15),
  },
  productPrice: {
    height: scale(20),
    width: scale(100),
    borderRadius: scale(10),
  },
  bottomActions: {
    position: "absolute",
    bottom: 0,
    left: 0,
    right: 0,
    paddingHorizontal: scale(20),
    paddingBottom: scale(20),
  },
  actionButton: {
    height: scale(50),
    borderRadius: scale(25),
    width: "100%",
  },
});

export default SkeletonProductDetails;
