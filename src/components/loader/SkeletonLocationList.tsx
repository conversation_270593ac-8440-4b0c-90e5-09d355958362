import { scale } from "@/src/_helper/Scaler";
import { ThemedView } from "@/src/components/ui/ThemedView";
import { Colors } from "@/src/constants/Colors";
import { useTheme } from "@/src/context/ThemeContext";
import React from "react";
import { StyleSheet, View } from "react-native";
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withRepeat,
  withTiming,
} from "react-native-reanimated";

interface SkeletonLocationListProps {
  count?: number;
}

const SkeletonLocationList: React.FC<SkeletonLocationListProps> = ({
  count = 1,
}) => {
  const { currentTheme } = useTheme();
  const pulse = useSharedValue(0.5);

  React.useEffect(() => {
    pulse.value = withRepeat(
      withTiming(1, { duration: 700 }),
      -1,
      true // reverse animation
    );
  }, []);

  const animatedStyle = useAnimatedStyle(() => ({
    opacity: pulse.value,
  }));

  const background = Colors[currentTheme ?? "dark"].border;

  return (
    <>
      {Array(count)
        .fill(null)
        .map((_, idx) => (
          <ThemedView
            key={idx}
            style={[
              styles.card,
              {
                backgroundColor: Colors[currentTheme ?? "dark"].thirdary,
                shadowColor: Colors[currentTheme ?? "dark"].text,
              },
            ]}
          >
            <View style={styles.content}>
              {/* Location Icon Placeholder */}
              <Animated.View
                style={[
                  styles.iconPlaceholder,
                  { backgroundColor: background },
                  animatedStyle,
                ]}
              />

              {/* Content Section */}
              <View style={styles.info}>
                {/* Header section with address and type badge */}
                <View style={styles.headerSection}>
                  <Animated.View
                    style={[
                      styles.addressLine,
                      { backgroundColor: background },
                      animatedStyle,
                    ]}
                  />
                  <Animated.View
                    style={[
                      styles.typeBadge,
                      { backgroundColor: background },
                      animatedStyle,
                    ]}
                  />
                </View>

                {/* Details section */}
                <View style={styles.detailsSection}>
                  {/* City, State line with icon space */}
                  <View style={styles.detailRow}>
                    <Animated.View
                      style={[
                        styles.iconPlaceholderSmall,
                        { backgroundColor: background },
                        animatedStyle,
                      ]}
                    />
                    <Animated.View
                      style={[
                        styles.textLineMedium,
                        { backgroundColor: background },
                        animatedStyle,
                      ]}
                    />
                  </View>

                  {/* Country line with icon space */}
                  <View style={styles.detailRow}>
                    <Animated.View
                      style={[
                        styles.iconPlaceholderSmall,
                        { backgroundColor: background },
                        animatedStyle,
                      ]}
                    />
                    <Animated.View
                      style={[
                        styles.textLineShort,
                        { backgroundColor: background },
                        animatedStyle,
                      ]}
                    />
                  </View>

                  {/* Zip code line with icon space */}
                  <View style={styles.detailRow}>
                    <Animated.View
                      style={[
                        styles.iconPlaceholderSmall,
                        { backgroundColor: background },
                        animatedStyle,
                      ]}
                    />
                    <Animated.View
                      style={[
                        styles.textLineVeryShort,
                        { backgroundColor: background },
                        animatedStyle,
                      ]}
                    />
                  </View>
                </View>
              </View>

              {/* Action Buttons Placeholder */}
              <View style={styles.actionsContainer}>
                <Animated.View
                  style={[
                    styles.actionButtonPlaceholder,
                    { backgroundColor: background },
                    animatedStyle,
                  ]}
                />
                <Animated.View
                  style={[
                    styles.actionButtonPlaceholder,
                    { backgroundColor: background },
                    animatedStyle,
                  ]}
                />
              </View>
            </View>
          </ThemedView>
        ))}
    </>
  );
};

const styles = StyleSheet.create({
  card: {
    borderRadius: scale(16),
    padding: scale(16),
    marginBottom: scale(12),
    shadowOffset: {
      width: 0,
      height: scale(2),
    },
    shadowOpacity: 0.05,
    shadowRadius: scale(8),
    elevation: 3, // elevation should be an integer
  },
  content: {
    flexDirection: "row",
    alignItems: "flex-start",
  },
  iconPlaceholder: {
    width: scale(50),
    height: scale(50),
    borderRadius: scale(25),
    marginRight: scale(16),
  },
  info: {
    flex: 1,
  },
  headerSection: {
    flexDirection: "row",
    alignItems: "flex-start",
    justifyContent: "space-between",
    marginBottom: scale(8),
  },
  addressLine: {
    height: scale(16),
    width: "65%",
    borderRadius: scale(4),
  },
  typeBadge: {
    height: scale(20),
    width: scale(50),
    borderRadius: scale(12),
  },
  detailsSection: {
    gap: scale(4),
  },
  detailRow: {
    flexDirection: "row",
    alignItems: "center",
  },
  iconPlaceholderSmall: {
    width: scale(16),
    height: scale(14),
    borderRadius: scale(2),
    marginRight: scale(8),
  },
  textLineMedium: {
    height: scale(12),
    width: "50%",
    borderRadius: scale(4),
  },
  textLineShort: {
    height: scale(12),
    width: "35%",
    borderRadius: scale(4),
  },
  textLineVeryShort: {
    height: scale(12),
    width: "20%",
    borderRadius: scale(4),
  },
  actionsContainer: {
    flexDirection: "column",
    gap: scale(8),
    marginLeft: scale(8),
  },
  actionButtonPlaceholder: {
    width: scale(36),
    height: scale(36),
    borderRadius: scale(18),
  },
});

export default SkeletonLocationList;
