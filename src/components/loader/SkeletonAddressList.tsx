import { scale } from "@/src/_helper/Scaler";
import { ThemedView } from "@/src/components/ui/ThemedView";
import { Colors } from "@/src/constants/Colors";
import { useTheme } from "@/src/context/ThemeContext";
import React from "react";
import { StyleSheet, View } from "react-native";
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withRepeat,
  withTiming,
} from "react-native-reanimated";

interface SkeletonAddressListProps {
  count?: number;
}

const SkeletonAddressList: React.FC<SkeletonAddressListProps> = ({
  count = 1,
}) => {
  const { currentTheme } = useTheme();
  const pulse = useSharedValue(0.5);

  React.useEffect(() => {
    pulse.value = withRepeat(
      withTiming(1, { duration: 700 }),
      -1,
      true // reverse animation
    );
  }, []);

  const animatedStyle = useAnimatedStyle(() => ({
    opacity: pulse.value,
  }));

  const background = Colors[currentTheme ?? "dark"].border;

  return (
    <>
      {Array(count)
        .fill(null)
        .map((_, idx) => (
          <ThemedView
            key={idx}
            style={[
              styles.addressItem,
              { backgroundColor: Colors[currentTheme ?? "dark"].thirdary },
            ]}
          >
            {/* Address Icon Placeholder */}
            <Animated.View
              style={[
                styles.addressIconContainer,
                { backgroundColor: background },
                animatedStyle,
              ]}
            />

            {/* Address Details Placeholder */}
            <View style={styles.addressDetails}>
              <Animated.View
                style={[
                  styles.addressTypeText,
                  { backgroundColor: background },
                  animatedStyle,
                ]}
              />
              <Animated.View
                style={[
                  styles.addressText,
                  { backgroundColor: background },
                  animatedStyle,
                ]}
              />
            </View>

            {/* Chevron Icon Placeholder */}
            <Animated.View
              style={[
                styles.chevronIcon,
                { backgroundColor: background },
                animatedStyle,
              ]}
            />
          </ThemedView>
        ))}
    </>
  );
};

const styles = StyleSheet.create({
  addressItem: {
    flexDirection: "row",
    alignItems: "center",
    paddingVertical: scale(16),
    paddingHorizontal: scale(20),
    marginBottom: scale(8),
    borderRadius: scale(12),
    overflow: "hidden",
  },
  addressIconContainer: {
    width: scale(40),
    height: scale(40),
    borderRadius: scale(20),
    marginRight: scale(12),
  },
  addressDetails: {
    flex: 1,
    justifyContent: "space-between",
  },
  addressTypeText: {
    height: scale(16),
    borderRadius: scale(8),
    marginBottom: scale(6),
    width: "40%",
  },
  addressText: {
    height: scale(14),
    borderRadius: scale(7),
    width: "85%",
  },
  chevronIcon: {
    width: scale(20),
    height: scale(20),
    borderRadius: scale(4),
    marginLeft: scale(12),
  },
});

export default SkeletonAddressList;
