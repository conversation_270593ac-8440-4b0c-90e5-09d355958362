import { scale } from "@/src/_helper/Scaler";
import { Colors } from "@/src/constants/Colors";
import { useTheme } from "@/src/context/ThemeContext";
import React from "react";
import { StyleSheet, View } from "react-native";
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withRepeat,
  withTiming,
} from "react-native-reanimated";

interface SkeletonProductsListItemProps {
  count?: number;
}

const SkeletonProductsListItem: React.FC<SkeletonProductsListItemProps> = ({
  count = 1,
}) => {
  const { currentTheme } = useTheme();
  const pulse = useSharedValue(0.5);

  React.useEffect(() => {
    pulse.value = withRepeat(
      withTiming(1, { duration: 700 }),
      -1,
      true // reverse animation
    );
  }, []);

  const animatedStyle = useAnimatedStyle(() => ({
    opacity: pulse.value,
  }));

  const background = Colors[currentTheme ?? "dark"].border;

  return (
    <>
      {Array(count)
        .fill(null)
        .map((_, idx) => (
          <View key={idx} style={styles.container}>
            <Animated.View
              style={[
                styles.imagePlaceholder,
                { backgroundColor: background },
                animatedStyle,
              ]}
            />
            <View style={styles.textContainer}>
              <Animated.View
                style={[
                  styles.titlePlaceholder,
                  { backgroundColor: background },
                  animatedStyle,
                ]}
              />
              <Animated.View
                style={[
                  styles.pricePlaceholder,
                  { backgroundColor: background },
                  animatedStyle,
                ]}
              />
            </View>
          </View>
        ))}
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: "flex-start",
    flexDirection: "column",
    gap: scale(20),
    marginBottom: scale(20),
    width: scale(160),
  },
  imagePlaceholder: {
    width: scale(160),
    height: scale(160),
    borderRadius: scale(12),
  },
  textContainer: {
    alignSelf: "flex-start",
    gap: scale(8),
  },
  titlePlaceholder: {
    height: scale(16),
    borderRadius: scale(8),
    width: scale(100),
  },
  pricePlaceholder: {
    height: scale(14),
    borderRadius: scale(7),
    width: scale(60),
  },
});

export default SkeletonProductsListItem;
