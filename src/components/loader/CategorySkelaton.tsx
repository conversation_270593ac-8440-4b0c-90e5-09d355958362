import { scale } from "@/src/_helper/Scaler";
import { Colors } from "@/src/constants/Colors";
import { useTheme } from "@/src/context/ThemeContext";
import React from "react";
import { StyleSheet, View } from "react-native";
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withRepeat,
  withTiming,
} from "react-native-reanimated";

interface SkeletonShopCategoryProps {
  count?: number;
}

const SkeletonShopCategory: React.FC<SkeletonShopCategoryProps> = ({
  count = 1,
}) => {
  const { currentTheme } = useTheme();
  const pulse = useSharedValue(0.5);

  React.useEffect(() => {
    pulse.value = withRepeat(withTiming(1, { duration: 700 }), -1, true);
  }, []);

  const animatedStyle = useAnimatedStyle(() => ({
    opacity: pulse.value,
  }));

  const background = Colors[currentTheme ?? "dark"].border;
  const secondary = Colors[currentTheme ?? "dark"].secondary;

  return (
    <>
      {Array(count)
        .fill(null)
        .map((_, idx) => (
          <View
            key={idx}
            style={[
              styles.container,
              {
                backgroundColor: Colors[currentTheme ?? "dark"].background,
                borderColor: Colors[currentTheme ?? "dark"].thirdary,
              },
            ]}
          >
            <View
              style={[styles.imageContainer, { backgroundColor: secondary }]}
            >
              <Animated.View
                style={[
                  styles.imagePlaceholder,
                  { backgroundColor: background },
                  animatedStyle,
                ]}
              />
            </View>
            <View style={styles.textContainer}>
              <Animated.View
                style={[
                  styles.textPlaceholder,
                  { backgroundColor: background },
                  animatedStyle,
                ]}
              />
            </View>
          </View>
        ))}
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    borderWidth: 1,
    borderRadius: scale(10),
    height: scale(70),
    width: scale(160),
    flexDirection: "row",
    justifyContent: "flex-start",
    alignItems: "center",
    marginBottom: scale(10),
  },
  imageContainer: {
    width: scale(40),
    height: scale(40),
    borderRadius: scale(20),
    justifyContent: "center",
    alignItems: "center",
    marginRight: scale(10),
  },
  imagePlaceholder: {
    width: scale(30),
    height: scale(30),
    borderRadius: scale(15),
  },
  textContainer: {
    flex: 1,
  },
  textPlaceholder: {
    height: scale(14),
    width: "80%",
    borderRadius: scale(7),
  },
});

export default SkeletonShopCategory;
