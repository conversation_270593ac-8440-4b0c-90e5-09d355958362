import { scale } from "@/src/_helper/Scaler";
import { Colors } from "@/src/constants/Colors";
import { useTheme } from "@/src/context/ThemeContext";
import React from "react";
import { StyleSheet, View } from "react-native";
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withRepeat,
  withTiming,
} from "react-native-reanimated";

export function ProfileHeaderSkeleton() {
  const { currentTheme } = useTheme();
  const pulse = useSharedValue(0.5);

  React.useEffect(() => {
    pulse.value = withRepeat(
      withTiming(1, { duration: 700 }),
      -1,
      true // reverse animation
    );
  }, []);

  const animatedStyle = useAnimatedStyle(() => ({
    opacity: pulse.value,
  }));

  const background = Colors[currentTheme ?? "dark"].border;

  return (
    <View
      style={{
        justifyContent: "center",
        alignItems: "center",
      }}
    >
      <View style={{ height: scale(190), width: "100%" }}>
        {/* Avatar skeleton */}
        <View style={styles.avatarWrapper}>
          <Animated.View
            style={[
              styles.avatarPlaceholder,
              { backgroundColor: background },
              animatedStyle,
            ]}
          />
        </View>

        {/* Name skeleton */}
        <Animated.View
          style={[
            styles.nameLine,
            { backgroundColor: background },
            animatedStyle,
          ]}
        />

        {/* Email skeleton */}
        <Animated.View
          style={[
            styles.emailLine,
            { backgroundColor: background },
            animatedStyle,
          ]}
        />
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  avatarWrapper: {
    marginBottom: scale(12),
    borderRadius: scale(60),
    overflow: "hidden",
    backgroundColor: "#ccc",
    width: scale(120),
    height: scale(120),
    alignSelf: "center",
  },
  avatarPlaceholder: {
    width: scale(120),
    height: scale(120),
    borderRadius: scale(60),
    justifyContent: "center",
    alignItems: "center",
  },
  nameLine: {
    marginTop: scale(3),
    height: scale(22),
    width: scale(140),
    borderRadius: scale(11),
    alignSelf: "center",
    marginBottom: scale(4),
  },
  emailLine: {
    height: scale(14),
    width: scale(180),
    borderRadius: scale(7),
    alignSelf: "center",
  },
});
