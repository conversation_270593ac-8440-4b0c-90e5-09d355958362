import { scale } from "@/src/_helper/Scaler";
import { ThemedView } from "@/src/components/ui/ThemedView";
import { Colors } from "@/src/constants/Colors";
import { useTheme } from "@/src/context/ThemeContext";
import React from "react";
import { StyleSheet, View } from "react-native";
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withRepeat,
  withTiming,
} from "react-native-reanimated";

interface SkeletonNotificationListProps {
  count?: number;
}

const SkeletonNotificationList: React.FC<SkeletonNotificationListProps> = ({
  count = 1,
}) => {
  const { currentTheme } = useTheme();
  const pulse = useSharedValue(0.5);

  React.useEffect(() => {
    pulse.value = withRepeat(
      withTiming(1, { duration: 700 }),
      -1,
      true // reverse animation
    );
  }, []);

  const animatedStyle = useAnimatedStyle(() => ({
    opacity: pulse.value,
  }));

  const background = Colors[currentTheme ?? "dark"].border;

  // If count is 1, render single item for InfiniteScrollList
  if (count === 1) {
    return (
      <ThemedView
        style={[
          styles.card,
          {
            backgroundColor: Colors[currentTheme ?? "dark"].thirdary,
            padding: scale(16),
          },
        ]}
      >
        <View style={styles.content}>
          {/* Icon Placeholder */}
          <Animated.View
            style={[
              styles.iconPlaceholder,
              { backgroundColor: background },
              animatedStyle,
            ]}
          />

          {/* Content Section */}
          <View style={styles.textContent}>
            {/* Title/Message line */}
            <Animated.View
              style={[
                styles.messageLine,
                { backgroundColor: background },
                animatedStyle,
              ]}
            />

            {/* Timestamp line */}
            <Animated.View
              style={[
                styles.timestampLine,
                { backgroundColor: background },
                animatedStyle,
              ]}
            />
          </View>
        </View>
      </ThemedView>
    );
  }

  // For multiple items, render array
  return (
    <>
      {Array(count)
        .fill(null)
        .map((_, idx) => (
          <ThemedView
            key={idx}
            style={[
              styles.card,
              {
                backgroundColor: Colors[currentTheme ?? "dark"].thirdary,
              },
            ]}
          >
            <View style={styles.content}>
              {/* Icon Placeholder */}
              <Animated.View
                style={[
                  styles.iconPlaceholder,
                  { backgroundColor: background },
                  animatedStyle,
                ]}
              />

              {/* Content Section */}
              <View style={styles.textContent}>
                {/* Title/Message line */}
                <Animated.View
                  style={[
                    styles.messageLine,
                    { backgroundColor: background },
                    animatedStyle,
                  ]}
                />

                {/* Timestamp line */}
                <Animated.View
                  style={[
                    styles.timestampLine,
                    { backgroundColor: background },
                    animatedStyle,
                  ]}
                />
              </View>
            </View>
          </ThemedView>
        ))}
    </>
  );
};

const styles = StyleSheet.create({
  card: {
    borderRadius: scale(16),
    paddingVertical: scale(16),
    marginBottom: scale(12),
  },
  content: {
    flexDirection: "row",
    alignItems: "center",
  },
  iconPlaceholder: {
    width: scale(24),
    height: scale(24),
    borderRadius: scale(12),
    marginRight: scale(10),
  },
  textContent: {
    flex: 1,
    gap: scale(4),
  },
  messageLine: {
    height: scale(16),
    width: "80%",
    borderRadius: scale(4),
  },
  timestampLine: {
    height: scale(12),
    width: "40%",
    borderRadius: scale(4),
    marginTop: scale(4),
  },
});

export default SkeletonNotificationList;
