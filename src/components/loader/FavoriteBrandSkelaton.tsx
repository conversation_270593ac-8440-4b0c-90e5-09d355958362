import { scale } from "@/src/_helper/Scaler";
import { Colors } from "@/src/constants/Colors";
import { useTheme } from "@/src/context/ThemeContext";
import React from "react";
import { StyleSheet, View } from "react-native";
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withRepeat,
  withTiming,
} from "react-native-reanimated";

interface SkeletonFavoriteBrandCardProps {
  count?: number;
}

const SkeletonFavoriteBrandCard: React.FC<SkeletonFavoriteBrandCardProps> = ({
  count = 1,
}) => {
  const { currentTheme } = useTheme();
  const pulse = useSharedValue(0.5);

  React.useEffect(() => {
    pulse.value = withRepeat(
      withTiming(1, { duration: 700 }),
      -1,
      true // reverse animation
    );
  }, []);

  const animatedStyle = useAnimatedStyle(() => ({
    opacity: pulse.value,
  }));

  const background = Colors[currentTheme ?? "dark"].border;

  return (
    <>
      {Array(count)
        .fill(null)
        .map((_, idx) => (
          <View key={idx} style={styles.container}>
            <View
              style={[
                styles.imageContainer,
                { backgroundColor: Colors[currentTheme ?? "dark"].secondary },
              ]}
            >
              <Animated.View
                style={[
                  styles.imagePlaceholder,
                  { backgroundColor: background },
                  animatedStyle,
                ]}
              />
            </View>

            <View style={styles.textContainer}>
              <Animated.View
                style={[
                  styles.titlePlaceholder,
                  { backgroundColor: background },
                  animatedStyle,
                ]}
              />
            </View>
          </View>
        ))}
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    width: scale(160),
    alignItems: "center",
  },
  imageContainer: {
    justifyContent: "center",
    alignItems: "center",
    borderRadius: scale(100),
  },
  imagePlaceholder: {
    width: scale(128),
    height: scale(128),
    borderRadius: scale(64),
  },
  textContainer: {
    marginTop: scale(8),
    paddingHorizontal: scale(7),
    alignItems: "center",
  },
  titlePlaceholder: {
    height: scale(16),
    width: scale(120),
    borderRadius: scale(8),
    marginBottom: scale(4),
  },
});

export default SkeletonFavoriteBrandCard;
