import { scale } from "@/src/_helper/Scaler";
import { Colors } from "@/src/constants/Colors";
import { useTheme } from "@/src/context/ThemeContext";
import React, { useEffect } from "react";
import { Dimensions, StyleSheet, View } from "react-native";
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withRepeat,
  withTiming,
} from "react-native-reanimated";
import { ThemedView } from "../ui/ThemedView";

const { width } = Dimensions.get("window");

const SkeletonDashboard = () => {
  const { currentTheme } = useTheme();
  const pulse = useSharedValue(0.5);

  useEffect(() => {
    pulse.value = withRepeat(withTiming(1, { duration: 700 }), -1, true);
  }, []);

  const animatedStyle = useAnimatedStyle(() => ({
    opacity: pulse.value,
  }));

  const background = Colors[currentTheme ?? "dark"].border;

  return (
    <ThemedView style={styles.container}>
      {/* Header */}
      <Animated.View
        style={[
          styles.headerPlaceholder,
          { backgroundColor: background },
          animatedStyle,
        ]}
      />

      {/* Overview Metrics */}
      <View style={styles.overviewGrid}>
        {Array(3)
          .fill(null)
          .map((_, idx) => (
            <Animated.View
              key={idx}
              style={[
                styles.metricCard,
                { backgroundColor: background, borderColor: background },
                animatedStyle,
              ]}
            />
          ))}
      </View>

      {/* Chart Sections */}
      {Array(4)
        .fill(null)
        .map((_, idx) => (
          <View key={idx} style={styles.section}>
            <Animated.View
              style={[
                styles.sectionTitle,
                { backgroundColor: background },
                animatedStyle,
              ]}
            />
            <Animated.View
              style={[
                styles.chartPlaceholder,
                { backgroundColor: background },
                animatedStyle,
              ]}
            />
          </View>
        ))}

      {/* Product Performance Cards */}
      <View style={styles.section}>
        <Animated.View
          style={[
            styles.sectionTitle,
            { backgroundColor: background },
            animatedStyle,
          ]}
        />
        {Array(3)
          .fill(null)
          .map((_, idx) => (
            <Animated.View
              key={idx}
              style={[
                styles.productCard,
                { backgroundColor: background, borderColor: background },
                animatedStyle,
              ]}
            />
          ))}
      </View>

      {/* Customer Stats */}
      <View style={styles.section}>
        <Animated.View
          style={[
            styles.sectionTitle,
            { backgroundColor: background },
            animatedStyle,
          ]}
        />
        <View style={styles.customerStats}>
          {Array(2)
            .fill(null)
            .map((_, idx) => (
              <Animated.View
                key={idx}
                style={[
                  styles.customerStatCard,
                  { backgroundColor: background, borderColor: background },
                  animatedStyle,
                ]}
              />
            ))}
        </View>
      </View>
    </ThemedView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: scale(16),
  },
  headerPlaceholder: {
    height: scale(32),
    width: scale(120),
    borderRadius: scale(8),
    marginBottom: scale(20),
  },
  overviewGrid: {
    flexDirection: "row",
    flexWrap: "wrap",
    justifyContent: "space-between",
    marginBottom: scale(24),
  },
  metricCard: {
    borderRadius: scale(12),
    height: scale(100),
    width: "48%",
    marginBottom: scale(12),
    borderWidth: scale(1),
  },
  section: {
    marginBottom: scale(24),
  },
  sectionTitle: {
    height: scale(28),
    width: scale(180),
    borderRadius: scale(8),
    marginBottom: scale(16),
  },
  chartPlaceholder: {
    height: scale(200),
    width: width - scale(64),
    borderRadius: scale(12),
    marginBottom: scale(16),
  },
  productCard: {
    height: scale(60),
    borderRadius: scale(12),
    marginBottom: scale(12),
    borderWidth: scale(1),
  },
  customerStats: {
    flexDirection: "row",
    justifyContent: "space-between",
  },
  customerStatCard: {
    height: scale(80),
    width: "48%",
    borderRadius: scale(12),
    borderWidth: scale(1),
  },
});

export default SkeletonDashboard;
