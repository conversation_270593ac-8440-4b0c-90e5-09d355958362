import { scale } from "@/src/_helper/Scaler";
import { ThemedView } from "@/src/components/ui/ThemedView";
import { Colors } from "@/src/constants/Colors";
import { useTheme } from "@/src/context/ThemeContext";
import React from "react";
import { StyleSheet, View } from "react-native";
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withRepeat,
  withTiming,
} from "react-native-reanimated";

interface SkeletonProductListProps {
  count?: number;
}

const SkeletonProductList: React.FC<SkeletonProductListProps> = ({
  count = 1,
}) => {
  const { currentTheme } = useTheme();
  const pulse = useSharedValue(0.5);

  React.useEffect(() => {
    pulse.value = withRepeat(
      withTiming(1, { duration: 700 }),
      -1,
      true // reverse animation
    );
  }, []);

  const animatedStyle = useAnimatedStyle(() => ({
    opacity: pulse.value,
  }));

  const background = Colors[currentTheme ?? "dark"].border;

  return (
    <>
      {Array(count)
        .fill(null)
        .map((_, idx) => (
          <ThemedView
            key={idx}
            style={[
              styles.card,
              { backgroundColor: Colors[currentTheme ?? "dark"].thirdary },
            ]}
          >
            <Animated.View
              style={[
                styles.productImagePlaceholder,
                { backgroundColor: background },
                animatedStyle,
              ]}
            />
            <View style={styles.textContainer}>
              <Animated.View
                style={[
                  styles.textLine,
                  { backgroundColor: background },
                  animatedStyle,
                ]}
              />
              <Animated.View
                style={[
                  styles.textLineShort,
                  { backgroundColor: background },
                  animatedStyle,
                ]}
              />
            </View>
            <Animated.View
              style={[
                styles.chevronIcon,
                { backgroundColor: background },
                animatedStyle,
              ]}
            />
          </ThemedView>
        ))}
    </>
  );
};

const styles = StyleSheet.create({
  card: {
    flexDirection: "row",
    alignItems: "center",
    padding: scale(16),
    marginBottom: scale(12),
    borderRadius: scale(16),
    overflow: "hidden",
    marginTop: scale(4),
  },
  productImagePlaceholder: {
    width: scale(50),
    height: scale(50),
    borderRadius: scale(12),
    marginRight: scale(16),
  },
  textContainer: {
    flex: 1,
    justifyContent: "space-between",
  },
  textLine: {
    height: scale(12),
    borderRadius: scale(6),
    marginBottom: scale(8),
    width: "75%",
  },
  textLineShort: {
    height: scale(10),
    borderRadius: scale(5),
    width: "45%",
  },
  chevronIcon: {
    width: scale(24),
    height: scale(24),
    borderRadius: scale(4),
    marginLeft: scale(12),
  },
});

export default SkeletonProductList;
