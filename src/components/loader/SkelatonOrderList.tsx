import { scale } from "@/src/_helper/Scaler";
import { ThemedView } from "@/src/components/ui/ThemedView";
import { Colors } from "@/src/constants/Colors";
import { useTheme } from "@/src/context/ThemeContext";
import React from "react";
import { StyleSheet, View } from "react-native";
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withRepeat,
  withTiming,
} from "react-native-reanimated";

interface SkeletonOrderListProps {
  count?: number;
}

const SkeletonOrderList: React.FC<SkeletonOrderListProps> = ({ count = 1 }) => {
  const { currentTheme } = useTheme();
  const pulse = useSharedValue(0.5);

  React.useEffect(() => {
    pulse.value = withRepeat(
      withTiming(1, { duration: 700 }),
      -1,
      true // reverse animation
    );
  }, []);

  const animatedStyle = useAnimatedStyle(() => ({
    opacity: pulse.value,
  }));

  const background = Colors[currentTheme ?? "dark"].border;

  return (
    <>
      {Array(count)
        .fill(null)
        .map((_, idx) => (
          <ThemedView
            key={idx}
            style={[
              styles.card,
              {
                backgroundColor: Colors[currentTheme ?? "dark"].thirdary,
                shadowColor: Colors[currentTheme ?? "dark"].text,
              },
            ]}
          >
            <View style={styles.content}>
              {/* Avatar placeholder */}
              <Animated.View
                style={[
                  styles.avatarPlaceholder,
                  { backgroundColor: background },
                  animatedStyle,
                ]}
              />

              {/* Text content placeholder */}
              <View style={styles.info}>
                {/* Header section with name and badge */}
                <View style={styles.headerSection}>
                  <Animated.View
                    style={[
                      styles.userNameLine,
                      { backgroundColor: background },
                      animatedStyle,
                    ]}
                  />
                  <Animated.View
                    style={[
                      styles.badgePlaceholder,
                      { backgroundColor: background },
                      animatedStyle,
                    ]}
                  />
                </View>

                {/* Details section */}
                <View style={styles.detailsSection}>
                  {/* Product line with icon space */}
                  <View style={styles.detailRow}>
                    <Animated.View
                      style={[
                        styles.iconPlaceholder,
                        { backgroundColor: background },
                        animatedStyle,
                      ]}
                    />
                    <Animated.View
                      style={[
                        styles.textLineShort,
                        { backgroundColor: background },
                        animatedStyle,
                      ]}
                    />
                  </View>

                  {/* Brand line with icon space */}
                  <View style={styles.detailRow}>
                    <Animated.View
                      style={[
                        styles.iconPlaceholder,
                        { backgroundColor: background },
                        animatedStyle,
                      ]}
                    />
                    <Animated.View
                      style={[
                        styles.textLineShort,
                        { backgroundColor: background },
                        animatedStyle,
                      ]}
                    />
                  </View>

                  {/* Phone line with icon space */}
                  <View style={styles.detailRow}>
                    <Animated.View
                      style={[
                        styles.iconPlaceholder,
                        { backgroundColor: background },
                        animatedStyle,
                      ]}
                    />
                    <Animated.View
                      style={[
                        styles.textLineMedium,
                        { backgroundColor: background },
                        animatedStyle,
                      ]}
                    />
                  </View>
                </View>
              </View>
            </View>

            {/* Chevron icon placeholder */}
            <View style={styles.chevronContainer}>
              <Animated.View
                style={[
                  styles.chevronIcon,
                  { backgroundColor: background },
                  animatedStyle,
                ]}
              />
            </View>
          </ThemedView>
        ))}
    </>
  );
};

const styles = StyleSheet.create({
  card: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    borderRadius: scale(16),
    padding: scale(16),
    marginBottom: scale(12),
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 3,
  },
  content: {
    flexDirection: "row",
    alignItems: "flex-start",
    flex: 1,
  },
  avatarPlaceholder: {
    width: scale(50),
    height: scale(50),
    borderRadius: scale(25),
    marginRight: scale(16),
  },
  info: {
    flex: 1,
  },
  headerSection: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    marginBottom: scale(8),
  },
  badgePlaceholder: {
    width: scale(45),
    height: scale(18),
    borderRadius: scale(10),
  },
  detailsSection: {
    gap: scale(6),
  },
  userNameLine: {
    height: scale(14),
    borderRadius: scale(7),
    width: "55%",
    flex: 1,
    marginRight: scale(8),
  },
  detailRow: {
    flexDirection: "row",
    alignItems: "center",
  },
  iconPlaceholder: {
    width: scale(16),
    height: scale(10),
    borderRadius: scale(2),
    marginRight: scale(8),
  },
  textLineShort: {
    height: scale(10),
    borderRadius: scale(5),
    width: "45%",
    flex: 1,
  },
  textLineMedium: {
    height: scale(10),
    borderRadius: scale(5),
    width: "65%",
    flex: 1,
  },
  chevronContainer: {
    paddingLeft: scale(8),
    alignItems: "center",
    justifyContent: "center",
  },
  chevronIcon: {
    width: scale(24),
    height: scale(24),
    borderRadius: scale(4),
  },
});

export default SkeletonOrderList;
