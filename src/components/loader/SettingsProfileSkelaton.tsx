import { scale } from "@/src/_helper/Scaler";
import { Colors } from "@/src/constants/Colors";
import { useTheme } from "@/src/context/ThemeContext";
import React from "react";
import { StyleSheet, View } from "react-native";
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withRepeat,
  withTiming,
} from "react-native-reanimated";

interface SettingsProfileInfoSkeletonProps {
  infoItemsCount?: number;
}

export function SettingsProfileInfoSkeleton({
  infoItemsCount = 3,
}: SettingsProfileInfoSkeletonProps) {
  const { currentTheme } = useTheme();
  const pulse = useSharedValue(0.5);

  React.useEffect(() => {
    pulse.value = withRepeat(
      withTiming(1, { duration: 700 }),
      -1,
      true // reverse animation
    );
  }, []);

  const animatedStyle = useAnimatedStyle(() => ({
    opacity: pulse.value,
  }));

  const background = Colors[currentTheme ?? "dark"].border;

  return (
    <View style={styles.container}>
      {/* Avatar skeleton */}
      <View style={styles.avatarWrapper}>
        <Animated.View
          style={[
            styles.avatar,
            { backgroundColor: background },
            animatedStyle,
          ]}
        />
      </View>

      {/* Info items skeleton */}
      <View style={styles.infoContainer}>
        {Array(infoItemsCount)
          .fill(null)
          .map((_, idx) => (
            <View key={idx} style={styles.infoItem}>
              {/* Label skeleton */}
              <Animated.View
                style={[
                  styles.labelSkeleton,
                  { backgroundColor: background },
                  animatedStyle,
                ]}
              />
              {/* Value skeleton */}
              <Animated.View
                style={[
                  styles.valueSkeleton,
                  { backgroundColor: background },
                  animatedStyle,
                ]}
              />
            </View>
          ))}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    width: "100%",
  },
  avatarWrapper: {
    marginBottom: scale(10),
    width: "100%",
    justifyContent: "center",
    alignItems: "center",
  },
  avatar: {
    borderRadius: scale(60),
    height: scale(120),
    width: scale(120),
    alignSelf: "center",
  },
  infoContainer: {
    width: "100%",
    borderRadius: scale(12),
    paddingVertical: scale(20),
  },
  infoItem: {
    marginBottom: scale(20),
  },
  labelSkeleton: {
    height: scale(14),
    width: scale(80),
    borderRadius: scale(7),
    marginBottom: scale(6),
  },
  valueSkeleton: {
    height: scale(16),
    width: scale(150),
    borderRadius: scale(8),
  },
});
