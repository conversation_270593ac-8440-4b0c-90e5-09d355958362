import { scale } from "@/src/_helper/Scaler";
import { Colors } from "@/src/constants/Colors";
import { useTheme } from "@/src/context/ThemeContext";
import React from "react";
import { Dimensions, StyleSheet, View } from "react-native";

const { width } = Dimensions.get("window");

const SkeletonFeaturedItem = () => {
  const { currentTheme } = useTheme();
  const theme = currentTheme ?? "dark";

  return (
    <View style={[styles.container, { width }]}>
      <View style={styles.content}>
        <View style={styles.textContainer}>
          {/* Featured label skeleton */}
          <View
            style={[
              styles.featuredLabel,
              { backgroundColor: Colors[theme].border },
            ]}
          />

          {/* Title skeleton */}
          <View
            style={[styles.title, { backgroundColor: Colors[theme].border }]}
          />

          {/* Description skeleton - multiple lines */}
          <View
            style={[
              styles.descriptionLine1,
              { backgroundColor: Colors[theme].border },
            ]}
          />
          <View
            style={[
              styles.descriptionLine2,
              { backgroundColor: Colors[theme].border },
            ]}
          />
        </View>

        {/* Image skeleton */}
        <View
          style={[
            styles.imageSkeleton,
            { backgroundColor: Colors[theme].border },
          ]}
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    justifyContent: "flex-start",
  },
  content: {
    flexDirection: "row",
    gap: scale(10),
    paddingHorizontal: scale(15),
    alignItems: "center",
  },
  textContainer: {
    flex: 1,
  },
  featuredLabel: {
    height: scale(14),
    width: scale(60),
    borderRadius: scale(4),
    marginBottom: scale(8),
  },
  title: {
    height: scale(18),
    width: "80%",
    borderRadius: scale(4),
    marginBottom: scale(8),
  },
  descriptionLine1: {
    height: scale(14),
    width: "100%",
    borderRadius: scale(4),
    marginBottom: scale(4),
  },
  descriptionLine2: {
    height: scale(14),
    width: "70%",
    borderRadius: scale(4),
  },
  imageSkeleton: {
    height: scale(93),
    width: scale(138),
    borderRadius: scale(10),
  },
});

export default SkeletonFeaturedItem;
