import { scale } from "@/src/_helper/Scaler";
import { Colors } from "@/src/constants/Colors";
import { useTheme } from "@/src/context/ThemeContext";
import React, { useEffect } from "react";
import { StyleSheet, View } from "react-native";
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withRepeat,
  withTiming,
} from "react-native-reanimated";

interface SkeletonShopCategoryProps {
  count?: number;
}

const SkeletonShopCategory: React.FC<SkeletonShopCategoryProps> = ({
  count = 1,
}) => {
  const { currentTheme } = useTheme();
  const pulse = useSharedValue(0.5);

  useEffect(() => {
    pulse.value = withRepeat(withTiming(1, { duration: 700 }), -1, true);
  }, []);

  const animatedStyle = useAnimatedStyle(() => ({
    opacity: pulse.value,
  }));

  const background = Colors[currentTheme ?? "dark"].border;
  const secondary = Colors[currentTheme ?? "dark"].secondary;

  return (
    <View
      style={{
        flexWrap: "wrap",
        flexDirection: "row",
        justifyContent: "space-between",
        width: "100%",
      }}
    >
      {Array(count)
        .fill(null)
        .map((_, idx) => (
          <View
            key={idx}
            style={[
              styles.container,
              {
                backgroundColor: Colors[currentTheme ?? "dark"].background,
                borderColor: background,
              },
            ]}
          >
            <View style={[styles.imageBox, { backgroundColor: secondary }]}>
              <Animated.View
                style={[
                  styles.image,
                  { backgroundColor: background },
                  animatedStyle,
                ]}
              />
            </View>
            <View style={styles.textBox}>
              <Animated.View
                style={[
                  styles.textLine,
                  { backgroundColor: background },
                  animatedStyle,
                ]}
              />
            </View>
          </View>
        ))}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: "row",
    alignItems: "center",
    borderWidth: 1,
    borderRadius: scale(10),
    height: scale(70),
    width: scale(160),
    padding: scale(10),
  },
  imageBox: {
    width: scale(40),
    height: scale(40),
    borderRadius: scale(8),
    justifyContent: "center",
    alignItems: "center",
    marginRight: scale(10),
  },
  image: {
    width: scale(30),
    height: scale(30),
    borderRadius: scale(6),
  },
  textBox: {
    flex: 1,
    justifyContent: "center",
  },
  textLine: {
    height: scale(12),
    width: "80%",
    borderRadius: scale(4),
  },
});

export default SkeletonShopCategory;
