import { scale } from "@/src/_helper/Scaler";
import { Colors } from "@/src/constants/Colors";
import { useTheme } from "@/src/context/ThemeContext";
import React from "react";
import { StyleSheet, View } from "react-native";
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withRepeat,
  withTiming,
} from "react-native-reanimated";

interface SkeletonBrandDetailsProps {
  showReviews?: boolean;
  reviewCount?: number;
}

const SkeletonBrandDetails: React.FC<SkeletonBrandDetailsProps> = ({
  showReviews = true,
  reviewCount = 3,
}) => {
  const { currentTheme } = useTheme();
  const pulse = useSharedValue(0.5);

  React.useEffect(() => {
    pulse.value = withRepeat(
      withTiming(1, { duration: 700 }),
      -1,
      true // reverse animation
    );
  }, []);

  const animatedStyle = useAnimatedStyle(() => ({
    opacity: pulse.value,
  }));

  const background = Colors[currentTheme ?? "dark"].border;

  const renderSkeletonReview = (index: number) => (
    <View key={index} style={styles.reviewContainer}>
      <View style={styles.reviewHeader}>
        <Animated.View
          style={[
            styles.reviewAvatar,
            { backgroundColor: background },
            animatedStyle,
          ]}
        />
        <View style={styles.reviewUserInfo}>
          <Animated.View
            style={[
              styles.reviewUserName,
              { backgroundColor: background },
              animatedStyle,
            ]}
          />
          <Animated.View
            style={[
              styles.reviewDate,
              { backgroundColor: background },
              animatedStyle,
            ]}
          />
        </View>
      </View>

      <View style={styles.starContainer}>
        {Array(5)
          .fill(null)
          .map((_, starIndex) => (
            <Animated.View
              key={starIndex}
              style={[
                styles.starPlaceholder,
                { backgroundColor: background },
                animatedStyle,
              ]}
            />
          ))}
      </View>

      <Animated.View
        style={[
          styles.reviewTextLine1,
          { backgroundColor: background },
          animatedStyle,
        ]}
      />
      <Animated.View
        style={[
          styles.reviewTextLine2,
          { backgroundColor: background },
          animatedStyle,
        ]}
      />

      <View style={styles.reviewActions}>
        <View style={styles.actionButton}>
          <Animated.View
            style={[
              styles.actionIcon,
              { backgroundColor: background },
              animatedStyle,
            ]}
          />
          <Animated.View
            style={[
              styles.actionText,
              { backgroundColor: background },
              animatedStyle,
            ]}
          />
        </View>
        <View style={styles.actionButton}>
          <Animated.View
            style={[
              styles.actionIcon,
              { backgroundColor: background },
              animatedStyle,
            ]}
          />
          <Animated.View
            style={[
              styles.actionText,
              { backgroundColor: background },
              animatedStyle,
            ]}
          />
        </View>
      </View>
    </View>
  );

  return (
    <View style={styles.container}>
      {/* Header Section */}
      <View style={styles.headerSection}>
        <Animated.View
          style={[
            styles.brandLogo,
            { backgroundColor: background },
            animatedStyle,
          ]}
        />
        <Animated.View
          style={[
            styles.brandTitle,
            { backgroundColor: background },
            animatedStyle,
          ]}
        />
        <Animated.View
          style={[
            styles.brandSubtitle,
            { backgroundColor: background },
            animatedStyle,
          ]}
        />
        <Animated.View
          style={[
            styles.brandRating,
            { backgroundColor: background },
            animatedStyle,
          ]}
        />
        <Animated.View
          style={[
            styles.viewProductsButton,
            { backgroundColor: background },
            animatedStyle,
          ]}
        />
      </View>

      {/* Vendor Section */}
      <View style={styles.vendorSection}>
        <Animated.View
          style={[
            styles.sectionTitle,
            { backgroundColor: background },
            animatedStyle,
          ]}
        />
        <View style={styles.vendorInfo}>
          <Animated.View
            style={[
              styles.vendorAvatar,
              { backgroundColor: background },
              animatedStyle,
            ]}
          />
          <View style={styles.vendorDetails}>
            <Animated.View
              style={[
                styles.vendorName,
                { backgroundColor: background },
                animatedStyle,
              ]}
            />
            <Animated.View
              style={[
                styles.vendorEmail,
                { backgroundColor: background },
                animatedStyle,
              ]}
            />
          </View>
        </View>
      </View>

      {/* About Section */}
      <View style={styles.aboutSection}>
        <Animated.View
          style={[
            styles.sectionTitle,
            { backgroundColor: background },
            animatedStyle,
          ]}
        />
        <Animated.View
          style={[
            styles.aboutTextLine1,
            { backgroundColor: background },
            animatedStyle,
          ]}
        />
        <Animated.View
          style={[
            styles.aboutTextLine2,
            { backgroundColor: background },
            animatedStyle,
          ]}
        />
        <Animated.View
          style={[
            styles.aboutTextLine3,
            { backgroundColor: background },
            animatedStyle,
          ]}
        />

        {/* Location */}
        <View style={styles.locationContainer}>
          <Animated.View
            style={[
              styles.locationIcon,
              { backgroundColor: background },
              animatedStyle,
            ]}
          />
          <Animated.View
            style={[
              styles.locationText,
              { backgroundColor: background },
              animatedStyle,
            ]}
          />
        </View>

        {/* Phone */}
        <View style={styles.contactContainer}>
          <Animated.View
            style={[
              styles.contactIcon,
              { backgroundColor: background },
              animatedStyle,
            ]}
          />
          <Animated.View
            style={[
              styles.contactText,
              { backgroundColor: background },
              animatedStyle,
            ]}
          />
        </View>
      </View>

      {/* Reviews Section */}
      {showReviews && (
        <View style={styles.reviewsSection}>
          <Animated.View
            style={[
              styles.sectionTitle,
              { backgroundColor: background },
              animatedStyle,
            ]}
          />
          {Array(reviewCount)
            .fill(null)
            .map((_, index) => renderSkeletonReview(index))}
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: scale(20),
    paddingVertical: scale(20),
  },
  headerSection: {
    alignItems: "center",
    marginBottom: scale(30),
  },
  brandLogo: {
    width: scale(100),
    height: scale(100),
    borderRadius: scale(50),
  },
  brandTitle: {
    width: scale(180),
    height: scale(24),
    borderRadius: scale(12),
    marginTop: scale(10),
  },
  brandSubtitle: {
    width: scale(220),
    height: scale(16),
    borderRadius: scale(8),
    marginTop: scale(4),
  },
  brandRating: {
    width: scale(120),
    height: scale(16),
    borderRadius: scale(8),
    marginTop: scale(4),
  },
  viewProductsButton: {
    width: "100%",
    height: scale(40),
    borderRadius: scale(20),
    marginTop: scale(15),
  },
  vendorSection: {
    marginBottom: scale(30),
  },
  sectionTitle: {
    width: scale(80),
    height: scale(20),
    borderRadius: scale(10),
    marginBottom: scale(10),
  },
  vendorInfo: {
    flexDirection: "row",
    alignItems: "center",
  },
  vendorAvatar: {
    width: scale(30),
    height: scale(30),
    borderRadius: scale(15),
  },
  vendorDetails: {
    marginLeft: scale(10),
    flex: 1,
  },
  vendorName: {
    width: scale(140),
    height: scale(16),
    borderRadius: scale(8),
    marginBottom: scale(4),
  },
  vendorEmail: {
    width: scale(180),
    height: scale(14),
    borderRadius: scale(7),
  },
  aboutSection: {
    marginBottom: scale(30),
  },
  aboutTextLine1: {
    width: "100%",
    height: scale(16),
    borderRadius: scale(8),
    marginBottom: scale(8),
  },
  aboutTextLine2: {
    width: "85%",
    height: scale(16),
    borderRadius: scale(8),
    marginBottom: scale(8),
  },
  aboutTextLine3: {
    width: "60%",
    height: scale(16),
    borderRadius: scale(8),
    marginBottom: scale(8),
  },
  locationContainer: {
    flexDirection: "row",
    alignItems: "center",
    marginTop: scale(8),
  },
  locationIcon: {
    width: scale(16),
    height: scale(16),
    borderRadius: scale(8),
  },
  locationText: {
    width: scale(160),
    height: scale(14),
    borderRadius: scale(7),
    marginLeft: scale(6),
  },
  contactContainer: {
    flexDirection: "row",
    alignItems: "center",
    marginTop: scale(4),
  },
  contactIcon: {
    width: scale(16),
    height: scale(16),
    borderRadius: scale(8),
  },
  contactText: {
    width: scale(120),
    height: scale(14),
    borderRadius: scale(7),
    marginLeft: scale(6),
  },
  reviewsSection: {
    marginBottom: scale(30),
  },
  reviewContainer: {
    marginTop: scale(15),
    borderRadius: scale(10),
    padding: scale(15),
  },
  reviewHeader: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: scale(8),
  },
  reviewAvatar: {
    width: scale(30),
    height: scale(30),
    borderRadius: scale(15),
  },
  reviewUserInfo: {
    marginLeft: scale(10),
    flex: 1,
  },
  reviewUserName: {
    width: scale(100),
    height: scale(16),
    borderRadius: scale(8),
    marginBottom: scale(4),
  },
  reviewDate: {
    width: scale(80),
    height: scale(12),
    borderRadius: scale(6),
  },
  starContainer: {
    flexDirection: "row",
    marginBottom: scale(8),
  },
  starPlaceholder: {
    width: scale(16),
    height: scale(16),
    borderRadius: scale(8),
    marginRight: scale(2),
  },
  reviewTextLine1: {
    width: "100%",
    height: scale(16),
    borderRadius: scale(8),
    marginBottom: scale(6),
  },
  reviewTextLine2: {
    width: "70%",
    height: scale(16),
    borderRadius: scale(8),
    marginBottom: scale(10),
  },
  reviewActions: {
    flexDirection: "row",
  },
  actionButton: {
    flexDirection: "row",
    alignItems: "center",
    marginRight: scale(15),
  },
  actionIcon: {
    width: scale(14),
    height: scale(14),
    borderRadius: scale(7),
  },
  actionText: {
    width: scale(20),
    height: scale(12),
    borderRadius: scale(6),
    marginLeft: scale(4),
  },
});

export default SkeletonBrandDetails;
