import { scale } from "@/src/_helper/Scaler";
import { ThemedView } from "@/src/components/ui/ThemedView";
import { Colors } from "@/src/constants/Colors";
import { useTheme } from "@/src/context/ThemeContext";
import React from "react";
import { StyleSheet, View } from "react-native";
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withRepeat,
  withTiming,
} from "react-native-reanimated";

interface SkeletonOrderListProps {
  count?: number;
}

const SkeletonOrderListClient: React.FC<SkeletonOrderListProps> = ({
  count = 1,
}) => {
  const { currentTheme } = useTheme();
  const pulse = useSharedValue(0.5);

  React.useEffect(() => {
    pulse.value = withRepeat(
      withTiming(1, { duration: 700 }),
      -1,
      true // reverse animation
    );
  }, []);

  const animatedStyle = useAnimatedStyle(() => ({
    opacity: pulse.value,
  }));

  const background = Colors[currentTheme ?? "dark"].border;

  return (
    <>
      {Array(count)
        .fill(null)
        .map((_, idx) => (
          <ThemedView
            key={idx}
            style={[
              styles.card,
              {
                backgroundColor: Colors[currentTheme ?? "dark"].thirdary,
                shadowColor: Colors[currentTheme ?? "dark"].text,
              },
            ]}
          >
            <View style={styles.content}>
              {/* Product Image Placeholder */}
              <Animated.View
                style={[
                  styles.imagePlaceholder,
                  { backgroundColor: background },
                  animatedStyle,
                ]}
              />

              {/* Content Section */}
              <View style={styles.info}>
                {/* Header section with title and status */}
                <View style={styles.headerSection}>
                  <Animated.View
                    style={[
                      styles.titleLine,
                      { backgroundColor: background },
                      animatedStyle,
                    ]}
                  />
                  <Animated.View
                    style={[
                      styles.statusBadge,
                      { backgroundColor: background },
                      animatedStyle,
                    ]}
                  />
                </View>

                {/* Details section */}
                <View style={styles.detailsSection}>
                  {/* Brand line with icon space */}
                  <View style={styles.detailRow}>
                    <Animated.View
                      style={[
                        styles.iconPlaceholder,
                        { backgroundColor: background },
                        animatedStyle,
                      ]}
                    />
                    <Animated.View
                      style={[
                        styles.textLineShort,
                        { backgroundColor: background },
                        animatedStyle,
                      ]}
                    />
                  </View>

                  {/* Date line with icon space */}
                  <View style={styles.detailRow}>
                    <Animated.View
                      style={[
                        styles.iconPlaceholder,
                        { backgroundColor: background },
                        animatedStyle,
                      ]}
                    />
                    <Animated.View
                      style={[
                        styles.textLineShort,
                        { backgroundColor: background },
                        animatedStyle,
                      ]}
                    />
                  </View>

                  {/* Price line with icon space */}
                  <View style={styles.detailRow}>
                    <Animated.View
                      style={[
                        styles.iconPlaceholder,
                        { backgroundColor: background },
                        animatedStyle,
                      ]}
                    />
                    <Animated.View
                      style={[
                        styles.textLineVeryShort,
                        { backgroundColor: background },
                        animatedStyle,
                      ]}
                    />
                  </View>
                </View>
              </View>

              {/* Chevron placeholder */}
              <Animated.View
                style={[
                  styles.chevronPlaceholder,
                  { backgroundColor: background },
                  animatedStyle,
                ]}
              />
            </View>
          </ThemedView>
        ))}
    </>
  );
};

const styles = StyleSheet.create({
  card: {
    borderRadius: scale(16),
    padding: scale(16),
    marginBottom: scale(12),
    shadowOffset: {
      width: 0,
      height: scale(2),
    },
    shadowOpacity: 0.05,
    shadowRadius: scale(8),
    elevation: 3, // elevation should be an integer
  },
  content: {
    flexDirection: "row",
    alignItems: "center",
  },
  imagePlaceholder: {
    width: scale(60),
    height: scale(60),
    borderRadius: scale(8),
    marginRight: scale(16),
  },
  info: {
    flex: 1,
  },
  headerSection: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    marginBottom: scale(8),
  },
  titleLine: {
    height: scale(16),
    width: "60%",
    borderRadius: scale(4),
  },
  statusBadge: {
    height: scale(20),
    width: scale(60),
    borderRadius: scale(12),
  },
  detailsSection: {
    gap: scale(4),
  },
  detailRow: {
    flexDirection: "row",
    alignItems: "center",
  },
  iconPlaceholder: {
    width: scale(16),
    height: scale(14),
    borderRadius: scale(2),
    marginRight: scale(8),
  },
  textLineShort: {
    height: scale(12),
    width: "40%",
    borderRadius: scale(4),
  },
  textLineVeryShort: {
    height: scale(12),
    width: "25%",
    borderRadius: scale(4),
  },
  chevronPlaceholder: {
    width: scale(24),
    height: scale(24),
    borderRadius: scale(4),
    marginLeft: scale(8),
  },
});

export default SkeletonOrderListClient;
