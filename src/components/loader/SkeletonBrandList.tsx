import { scale } from "@/src/_helper/Scaler";
import { ThemedView } from "@/src/components/ui/ThemedView";
import { Colors } from "@/src/constants/Colors";
import { useTheme } from "@/src/context/ThemeContext";
import React from "react";
import { StyleSheet, View } from "react-native";
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withRepeat,
  withTiming,
} from "react-native-reanimated";

interface SkeletonBrandListProps {
  count?: number;
}

const SkeletonBrandList: React.FC<SkeletonBrandListProps> = ({ count = 1 }) => {
  const { currentTheme } = useTheme();
  const pulse = useSharedValue(0.5);

  React.useEffect(() => {
    pulse.value = withRepeat(
      withTiming(1, { duration: 700 }),
      -1,
      true // reverse animation
    );
  }, []);

  const animatedStyle = useAnimatedStyle(() => ({
    opacity: pulse.value,
  }));

  const background = Colors[currentTheme ?? "dark"].border;

  return (
    <>
      {Array(count)
        .fill(null)
        .map((_, idx) => (
          <ThemedView
            key={idx}
            style={[
              styles.card,
              { backgroundColor: Colors[currentTheme ?? "dark"].thirdary },
            ]}
          >
            <Animated.View
              style={[
                styles.logoPlaceholder,
                { backgroundColor: background },
                animatedStyle,
              ]}
            />
            <View style={styles.textContainer}>
              <Animated.View
                style={[
                  styles.textLine,
                  { backgroundColor: background },
                  animatedStyle,
                ]}
              />
              <Animated.View
                style={[
                  styles.textLineShort,
                  { backgroundColor: background },
                  animatedStyle,
                ]}
              />
            </View>
            <Animated.View
              style={[
                styles.editIcon,
                { backgroundColor: background },
                animatedStyle,
              ]}
            />
          </ThemedView>
        ))}
    </>
  );
};

const styles = StyleSheet.create({
  card: {
    flexDirection: "row",
    alignItems: "center",
    padding: scale(16),
    marginBottom: scale(16),
    borderRadius: scale(16),
    overflow: "hidden",
    marginTop: scale(8),
  },
  logoPlaceholder: {
    width: scale(48),
    height: scale(48),
    borderRadius: scale(12),
    marginRight: scale(16),
  },
  textContainer: {
    flex: 1,
    justifyContent: "space-between",
  },
  textLine: {
    height: scale(12),
    borderRadius: scale(6),
    marginBottom: scale(8),
    width: "80%",
  },
  textLineShort: {
    height: scale(10),
    borderRadius: scale(5),
    width: "50%",
  },
  editIcon: {
    width: scale(24),
    height: scale(24),
    borderRadius: scale(12),
    marginLeft: scale(12),
  },
});

export default SkeletonBrandList;
