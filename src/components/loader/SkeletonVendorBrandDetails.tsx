import { scale } from "@/src/_helper/Scaler";
import { Colors } from "@/src/constants/Colors";
import { useTheme } from "@/src/context/ThemeContext";
import React from "react";
import { StyleSheet, View } from "react-native";
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withRepeat,
  withTiming,
} from "react-native-reanimated";

interface SkeletonVendorBrandDetailsProps {
  showProducts?: boolean;
  productCount?: number;
}

const SkeletonVendorBrandDetails: React.FC<SkeletonVendorBrandDetailsProps> = ({
  showProducts = true,
  productCount = 3,
}) => {
  const { currentTheme } = useTheme();
  const pulse = useSharedValue(0.5);

  React.useEffect(() => {
    pulse.value = withRepeat(
      withTiming(1, { duration: 700 }),
      -1,
      true // reverse animation
    );
  }, []);

  const animatedStyle = useAnimatedStyle(() => ({
    opacity: pulse.value,
  }));

  const background = Colors[currentTheme ?? "dark"].border;

  const renderSkeletonProduct = (index: number) => (
    <View key={index} style={styles.productCard}>
      <View style={styles.productContent}>
        <Animated.View
          style={[
            styles.productImage,
            { backgroundColor: background },
            animatedStyle,
          ]}
        />
        <View style={styles.productInfo}>
          <Animated.View
            style={[
              styles.productName,
              { backgroundColor: background },
              animatedStyle,
            ]}
          />
          <Animated.View
            style={[
              styles.productDescription,
              { backgroundColor: background },
              animatedStyle,
            ]}
          />
          <Animated.View
            style={[
              styles.productPrice,
              { backgroundColor: background },
              animatedStyle,
            ]}
          />
        </View>
      </View>
      <Animated.View
        style={[
          styles.editButton,
          { backgroundColor: background },
          animatedStyle,
        ]}
      />
    </View>
  );

  return (
    <View style={styles.container}>
      {/* Brand Section */}
      <View style={styles.brandSection}>
        <View style={styles.brandLogoContainer}>
          <Animated.View
            style={[
              styles.brandLogo,
              { backgroundColor: background },
              animatedStyle,
            ]}
          />
        </View>
        <View style={styles.brandInfo}>
          <Animated.View
            style={[
              styles.brandName,
              { backgroundColor: background },
              animatedStyle,
            ]}
          />
          <Animated.View
            style={[
              styles.brandCategory,
              { backgroundColor: background },
              animatedStyle,
            ]}
          />
          <Animated.View
            style={[
              styles.brandProducts,
              { backgroundColor: background },
              animatedStyle,
            ]}
          />
          <View style={styles.ratingContainer}>
            <Animated.View
              style={[
                styles.starIcon,
                { backgroundColor: background },
                animatedStyle,
              ]}
            />
            <Animated.View
              style={[
                styles.ratingText,
                { backgroundColor: background },
                animatedStyle,
              ]}
            />
          </View>
        </View>
      </View>

      {/* About Section */}
      <View style={styles.section}>
        <Animated.View
          style={[
            styles.sectionTitle,
            { backgroundColor: background },
            animatedStyle,
          ]}
        />
        <Animated.View
          style={[
            styles.aboutTextLine1,
            { backgroundColor: background },
            animatedStyle,
          ]}
        />
        <Animated.View
          style={[
            styles.aboutTextLine2,
            { backgroundColor: background },
            animatedStyle,
          ]}
        />
      </View>

      {/* Contact Section */}
      <View style={styles.section}>
        <Animated.View
          style={[
            styles.sectionTitle,
            { backgroundColor: background },
            animatedStyle,
          ]}
        />
        <View style={styles.contactItem}>
          <Animated.View
            style={[
              styles.contactLabel,
              { backgroundColor: background },
              animatedStyle,
            ]}
          />
          <Animated.View
            style={[
              styles.contactValue,
              { backgroundColor: background },
              animatedStyle,
            ]}
          />
        </View>
        <View style={styles.contactItem}>
          <Animated.View
            style={[
              styles.contactLabel,
              { backgroundColor: background },
              animatedStyle,
            ]}
          />
          <Animated.View
            style={[
              styles.contactValue,
              { backgroundColor: background },
              animatedStyle,
            ]}
          />
        </View>
      </View>

      {/* Location Section */}
      <View style={styles.section}>
        <Animated.View
          style={[
            styles.sectionTitle,
            { backgroundColor: background },
            animatedStyle,
          ]}
        />
        <View style={styles.addressContainer}>
          <Animated.View
            style={[
              styles.locationIcon,
              { backgroundColor: background },
              animatedStyle,
            ]}
          />
          <Animated.View
            style={[
              styles.addressText,
              { backgroundColor: background },
              animatedStyle,
            ]}
          />
        </View>
        <Animated.View
          style={[
            styles.mapContainer,
            { backgroundColor: background },
            animatedStyle,
          ]}
        />
      </View>

      {/* Products Section */}
      {showProducts && (
        <View style={styles.section}>
          <Animated.View
            style={[
              styles.sectionTitle,
              { backgroundColor: background },
              animatedStyle,
            ]}
          />
          {Array(productCount)
            .fill(null)
            .map((_, index) => renderSkeletonProduct(index))}
        </View>
      )}

      {/* Action Buttons */}
      <View style={styles.buttonContainer}>
        <Animated.View
          style={[
            styles.actionButton,
            { backgroundColor: background },
            animatedStyle,
          ]}
        />
        <Animated.View
          style={[
            styles.deleteButton,
            { backgroundColor: background },
            animatedStyle,
          ]}
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: scale(20),
    paddingVertical: scale(20),
  },
  brandSection: {
    alignItems: "center",
    marginBottom: scale(24),
  },
  brandLogoContainer: {
    marginBottom: scale(16),
  },
  brandLogo: {
    width: scale(80),
    height: scale(80),
    borderRadius: scale(40),
  },
  brandInfo: {
    alignItems: "center",
  },
  brandName: {
    width: scale(180),
    height: scale(24),
    borderRadius: scale(12),
    marginBottom: scale(4),
  },
  brandCategory: {
    width: scale(120),
    height: scale(16),
    borderRadius: scale(8),
    marginBottom: scale(4),
  },
  brandProducts: {
    width: scale(100),
    height: scale(14),
    borderRadius: scale(7),
    marginBottom: scale(8),
  },
  ratingContainer: {
    flexDirection: "row",
    alignItems: "center",
  },
  starIcon: {
    width: scale(16),
    height: scale(16),
    borderRadius: scale(8),
    marginRight: scale(4),
  },
  ratingText: {
    width: scale(80),
    height: scale(14),
    borderRadius: scale(7),
  },
  section: {
    marginBottom: scale(24),
  },
  sectionTitle: {
    width: scale(100),
    height: scale(18),
    borderRadius: scale(9),
    marginBottom: scale(10),
  },
  aboutTextLine1: {
    width: "100%",
    height: scale(16),
    borderRadius: scale(8),
    marginBottom: scale(8),
  },
  aboutTextLine2: {
    width: "85%",
    height: scale(16),
    borderRadius: scale(8),
  },
  contactItem: {
    marginBottom: scale(8),
  },
  contactLabel: {
    width: scale(60),
    height: scale(14),
    borderRadius: scale(7),
    marginBottom: scale(4),
  },
  contactValue: {
    width: scale(140),
    height: scale(14),
    borderRadius: scale(7),
  },
  addressContainer: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: scale(12),
  },
  locationIcon: {
    width: scale(16),
    height: scale(16),
    borderRadius: scale(8),
    marginRight: scale(6),
  },
  addressText: {
    width: scale(200),
    height: scale(14),
    borderRadius: scale(7),
  },
  mapContainer: {
    width: "100%",
    height: scale(150),
    borderRadius: scale(10),
  },
  productCard: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: scale(12),
    paddingVertical: scale(8),
  },
  productContent: {
    flexDirection: "row",
    flex: 1,
  },
  productImage: {
    width: scale(50),
    height: scale(50),
    borderRadius: scale(8),
    marginRight: scale(12),
  },
  productInfo: {
    flex: 1,
  },
  productName: {
    width: "80%",
    height: scale(16),
    borderRadius: scale(8),
    marginBottom: scale(4),
  },
  productDescription: {
    width: "100%",
    height: scale(14),
    borderRadius: scale(7),
    marginBottom: scale(4),
  },
  productPrice: {
    width: scale(60),
    height: scale(14),
    borderRadius: scale(7),
  },
  editButton: {
    width: scale(20),
    height: scale(20),
    borderRadius: scale(10),
  },
  buttonContainer: {
    marginTop: scale(20),
  },
  actionButton: {
    width: "100%",
    height: scale(50),
    borderRadius: scale(25),
    marginBottom: scale(8),
  },
  deleteButton: {
    width: "100%",
    height: scale(50),
    borderRadius: scale(25),
  },
});

export default SkeletonVendorBrandDetails;
