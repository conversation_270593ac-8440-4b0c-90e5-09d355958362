import { scale } from "@/src/_helper/Scaler";
import { Colors } from "@/src/constants/Colors";
import { useTheme } from "@/src/context/ThemeContext";
import { router } from "expo-router";
import React from "react";
import {
  Image,
  ImageURISource,
  StyleSheet,
  TouchableOpacity,
  View,
} from "react-native";
import { ThemedText } from "../ui/ThemedText";

interface SearchResultProductItemProps {
  id: string;
  image: ImageURISource;
  title: string;
  price: string;
  brand?: string;
}

const SearchResultProductItem: React.FC<SearchResultProductItemProps> = ({
  id,
  image,
  title,
  price,
  brand,
}) => {
  const { currentTheme } = useTheme();

  const handlePress = () => {
    router.push(`/(client)/(screens)/products/${id}/product`);
  };

  return (
    <TouchableOpacity
      style={[styles.container]}
      activeOpacity={0.7}
      onPress={handlePress}
    >
      <View
        style={[
          styles.imageContainer,
          { backgroundColor: Colors[currentTheme ?? "dark"].secondary },
        ]}
      >
        <Image
          source={image || require("@/src/assets/images/icon.png")}
          style={styles.image}
          resizeMode="cover"
        />
      </View>

      <View style={styles.contentContainer}>
        <View style={styles.textContainer}>
          <ThemedText size={16} type="semi-bold" numberOfLines={1}>
            {title}
          </ThemedText>
          {brand && (
            <ThemedText
              size={14}
              type="regular"
              style={{
                color: Colors[currentTheme ?? "dark"].secondary,
                marginTop: scale(2),
              }}
              numberOfLines={1}
            >
              {brand}
            </ThemedText>
          )}
          <ThemedText
            size={15}
            type="semi-bold"
            style={{
              color: Colors[currentTheme ?? "dark"].primary,
              marginTop: scale(4),
            }}
          >
            {price}
          </ThemedText>
        </View>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: "row",
    alignItems: "center",
    padding: scale(16),
    marginBottom: scale(12),
    borderRadius: scale(16),
    overflow: "hidden",
  },
  imageContainer: {
    width: scale(56),
    height: scale(56),
    borderRadius: scale(12),
    justifyContent: "center",
    alignItems: "center",
    marginRight: scale(16),
  },
  image: {
    width: scale(56),
    height: scale(56),
    borderRadius: scale(12),
  },
  contentContainer: {
    flex: 1,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
  },
  textContainer: {
    flex: 1,
  },
});

export default SearchResultProductItem;
