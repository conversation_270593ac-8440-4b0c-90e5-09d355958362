import { scale } from "@/src/_helper/Scaler";
import { Colors } from "@/src/constants/Colors";
import { useTheme } from "@/src/context/ThemeContext";
import { router } from "expo-router";
import React from "react";
import {
  Image,
  ImageProps,
  StyleSheet,
  TouchableOpacity,
  View,
} from "react-native";
import { ThemedText } from "../ui/ThemedText";

interface SearchResultBrandItemProps {
  id: string;
  image: ImageProps | null;
  title: string;
  description: string;
  productCount?: number;
}

const SearchResultBrandItem: React.FC<SearchResultBrandItemProps> = ({
  id,
  image,
  title,
  description,
  productCount,
}) => {
  const { currentTheme } = useTheme();

  const handlePress = () => {
    router.push(`/(client)/(screens)/brands/${id}/brand-details`);
  };
  console.log(image);
  return (
    <TouchableOpacity
      style={[styles.container]}
      activeOpacity={0.7}
      onPress={handlePress}
    >
      <View style={[styles.imageContainer]}>
        <Image source={image?.source} style={styles.image} resizeMode="cover" />
      </View>

      <View style={styles.contentContainer}>
        <View style={styles.textContainer}>
          <ThemedText size={16} type="semi-bold" numberOfLines={1}>
            {title}
          </ThemedText>
          <ThemedText
            size={14}
            type="regular"
            style={{
              color: Colors[currentTheme ?? "dark"].secondary,
              marginTop: scale(2),
            }}
            numberOfLines={1}
          >
            {description}
          </ThemedText>
          {productCount !== undefined && (
            <ThemedText
              size={12}
              type="regular"
              style={{
                color: Colors[currentTheme ?? "dark"].secondary,
                marginTop: scale(4),
              }}
            >
              {productCount} products
            </ThemedText>
          )}
        </View>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: "row",
    alignItems: "center",
    padding: scale(16),
    marginBottom: scale(12),
    borderRadius: scale(16),
    overflow: "hidden",
  },
  imageContainer: {
    width: scale(56),
    height: scale(56),
    borderRadius: scale(12),
    justifyContent: "center",
    alignItems: "center",
    marginRight: scale(16),
  },
  image: {
    width: scale(56),
    height: scale(56),
    borderRadius: scale(12),
  },
  contentContainer: {
    flex: 1,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
  },
  textContainer: {
    flex: 1,
  },
});

export default SearchResultBrandItem;
