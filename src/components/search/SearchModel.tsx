import { scale } from "@/src/_helper/Scaler";
import { Colors } from "@/src/constants/Colors";
import { useLanguage } from "@/src/context/LanguageContext";
import { useTheme } from "@/src/context/ThemeContext";
import { BlurView } from "expo-blur";
import React, { useRef, useState } from "react";
import {
  Animated,
  Dimensions,
  Keyboard,
  Platform,
  StyleSheet,
  TouchableWithoutFeedback,
  View,
} from "react-native";
import { Portal } from "react-native-paper";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import CustomBackButton from "../buttons/CustomBackButton";
import AnimatedTextInput from "../inputs/AnimatedTextInput";
import SearchResults from "../search/SearchResults";

const { width, height } = Dimensions.get("screen"); // Use screen instead of window for full height

type SearchModalProps = {
  visible: boolean;
  onClose: () => void;
};

const SearchModal: React.FC<SearchModalProps> = ({ visible, onClose }) => {
  const { currentTheme } = useTheme();
  const { t, locale } = useLanguage();
  const overlayAnim = useRef(new Animated.Value(0)).current;
  const insets = useSafeAreaInsets();

  const [searchText, setSearchText] = useState("");
  const [showResults, setShowResults] = useState(false);
  const [keyboardHeight, setKeyboardHeight] = useState(0);
  const [isKeyboardVisible, setIsKeyboardVisible] = useState(false);

  const [latestSearches, setLatestSearches] = useState<string[]>(
    t("search.sampleSearches") as unknown as string[]
  );

  // Keyboard listeners
  React.useEffect(() => {
    const keyboardDidShowListener = Keyboard.addListener(
      "keyboardDidShow",
      (event) => {
        setKeyboardHeight(event.endCoordinates.height);
        setIsKeyboardVisible(true);
      }
    );

    const keyboardDidHideListener = Keyboard.addListener(
      "keyboardDidHide",
      () => {
        setKeyboardHeight(0);
        setIsKeyboardVisible(false);
      }
    );

    return () => {
      keyboardDidShowListener?.remove();
      keyboardDidHideListener?.remove();
    };
  }, []);

  // Show results when user starts typing
  React.useEffect(() => {
    const shouldShowResults = searchText.trim().length > 0;
    setShowResults(shouldShowResults);
  }, [searchText]);

  // Handle keyboard visibility changes and modal state changes
  React.useEffect(() => {
    if (visible) {
      Animated.timing(overlayAnim, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }).start();
    }
  }, [visible, showResults, isKeyboardVisible, overlayAnim]);

  React.useEffect(() => {
    if (visible) {
      Animated.timing(overlayAnim, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }).start();
    } else {
      Animated.timing(overlayAnim, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }).start(() => {
        setSearchText("");
        setShowResults(false);
      });
    }
  }, [visible, overlayAnim]);

  const opacity = overlayAnim;

  if (!visible) return null;

  const onRecentSearchPress = (item: string) => {
    setSearchText(item);
    setShowResults(true);
  };

  // Calculate available height based on keyboard visibility and search state
  const getModalHeight = () => {
    if (showResults) {
      // When showing results, use full available screen space
      if (Platform.OS === "android") {
        // On Android, use full screen height to cover tabs and navigation
        return isKeyboardVisible ? height - keyboardHeight : height;
      } else {
        // On iOS, use window height
        return isKeyboardVisible ? height - keyboardHeight : height;
      }
    } else {
      // When showing recent searches, use compact height
      return height * 0.25;
    }
  };

  const getTranslateY = () => {
    return showResults
      ? isKeyboardVisible
        ? -height + keyboardHeight
        : -height
      : -height * 0.25;
  };

  return (
    <Portal>
      <TouchableWithoutFeedback
        onPress={() => {
          Keyboard.dismiss();
          onClose();
        }}
      >
        <View style={styles.fullScreenWrapper}>
          {/* Overlay background - only show when not in full screen search results */}
          {!(showResults && !isKeyboardVisible) && (
            <Animated.View
              pointerEvents="none"
              style={[
                StyleSheet.absoluteFillObject,
                {
                  opacity,
                  backgroundColor:
                    Platform.OS === "android"
                      ? Colors[currentTheme ?? "dark"].overlay
                      : "transparent",
                  zIndex: 1,
                },
              ]}
            >
              {Platform.OS === "ios" && (
                <BlurView
                  intensity={60}
                  tint={currentTheme === "light" ? "light" : "dark"}
                  style={StyleSheet.absoluteFill}
                />
              )}
            </Animated.View>
          )}

          {/* Animated Top Modal Content */}
          <Animated.View
            style={[
              styles.modal,
              {
                height: getModalHeight(),
                opacity,
                transform: [
                  {
                    translateY: overlayAnim.interpolate({
                      inputRange: [0, 1],
                      outputRange: [getTranslateY(), 0],
                    }),
                  },
                ],
                // On Android full screen, ensure it covers everything
                ...(Platform.OS === "android" &&
                  showResults &&
                  !isKeyboardVisible && {
                    ...StyleSheet.absoluteFillObject,
                    height: height,
                    zIndex: 9999,
                  }),
              },
            ]}
          >
            <TouchableWithoutFeedback>
              <View
                style={[
                  styles.modalContent,
                  {
                    backgroundColor: Colors[currentTheme ?? "dark"].background,
                    paddingTop: insets.top + scale(18), // Always use proper safe area padding
                    paddingHorizontal: scale(17),
                    paddingBottom:
                      showResults && !isKeyboardVisible ? 0 : scale(10),
                  },
                ]}
              >
                <View
                  style={{
                    height: scale(25),
                    flexDirection: "row",
                    alignItems: "center",
                  }}
                >
                  <CustomBackButton onPress={onClose} />
                </View>

                <AnimatedTextInput
                  value={searchText}
                  onChangeText={setSearchText}
                  animatedWords={
                    locale === "en"
                      ? ["products", "brands"]
                      : ["produits", "marques"]
                  }
                  staticPrefix={
                    locale === "en" ? "Search for " : "Rechercher des "
                  }
                />

                <SearchResults
                  searchQuery={searchText}
                  visible={showResults}
                  isFullScreen={showResults && !isKeyboardVisible}
                />
              </View>
            </TouchableWithoutFeedback>
          </Animated.View>
        </View>
      </TouchableWithoutFeedback>
    </Portal>
  );
};

const styles = StyleSheet.create({
  fullScreenWrapper: {
    ...StyleSheet.absoluteFillObject,
    zIndex: 1000,
    justifyContent: "flex-start",
    height: height, // Ensure full screen height
    width: width, // Ensure full screen width
  },
  modal: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    width: width,
    zIndex: 2,
    overflow: "hidden",
  },
  modalContent: {
    flex: 1,
    gap: scale(9),
  },
  latestSearchContainer: {
    flexDirection: "row",
    flexWrap: "wrap",
    marginTop: scale(10),
    gap: scale(10),
  },
  searchChip: {
    paddingVertical: scale(6),
    paddingHorizontal: scale(15),
    borderRadius: scale(20),
    borderWidth: scale(1),
    alignSelf: "flex-start",
  },
});

export default SearchModal;
