import { scale } from "@/src/_helper/Scaler";
import { Colors } from "@/src/constants/Colors";
import { useLanguage } from "@/src/context/LanguageContext";
import { useTheme } from "@/src/context/ThemeContext";
import useSearch from "@/src/services/querys/client/useSearch";
import React, { useMemo } from "react";
import { StyleSheet, View } from "react-native";
import SkeletonSearchResults from "../loader/SkeletonSearchResults";
import { InfiniteScrollList } from "../shared/InfiniteScrollList";
import { ThemedText } from "../ui/ThemedText";
import SearchResultBrandItem from "./SearchResultBrandItem";
import SearchResultProductItem from "./SearchResultProductItem";

import {
  SearchBrand,
  SearchProduct,
  SearchResultItem,
  SearchResultsProps,
} from "@/src/types";

const SearchResults: React.FC<SearchResultsProps> = ({
  searchQuery,
  visible,
  isFullScreen = false,
}) => {
  const { currentTheme } = useTheme();
  const { t } = useLanguage();

  // Use the actual search hook - only when there's a query and component is visible
  const {
    data,
    isLoading,
    isFetchingNextPage,
    hasNextPage,
    fetchNextPage,
    refetch,
    isRefetching,
  } = useSearch("all", visible && searchQuery.trim() ? searchQuery.trim() : "");
  // Process search results from the hook
  const searchResults = useMemo(() => {
    if (!data?.pages) return [];

    const allResults: SearchResultItem[] = [];
    const seenKeys = new Set<string>();

    // Process all pages of data
    data.pages.forEach((page) => {
      const searchResults = page.data.items;

      // Handle if items is an array of search results
      if (Array.isArray(searchResults)) {
        searchResults.forEach((searchData) => {
          // Transform brands
          if (searchData && searchData.brands) {
            const transformedBrands: SearchBrand[] = searchData.brands.map(
              (brand: any) => ({
                id: brand.id,
                type: "brand" as const,
                image: brand.image,
                title: brand.title,
                description: brand.description,
                productCount: brand.productCount,
              })
            );

            transformedBrands.forEach((brand) => {
              const key = `brand-${brand.id}`;
              if (!seenKeys.has(key)) {
                seenKeys.add(key);
                allResults.push(brand);
              }
            });
          }

          // Transform products
          if (searchData && searchData.products) {
            const transformedProducts: SearchProduct[] =
              searchData.products.map((product: any) => ({
                id: product.id,
                type: "product" as const,
                image: product.image,
                title: product.title,
                price: product.price,
                brand: product.brand,
              }));

            transformedProducts.forEach((product) => {
              const key = `product-${product.id}`;
              if (!seenKeys.has(key)) {
                seenKeys.add(key);
                allResults.push(product);
              }
            });
          }
        });
      } else {
        // Handle if items is a single search result object
        const searchData = searchResults as any;

        // Transform brands
        if (searchData && searchData.brands) {
          const transformedBrands: SearchBrand[] = searchData.brands.map(
            (brand: any) => ({
              id: brand.id,
              type: "brand" as const,
              image: brand.image,
              title: brand.title,
              description: brand.description,
              productCount: brand.productCount,
            })
          );

          transformedBrands.forEach((brand) => {
            const key = `brand-${brand.id}`;
            if (!seenKeys.has(key)) {
              seenKeys.add(key);
              allResults.push(brand);
            }
          });
        }

        // Transform products
        if (searchData && searchData.products) {
          const transformedProducts: SearchProduct[] = searchData.products.map(
            (product: any) => ({
              id: product.id,
              type: "product" as const,
              image: product.image,
              title: product.title,
              price: product.price,
              brand: product.brand,
            })
          );

          transformedProducts.forEach((product) => {
            const key = `product-${product.id}`;
            if (!seenKeys.has(key)) {
              seenKeys.add(key);
              allResults.push(product);
            }
          });
        }
      }
    });
    return allResults;
  }, [data]);

  const handleRefresh = () => {
    refetch();
  };

  const handleFetchNextPage = () => {
    if (hasNextPage && !isFetchingNextPage) {
      fetchNextPage();
    }
  };

  const renderItem = ({ item }: { item: SearchResultItem }) => {
    if (item.type === "brand") {
      return (
        <SearchResultBrandItem
          id={item.id}
          image={item.image ? { source: { uri: item.image } } : null}
          title={item.title}
          description={item.description}
          productCount={item.productCount}
        />
      );
    } else {
      return (
        <SearchResultProductItem
          id={item.id}
          image={{ uri: item.image }}
          title={item.title}
          price={item.price}
          brand={item.brand}
        />
      );
    }
  };

  if (!visible || !searchQuery.trim()) {
    return null;
  }

  return (
    <View
      style={[
        styles.container,
        {
          backgroundColor: Colors[currentTheme ?? "dark"].background,
        },
      ]}
    >
      {searchResults.length > 0 && !isLoading && (
        <ThemedText type="semi-bold" size={16} style={[styles.resultsHeader]}>
          {searchResults.length}{" "}
          {searchResults.length === 1
            ? t("search.result")
            : t("search.results")}{" "}
          {t("search.found")}
        </ThemedText>
      )}

      <InfiniteScrollList
        data={searchResults}
        isLoading={isLoading}
        isFetchingNextPage={isFetchingNextPage}
        hasNextPage={hasNextPage}
        fetchNextPage={handleFetchNextPage}
        refreshing={isRefetching}
        onRefresh={handleRefresh}
        estimatedItemSize={88}
        renderItem={renderItem}
        skeletonComponent={<SkeletonSearchResults />}
        emptyComponent={
          !isLoading && searchQuery.trim() ? (
            <View style={[styles.emptyContainer]}>
              <ThemedText type="semi-bold" size={18} style={styles.emptyTitle}>
                {t("search.noResults")}
              </ThemedText>
              <ThemedText
                style={[
                  styles.emptyDescription,
                  { color: Colors[currentTheme ?? "dark"].secondary },
                ]}
              >
                {t("search.tryDifferentKeywords")}
              </ThemedText>
            </View>
          ) : undefined
        }
        count={6}
        keyExtractor={(item, index) => `${item.type}-${item.id}-${index}`}
        showsVerticalScrollIndicator={false}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingTop: scale(10),
  },
  resultsHeader: {
    marginBottom: scale(16),
    marginTop: scale(8),
  },
  emptyContainer: {
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: scale(40),
    paddingTop: scale(60), // Moderate padding to account for search input area
  },
  emptyTitle: {
    marginBottom: scale(8),
  },
  emptyDescription: {
    textAlign: "center",
  },
});

export default SearchResults;
