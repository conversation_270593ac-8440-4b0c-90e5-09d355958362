import { scale } from "@/src/_helper/Scaler";
import { Colors } from "@/src/constants/Colors";
import { useTheme } from "@/src/context/ThemeContext";
import { AntDesign } from "@expo/vector-icons";
import React from "react";
import { Pressable, StyleSheet, View } from "react-native";
import ErrorText from "../shared/ErrorText";

interface StarRatingProps {
  rating: number;
  onRatingChange: (rating: number) => void;
  error?: string;
  disabled?: boolean;
}

export default function StarRating({
  rating,
  onRatingChange,
  error,
  disabled = false,
}: StarRatingProps) {
  const { currentTheme } = useTheme();
  const theme = currentTheme ?? "dark";

  const renderStars = () =>
    Array.from({ length: 5 }, (_, index) => {
      const starNumber = index + 1;
      const isSelected = starNumber <= rating;

      return (
        <Pressable
          key={index}
          onPress={() => !disabled && onRatingChange(starNumber)}
          style={styles.starContainer}
          disabled={disabled}
        >
          <AntDesign
            name="star"
            size={scale(24)}
            color={isSelected ? Colors[theme].star : Colors[theme].no_star}
            style={{ marginRight: scale(2) }}
          />
        </Pressable>
      );
    });

  return (
    <View style={styles.container}>
      <View style={styles.starsContainer}>{renderStars()}</View>
      {error && <ErrorText error={error} />}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    width: "100%",
    alignItems: "flex-start",
  },
  starsContainer: {
    flexDirection: "row",
    alignItems: "center",
    paddingVertical: scale(8),
  },
  starContainer: {
    padding: scale(4),
  },
  errorText: {
    marginTop: scale(4),
    marginLeft: scale(4),
  },
});
