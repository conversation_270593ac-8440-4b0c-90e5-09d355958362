import { scale } from "@/src/_helper/Scaler";
import { Colors } from "@/src/constants/Colors";
import { useLanguage } from "@/src/context/LanguageContext";
import { useTheme } from "@/src/context/ThemeContext";
import { MaterialIcons } from "@expo/vector-icons";
import React from "react";
import { Animated, StyleSheet, View } from "react-native";
import DefaultButton from "../buttons/Default";
import { ThemedText } from "./ThemedText";
import { ThemedView } from "./ThemedView";

interface NetworkErrorComponentProps {
  onRetry: () => void;
}

export const NetworkErrorComponent: React.FC<NetworkErrorComponentProps> = ({
  onRetry,
}) => {
  const { t } = useLanguage();
  const [fadeAnim] = React.useState(new Animated.Value(0));
  const { currentTheme } = useTheme();
  const colors = Colors[currentTheme ?? "dark"];
  React.useEffect(() => {
    Animated.timing(fadeAnim, {
      toValue: 1,
      duration: 500,
      useNativeDriver: true,
    }).start();
  }, [fadeAnim]);

  return (
    <ThemedView style={styles.container}>
      <Animated.View
        style={[
          styles.content,
          { opacity: fadeAnim, backgroundColor: colors.error_background },
        ]}
      >
        <MaterialIcons name="wifi-off" size={64} color={colors.input_text} />
        <View style={styles.textContainer}>
          <ThemedText type="semi-bold" style={styles.titleText}>
            {t("error.network.title")}
          </ThemedText>
          <ThemedText style={styles.errorText}>
            {t("error.network.message")}
          </ThemedText>
        </View>
        <DefaultButton
          title={t("error.network.retry")}
          onPress={onRetry}
          style={styles.retryButton}
        />
      </Animated.View>
    </ThemedView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: scale(24),
  },
  content: {
    alignItems: "center",
    gap: scale(24),
    maxWidth: scale(400),
    width: "100%",
    padding: scale(32),
    borderRadius: scale(16),
  },
  textContainer: {
    alignItems: "center",
    gap: scale(8),
  },
  titleText: {
    textAlign: "center",
  },
  errorText: {
    textAlign: "center",
    opacity: 0.8,
  },
  retryButton: {
    minWidth: scale(160),
    marginTop: scale(8),
  },
});
