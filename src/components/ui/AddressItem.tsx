import { scale, scaleFont } from "@/src/_helper/Scaler";
import { Colors } from "@/src/constants/Colors";
import { useLanguage } from "@/src/context/LanguageContext";
import { useTheme } from "@/src/context/ThemeContext";
import { ILocation } from "@/src/types/location";
import { Ionicons } from "@expo/vector-icons";
import React from "react";
import { StyleSheet, TouchableOpacity, View } from "react-native";
import { ThemedText } from "./ThemedText";

interface AddressItemProps {
  address: ILocation;
  onPress: (address: ILocation) => void;
}

const AddressItem: React.FC<AddressItemProps> = ({ address, onPress }) => {
  const { currentTheme } = useTheme();
  const { t } = useLanguage();

  const getAddressIcon = (type: ILocation["type"]) => {
    switch (type) {
      case "home":
        return "home";
      case "workplace":
        return "briefcase";
      case "other":
        return "location";
      default:
        return "location";
    }
  };

  const getAddressTypeLabel = (type: ILocation["type"]) => {
    switch (type) {
      case "home":
        return t("addresses.types.home");
      case "workplace":
        return t("addresses.types.work");
      case "other":
        return t("addresses.types.other");
      default:
        return t("addresses.types.other");
    }
  };

  // Safety check to ensure address exists and has required properties
  if (!address || !address._id) {
    return null;
  }

  return (
    <TouchableOpacity
      style={styles.addressItem}
      onPress={() => onPress(address)}
    >
      <View
        style={[
          styles.addressIconContainer,
          {
            backgroundColor: Colors[currentTheme ?? "dark"].primary + "20",
          },
        ]}
      >
        <Ionicons
          name={getAddressIcon(address.type) as any}
          size={scaleFont(24)}
          color={Colors[currentTheme ?? "dark"].primary}
        />
      </View>
      <View style={styles.addressDetails}>
        <ThemedText type="semi-bold" size={16}>
          {getAddressTypeLabel(address.type)}
        </ThemedText>
        <ThemedText
          size={14}
          style={[
            styles.addressText,
            {
              color: Colors[currentTheme ?? "dark"].secondary,
            },
          ]}
        >
          {address.address}, {address.city}, {address.state}
        </ThemedText>
      </View>
      <Ionicons
        name="chevron-forward"
        size={scaleFont(20)}
        color={Colors[currentTheme ?? "dark"].secondary}
      />
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  addressItem: {
    flexDirection: "row",
    alignItems: "center",
    paddingVertical: scale(16),
  },
  addressIconContainer: {
    width: scale(40),
    height: scale(40),
    borderRadius: scale(20),
    justifyContent: "center",
    alignItems: "center",
    marginRight: scale(12),
  },
  addressDetails: {
    flex: 1,
  },
  addressText: {
    marginTop: scale(2),
  },
});

export default AddressItem;
