import { scale, scaleFont } from "@/src/_helper/Scaler";
import { ThemedText } from "@/src/components/ui/ThemedText";
import { Colors } from "@/src/constants/Colors";
import { useTheme } from "@/src/context/ThemeContext";
import { MaterialIcons } from "@expo/vector-icons";
import React, { memo } from "react";
import { StyleSheet, TouchableOpacity, View } from "react-native";

type ThemeOptionType = {
  themeKey: "light" | "dark" | "system";
  icon: keyof typeof MaterialIcons.glyphMap;
  selected: boolean;
  onPress: () => void;
  label: string;
  description: string;
};

const ThemeOptionItem = memo(
  ({
    themeKey,
    icon,
    selected,
    onPress,
    label,
    description,
  }: ThemeOptionType) => {
    const { currentTheme } = useTheme();
    const colors = Colors[currentTheme ?? "dark"];

    return (
      <TouchableOpacity onPress={onPress} activeOpacity={0.7}>
        <View
          style={[
            styles.themeItem,
            selected && {
              borderColor: colors.secondary,
              borderWidth: scale(2),
            },
          ]}
        >
          <View style={styles.themeHeader}>
            <MaterialIcons
              name={icon}
              size={scaleFont(24)}
              color={selected ? colors.secondary : colors.text}
            />
            <ThemedText size={18}>{label}</ThemedText>
          </View>
          <ThemedText
            style={[styles.themeDescription, { color: colors.primary }]}
          >
            {description}
          </ThemedText>
          {selected && (
            <View style={styles.selectedIndicator}>
              <MaterialIcons
                name="check-circle"
                size={scaleFont(24)}
                color={colors.secondary}
              />
            </View>
          )}
        </View>
      </TouchableOpacity>
    );
  }
);

// Fix ESLint react/display-name by explicitly setting displayName
ThemeOptionItem.displayName = "ThemeOptionItem";

const styles = StyleSheet.create({
  themeItem: {
    padding: scale(16),
    borderRadius: scale(12),
    borderWidth: scale(2),
    borderColor: "transparent",
    position: "relative",
  },
  themeHeader: {
    flexDirection: "row",
    alignItems: "center",
    gap: scale(12),
    marginBottom: scale(8),
  },

  themeDescription: {
    opacity: 0.7,
  },
  selectedIndicator: {
    position: "absolute",
    top: scale(16),
    right: scale(16),
  },
});

export default ThemeOptionItem;
