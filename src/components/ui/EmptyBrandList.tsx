import { scale } from "@/src/_helper/Scaler";
import { ThemedText } from "@/src/components/ui/ThemedText";
import { Colors } from "@/src/constants/Colors";
import { useLanguage } from "@/src/context/LanguageContext";
import { useTheme } from "@/src/context/ThemeContext";
import React from "react";
import { Image, StyleSheet, View } from "react-native";

const EmptyBrandList: React.FC = () => {
  const { currentTheme } = useTheme();
  const { t } = useLanguage();

  return (
    <View style={styles.container}>
      <Image
        source={require("@/src/assets/ui/empty_brand.png")}
        style={styles.image}
        resizeMode="contain"
      />
      <ThemedText type="bold" size={18} style={styles.title}>
        {t("ui.emptyBrand.title")}
      </ThemedText>
      <ThemedText
        style={{
          textAlign: "center",
          color: Colors[currentTheme ?? "dark"].secondary,
        }}
      >
        {t("ui.emptyBrand.noFavorites")}
      </ThemedText>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingVertical: scale(12),
    alignItems: "center",
    justifyContent: "center",
  },
  image: {
    width: "100%",
    height: scale(201),
    marginBottom: scale(24),
  },
  title: {
    marginBottom: scale(8),
  },
});

export default EmptyBrandList;
