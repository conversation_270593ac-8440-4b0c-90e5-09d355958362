import { scale } from "@/src/_helper/Scaler";
import { Colors } from "@/src/constants/Colors";
import { useLanguage } from "@/src/context/LanguageContext";
import { useTheme } from "@/src/context/ThemeContext";
import { MaterialIcons } from "@expo/vector-icons";
import React from "react";
import { Animated, StyleSheet, View } from "react-native";
import { ThemedText } from "./ThemedText";
import { ThemedView } from "./ThemedView";

export const ErrorComponentSmall: React.FC = ({}) => {
  const { t } = useLanguage();
  const { currentTheme } = useTheme();
  const colors = Colors[currentTheme ?? "dark"];
  const [fadeAnim] = React.useState(new Animated.Value(0));

  React.useEffect(() => {
    Animated.timing(fadeAnim, {
      toValue: 1,
      duration: 500,
      useNativeDriver: true,
    }).start();
  }, []);

  return (
    <ThemedView style={styles.container}>
      <Animated.View
        style={[
          styles.content,
          { opacity: fadeAnim, backgroundColor: colors.error_background },
        ]}
      >
        <MaterialIcons name="error-outline" size={64} color={colors.error} />
        <View style={styles.textContainer}>
          <ThemedText style={styles.titleText} size={18}>
            {t("error.generic.title")}
          </ThemedText>
        </View>
      </Animated.View>
    </ThemedView>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: scale(10),
  },
  content: {
    alignItems: "center",
    gap: scale(12),
    maxWidth: scale(400),
    width: "100%",
    padding: scale(32),
    borderRadius: scale(16),
  },
  textContainer: {
    alignItems: "center",
    gap: scale(8),
  },
  titleText: {
    textAlign: "center",
    marginBottom: scale(4),
  },
  errorText: {
    textAlign: "center",
    opacity: 0.8,
    lineHeight: scale(22),
  },
  retryButton: {
    minWidth: scale(160),
    marginTop: scale(8),
  },
});
