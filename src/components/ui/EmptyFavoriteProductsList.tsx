import { scale } from "@/src/_helper/Scaler";
import React from "react";
import { Image, StyleSheet } from "react-native";

const EmptyFavoriteProductsList: React.FC = () => {
  return (
    <Image
      source={require("@/src/assets/ui/empty_fav.png")}
      style={styles.image}
      resizeMode="contain"
    />
  );
};

const styles = StyleSheet.create({
  image: {
    width: scale(350),
    height: scale(201),
  },
});

export default EmptyFavoriteProductsList;
