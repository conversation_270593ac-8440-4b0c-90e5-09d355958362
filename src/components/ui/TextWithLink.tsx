import React from "react";

import { scale } from "@/src/_helper/Scaler";
import { Colors } from "@/src/constants/Colors";
import { useLanguage } from "@/src/context/LanguageContext";
import { useTheme } from "@/src/context/ThemeContext";
import { router } from "expo-router";
import { StyleSheet, TouchableOpacity } from "react-native";
import { ThemedText } from "./ThemedText";
import { ThemedView } from "./ThemedView";

type TextWithLinkProp = {
  link: string;
  text: string;
  href: any;
  textTranslationKey?: string;
  linkTranslationKey?: string;
  type: "client" | "vendor";
};

const TextWithLink = ({
  text,
  link,
  href,
  textTranslationKey,
  linkTranslationKey,
  type,
}: TextWithLinkProp) => {
  const { t } = useLanguage();
  const { currentTheme } = useTheme();
  return (
    <ThemedView style={styles.signupContainer}>
      <TouchableOpacity
        onPress={() =>
          router.push({
            pathname: href,
            params: {
              type: type,
            },
          })
        }
        activeOpacity={0.75}
      >
        <ThemedText
          style={{
            color: Colors[currentTheme ?? "dark"].secondary,
          }}
        >
          {textTranslationKey ? t(textTranslationKey) : text}{" "}
          <ThemedText
            style={{
              color: Colors[currentTheme ?? "dark"].secondary,
              textDecorationLine: "underline",
            }}
          >
            {linkTranslationKey ? t(linkTranslationKey) : link}
          </ThemedText>
        </ThemedText>
      </TouchableOpacity>
    </ThemedView>
  );
};

export default TextWithLink;
const styles = StyleSheet.create({
  signupContainer: {
    flexDirection: "row",
    justifyContent: "flex-start",
    alignItems: "center",
    marginTop: scale(10),
    flexWrap: "wrap",
    paddingHorizontal: scale(3),
    marginBottom: scale(10),
  },
});
