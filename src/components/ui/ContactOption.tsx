import { scale, scaleFont } from "@/src/_helper/Scaler";
import { ThemedText } from "@/src/components/ui/ThemedText";
import { Colors } from "@/src/constants/Colors";
import { useTheme } from "@/src/context/ThemeContext";
import SimpleLineIcons from "@expo/vector-icons/SimpleLineIcons";
import React from "react";
import { StyleSheet, TouchableOpacity, View } from "react-native";

type ContactOptionProps = {
  icon: keyof typeof SimpleLineIcons.glyphMap;
  title: string;
  description: string;
  onPress?: () => void;
};

const styles = StyleSheet.create({
  container: {
    flexDirection: "row",
    alignItems: "center",
    paddingVertical: scale(12),
    gap: scale(20),
  },
  iconWrapper: {
    height: scale(48),
    width: scale(48),
    borderRadius: 8,
    justifyContent: "center",
    alignItems: "center",
  },
  descriptionText: {
    marginTop: scale(6),
  },
});

const ContactOption = ({
  icon,
  title,
  description,
  onPress,
}: ContactOptionProps) => {
  const { currentTheme } = useTheme();
  const color = Colors[currentTheme ?? "dark"];

  return (
    <TouchableOpacity
      onPress={onPress}
      activeOpacity={onPress ? 0.7 : 1}
      disabled={!onPress}
      accessibilityRole="button"
      accessibilityLabel={title}
      accessibilityHint={description}
      style={styles.container}
    >
      <View style={[styles.iconWrapper, { backgroundColor: color.thirdary }]}>
        <SimpleLineIcons name={icon} size={scaleFont(22)} color={color.text} />
      </View>

      <View style={{ flex: 1 }}>
        <ThemedText type="bold">{title}</ThemedText>
        <ThemedText style={[styles.descriptionText, { color: color.primary }]}>
          {description}
        </ThemedText>
      </View>
    </TouchableOpacity>
  );
};

export default ContactOption;
