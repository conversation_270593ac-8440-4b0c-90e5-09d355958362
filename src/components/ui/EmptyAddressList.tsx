import { scale, scaleFont } from "@/src/_helper/Scaler";
import { ThemedText } from "@/src/components/ui/ThemedText";
import { ThemedView } from "@/src/components/ui/ThemedView";
import { Colors } from "@/src/constants/Colors";
import { useLanguage } from "@/src/context/LanguageContext";
import { useTheme } from "@/src/context/ThemeContext";
import { Ionicons } from "@expo/vector-icons";
import React from "react";
import { Image, StyleSheet } from "react-native";
import DefaultButton from "../buttons/Default";

interface EmptyAddressListProps {
  onAddNewAddress: () => void;
}

const EmptyAddressList: React.FC<EmptyAddressListProps> = ({
  onAddNewAddress,
}) => {
  const { currentTheme } = useTheme();
  const { t } = useLanguage();

  return (
    <ThemedView style={styles.container}>
      <Image
        source={require("@/src/assets/ui/empty_address.png")}
        style={styles.image}
        resizeMode="contain"
      />

      <ThemedText type="bold" size={18} style={styles.title}>
        {t("addresses.empty.title")}
      </ThemedText>

      <ThemedText
        style={[
          styles.description,
          {
            color: Colors[currentTheme ?? "dark"].secondary,
          },
        ]}
      >
        {t("addresses.empty.description")}
      </ThemedText>

      <DefaultButton
        title={t("addresses.actions.addAddress")}
        onPress={onAddNewAddress}
        style={styles.addButton}
      >
        <Ionicons
          name="add"
          size={scaleFont(20)}
          color={
            currentTheme === "dark" ? Colors.dark.black : Colors.light.white
          }
        />
      </DefaultButton>
    </ThemedView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
    paddingHorizontal: scale(20),
  },
  image: {
    width: "100%",
    height: scale(200),
    marginBottom: scale(24),
  },
  title: {
    marginBottom: scale(8),
    textAlign: "center",
  },
  description: {
    textAlign: "center",
    marginBottom: scale(24),
    lineHeight: scale(20),
  },
  addButton: {
    width: "100%",
  },
});

export default EmptyAddressList;
