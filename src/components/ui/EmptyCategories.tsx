import { scale } from "@/src/_helper/Scaler";
import { ThemedText } from "@/src/components/ui/ThemedText";
import { Colors } from "@/src/constants/Colors";
import { useTheme } from "@/src/context/ThemeContext";
import React from "react";
import { Image, StyleSheet, View } from "react-native";

const EmptyCategories: React.FC = () => {
  const { currentTheme } = useTheme();

  return (
    <View style={styles.container}>
      <Image
        source={require("@/src/assets/ui/empty_brand.png")}
        style={styles.image}
        resizeMode="contain"
      />
      <ThemedText type="bold" size={18} style={styles.title}>
        No categories added yet
      </ThemedText>
      <ThemedText
        style={{
          textAlign: "center",
          color: Colors[currentTheme ?? "dark"].secondary,
        }}
      >
        We are sorry but there is no categories to display.Please try again
        later.
      </ThemedText>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingVertical: scale(12),
    alignItems: "center",
    justifyContent: "center",
  },
  image: {
    width: "100%",
    height: scale(201),
    marginBottom: scale(24),
  },
  title: {
    marginBottom: scale(8),
  },
});

export default EmptyCategories;
