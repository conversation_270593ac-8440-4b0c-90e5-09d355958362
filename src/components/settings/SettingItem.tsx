import { scale, scaleFont } from "@/src/_helper/Scaler";
import { ThemedText } from "@/src/components/ui/ThemedText";
import { ThemedView } from "@/src/components/ui/ThemedView";
import { Colors } from "@/src/constants/Colors";
import { useTheme } from "@/src/context/ThemeContext";
import { FontAwesome5, MaterialIcons } from "@expo/vector-icons";
import React from "react";
import { StyleSheet, TouchableOpacity, View } from "react-native";

export const SettingItem = ({
  title,
  onPress,
  icon = "account-circle",
}: {
  title: string;
  onPress: () => void;
  icon: any;
}) => {
  const { currentTheme } = useTheme();
  const colors = Colors[currentTheme ?? "dark"];

  return (
    <TouchableOpacity onPress={onPress}>
      <ThemedView style={styles.settingItem}>
        <View style={styles.settingText}>
          <View style={{ width: scale(36) }}>
            <FontAwesome5
              name={icon}
              size={scaleFont(18)}
              color={colors.text}
            />
          </View>
          <ThemedText size={16}>{title}</ThemedText>
        </View>
        <MaterialIcons
          name="chevron-right"
          size={scaleFont(24)}
          color={colors.text}
        />
      </ThemedView>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  settingItem: {
    flexDirection: "row",
    alignItems: "center",
    paddingVertical: scale(6),
    borderRadius: scale(12),
  },
  settingText: {
    flex: 1,
    flexDirection: "row",
    alignItems: "center",
  },

  settingSubtitle: {
    opacity: 0.6,
    marginTop: scale(2),
  },
});
