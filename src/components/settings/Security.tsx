import { scale } from "@/src/_helper/Scaler";
import { showToast } from "@/src/_helper/toast/showToast";
import DefaultButton from "@/src/components/buttons/Default";
import TextInput from "@/src/components/inputs/TextInput";
import { ThemedText } from "@/src/components/ui/ThemedText";
import { useLanguage } from "@/src/context/LanguageContext";
import useChangePassword from "@/src/services/querys/profile/useChangePassword";
import React, { useState } from "react";
import { ScrollView, StyleSheet, View } from "react-native";

export default function SecuritySettings() {
  const { t } = useLanguage();
  const [currentPassword, setCurrentPassword] = useState("");
  const [newPassword, setNewPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [currentPasswordError, setCurrentPasswordError] = useState("");
  const [newPasswordError, setNewPasswordError] = useState("");
  const [confirmPasswordError, setConfirmPasswordError] = useState("");

  const mutation = useChangePassword();

  const handleChangePassword = async () => {
    setCurrentPasswordError("");
    setNewPasswordError("");
    setConfirmPasswordError("");

    let isValid = true;

    if (currentPassword.length < 8) {
      setCurrentPasswordError("settings.security.changePassword.errors.length");
      isValid = false;
    }

    if (newPassword.length < 8) {
      setNewPasswordError("settings.security.changePassword.errors.length");
      isValid = false;
    }

    if (newPassword !== confirmPassword) {
      setConfirmPasswordError(
        "settings.security.changePassword.errors.mismatch"
      );
      isValid = false;
    }

    if (!isValid) return;
    await mutation.mutateAsync({
      confirmNewPassword: confirmPassword,
      newPassword: newPassword,
      oldPassword: currentPassword,
    });

    setCurrentPassword("");
    setNewPassword("");
    setConfirmPassword("");
  };

  return (
    <>
      <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
        <View style={styles.header}>
          <ThemedText size={24}>{t("settings.security.title")}</ThemedText>
        </View>

        <View style={styles.section}>
          <ThemedText style={styles.sectionTitle} size={18}>
            {t("settings.security.changePassword.title")}
          </ThemedText>
          <View style={styles.form}>
            <TextInput
              value={currentPassword}
              onChangeText={(text) => {
                setCurrentPassword(text);
                setCurrentPasswordError("");
              }}
              placeholder={t(
                "settings.security.changePassword.currentPassword.placeholder"
              )}
              isPassword
              error={currentPasswordError}
            />

            <TextInput
              value={newPassword}
              onChangeText={(text) => {
                setNewPassword(text);
                setNewPasswordError("");
              }}
              placeholder={t(
                "settings.security.changePassword.newPassword.placeholder"
              )}
              isPassword
              error={newPasswordError}
            />

            <TextInput
              value={confirmPassword}
              onChangeText={(text) => {
                setConfirmPassword(text);
                setConfirmPasswordError("");
              }}
              placeholder={t(
                "settings.security.changePassword.confirmPassword.placeholder"
              )}
              isPassword
              error={confirmPasswordError}
            />
          </View>

          <DefaultButton
            title={t("settings.security.changePassword.button")}
            onPress={handleChangePassword}
            style={styles.button}
            isSubmitting={mutation.isPending}
            disabled={mutation.isPending}
          />
        </View>

        <View style={styles.section}>
          <ThemedText style={styles.sectionTitle} size={18}>
            {t("settings.security.twoFactor.title")}
          </ThemedText>
          <ThemedText style={styles.description}>
            {t("settings.security.twoFactor.description")}
          </ThemedText>
          <DefaultButton
            title={t("settings.security.twoFactor.button")}
            onPress={() => {
              showToast(
                t("settings.security.twoFactor.title"),
                t("settings.security.twoFactor.comming"),
                "info"
              );
            }}
            style={styles.button}
          />
        </View>
      </ScrollView>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    marginBottom: scale(20),
  },

  section: {
    marginBottom: scale(32),
  },
  sectionTitle: {
    marginBottom: scale(12),
  },
  description: {
    opacity: 0.7,
    marginBottom: scale(16),
  },
  form: {
    gap: scale(16),
    marginBottom: scale(24),
  },
  button: {
    marginTop: scale(8),
  },
});
