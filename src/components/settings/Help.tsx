import { scale } from "@/src/_helper/Scaler";
import { ThemedText } from "@/src/components/ui/ThemedText";
import { ThemedView } from "@/src/components/ui/ThemedView";
import { Colors } from "@/src/constants/Colors";
import { useLanguage } from "@/src/context/LanguageContext";
import { useTheme } from "@/src/context/ThemeContext";
import { MaterialIcons } from "@expo/vector-icons";
import React, { useState } from "react";
import {
  Linking,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  View,
} from "react-native";

type FAQItem = {
  id: string;
  key: "hotelListings" | "complaints" | "services" | "orders" | "account";
  isExpanded: boolean;
};

type HelpCategory = {
  id: string;
  key: "support" | "documentation" | "tutorials";
  icon: keyof typeof MaterialIcons.glyphMap;
};

export default function HelpSettings() {
  const { currentTheme } = useTheme();
  const colors = Colors[currentTheme ?? "dark"];
  const { t } = useLanguage();

  const [faqs, setFaqs] = useState<FAQItem[]>([
    {
      id: "1",
      key: "hotelListings",
      isExpanded: false,
    },
    {
      id: "2",
      key: "orders",
      isExpanded: false,
    },
    {
      id: "3",
      key: "account",
      isExpanded: false,
    },
    {
      id: "4",
      key: "complaints",
      isExpanded: false,
    },
    {
      id: "5",
      key: "services",
      isExpanded: false,
    },
  ]);

  const helpCategories: HelpCategory[] = [
    {
      id: "1",
      key: "support",
      icon: "support-agent",
    } /*  {
      id: "2",
      key: "documentation",
      icon: "description",
    },
    {
      id: "3",
      key: "tutorials",
      icon: "play-circle",
    }, */,
  ];

  const toggleFAQ = (id: string) => {
    setFaqs(
      faqs.map((faq) =>
        faq.id === id ? { ...faq, isExpanded: !faq.isExpanded } : faq
      )
    );
  };

  return (
    <>
      <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
        <View style={styles.header}>
          <ThemedText style={styles.title} size={24}>
            {t("helpSettings.title")}
          </ThemedText>
          <ThemedText style={styles.subtitle} size={16}>
            {t("helpSettings.subtitle")}
          </ThemedText>
        </View>

        <View style={styles.categories}>
          {helpCategories.map((category) => (
            <TouchableOpacity
              key={category.id}
              onPress={() =>
                //send email to
                Linking.openURL(
                  `mailto:<EMAIL>?subject=Help%20Request`
                )
              }
            >
              <ThemedView style={styles.categoryCard}>
                <MaterialIcons
                  name={category.icon}
                  size={scale(32)}
                  color={colors.primary}
                />
                <ThemedText style={styles.categoryTitle} size={16}>
                  {t(`helpSettings.categories.${category.key}.title`)}
                </ThemedText>
                <ThemedText style={styles.categoryDescription} size={12}>
                  {t(`helpSettings.categories.${category.key}.description`)}
                </ThemedText>
              </ThemedView>
            </TouchableOpacity>
          ))}
        </View>

        <View style={styles.faqSection}>
          <ThemedText size={20}>{t("helpSettings.faq.title")}</ThemedText>
          <View style={styles.faqList}>
            {faqs.map((faq) => (
              <TouchableOpacity key={faq.id} onPress={() => toggleFAQ(faq.id)}>
                <ThemedView style={styles.faqItem}>
                  <View style={styles.faqHeader}>
                    <ThemedText style={styles.faqQuestion} size={16}>
                      {t(`helpSettings.faq.items.${faq.key}.question`)}
                    </ThemedText>
                    <MaterialIcons
                      name={faq.isExpanded ? "expand-less" : "expand-more"}
                      size={scale(24)}
                      color={colors.text}
                    />
                  </View>
                  {faq.isExpanded && (
                    <ThemedText style={styles.faqAnswer}>
                      {t(`helpSettings.faq.items.${faq.key}.answer`)}
                    </ThemedText>
                  )}
                </ThemedView>
              </TouchableOpacity>
            ))}
          </View>
        </View>
      </ScrollView>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    marginBottom: scale(24),
  },
  title: {
    marginBottom: scale(8),
  },
  subtitle: {
    opacity: 0.7,
  },
  categories: {
    flexDirection: "row",
    flexWrap: "wrap",
    gap: scale(12),
    marginBottom: scale(32),
  },
  categoryCard: {
    width: scale(156),
    padding: scale(16),
    borderRadius: scale(12),
    alignItems: "center",
    gap: scale(8),
  },
  categoryTitle: {
    textAlign: "center",
  },
  categoryDescription: {
    opacity: 0.7,
    textAlign: "center",
  },
  faqSection: {
    gap: scale(16),
  },

  faqList: {
    gap: scale(12),
  },
  faqItem: {
    padding: scale(16),
    borderRadius: scale(12),
  },
  faqHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  faqQuestion: {
    flex: 1,
    marginRight: scale(16),
  },
  faqAnswer: {
    opacity: 0.7,
    marginTop: scale(12),
    lineHeight: scale(20),
  },
});
