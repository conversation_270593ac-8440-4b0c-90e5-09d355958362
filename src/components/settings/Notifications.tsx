import { scale } from "@/src/_helper/Scaler";
import Loader from "@/src/components/loader/Loader";
import { ErrorComponent } from "@/src/components/ui/ErrorComponent";
import { ThemedText } from "@/src/components/ui/ThemedText";
import { ThemedView } from "@/src/components/ui/ThemedView";
import { Colors } from "@/src/constants/Colors";
import { useSession } from "@/src/context/AuthContext";
import { useLanguage } from "@/src/context/LanguageContext";
import { useTheme } from "@/src/context/ThemeContext";
import { useNotification } from "@/src/hooks/useNotification";
import { client } from "@/src/services/api";
import useGetProfile from "@/src/services/querys/profile/useGetProfile";
import { IProfile } from "@/src/types"; // Import your IProfile interface
import { useMutation, useQueryClient } from "@tanstack/react-query";
import * as Notifications from "expo-notifications";
import React, { useEffect, useState } from "react";
import {
  RefreshControl,
  ScrollView,
  StyleSheet,
  Switch,
  View,
} from "react-native";

type NotificationSetting = {
  id: string;
  key:
    | "pushNotifications"
    | "emailNotifications"
    | "bookingUpdates"
    | "newMessages"
    | "marketing";
  enabled: boolean;
};

const keyToBackendField: Record<NotificationSetting["key"], keyof IProfile> = {
  pushNotifications: "notification",
  emailNotifications: "emailNotification",
  bookingUpdates: "bookingUpdate",
  newMessages: "newMessage",
  marketing: "marketing",
};

export default function NotificationSettings() {
  const { currentTheme } = useTheme();
  const { session, isLoading: isSessionLoading } = useSession();
  const queryClient = useQueryClient();
  const colors = Colors[currentTheme ?? "dark"];
  const { t } = useLanguage();
  const { hasNotificationPermission, deviceInfo } = useNotification();
  const {
    data,
    isLoading: isLoadingProfile,
    error,
    refetch,
  } = useGetProfile(session?.userId, deviceInfo);
  const [refreshing, setRefreshing] = useState(false);
  const [settings, setSettings] = useState<NotificationSetting[]>([]);
  const handleRefetch = async () => await refetch();
  // Handle pull-to-refresh
  const onRefresh = async () => {
    setRefreshing(true);
    try {
      await refetch();
    } finally {
      setRefreshing(false);
    }
  };

  // Initialize settings from backend data
  useEffect(() => {
    if (data?.data) {
      setSettings([
        {
          id: "1",
          key: "pushNotifications",
          enabled: data.data.notification,
        },
        {
          id: "2",
          key: "emailNotifications",
          enabled: data.data.emailNotification,
        },
        {
          id: "3",
          key: "bookingUpdates",
          enabled: data.data.bookingUpdate,
        },
        {
          id: "4",
          key: "newMessages",
          enabled: data.data.newMessage,
        },
        {
          id: "5",
          key: "marketing",
          enabled: data.data.marketing,
        },
      ]);
    }
  }, [data?.data]);

  // Show loading state

  const { mutate: updateProfile } = useMutation({
    mutationFn: async (updateData: Partial<IProfile>) => {
      const response = await client.patch(
        `/profile/profile/${session?.userId}/${deviceInfo?.deviceId}`,
        updateData
      );
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["profile", session?.userId],
      });
    },
  });

  const toggleSetting = async (key: NotificationSetting["key"]) => {
    const currentSetting = settings.find((s) => s.key === key);
    if (!currentSetting) return;

    // Check for notification permission if enabling push notifications or new messages
    if (
      (key === "pushNotifications" || key === "newMessages") &&
      !currentSetting.enabled
    ) {
      if (!hasNotificationPermission) {
        const { status: newStatus } =
          await Notifications.requestPermissionsAsync();
        if (newStatus !== "granted") {
          return;
        }
      }
    }

    const newValue = !currentSetting.enabled;
    const backendField = keyToBackendField[key];

    // Optimistic UI update
    setSettings((prev) =>
      prev.map((setting) =>
        setting.key === key ? { ...setting, enabled: newValue } : setting
      )
    );

    // Update backend
    updateProfile({ [backendField]: newValue });
  };
  if (isSessionLoading || isLoadingProfile) {
    return <Loader />;
  }

  // Show error state
  if (error || !data?.data) {
    return (
      <>
        <ErrorComponent
          error={t("backend.server_error")}
          onRetry={handleRefetch}
        />
      </>
    );
  }

  return (
    <>
      <ScrollView
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        style={styles.container}
        showsVerticalScrollIndicator={false}
      >
        <View style={styles.header}>
          <ThemedText size={24}>{t("notifications.title")}</ThemedText>
        </View>

        <View style={styles.settingsList}>
          {settings.map((setting) => (
            <ThemedView key={setting.id} style={styles.settingItem}>
              <View style={styles.settingInfo}>
                <ThemedText
                  style={styles.settingTitle}
                  type="semi-bold"
                  size={16}
                >
                  {t(`notifications.${setting.key}`)}
                </ThemedText>
                <ThemedText style={styles.settingDescription}>
                  {t(`notifications.${setting.key}Description`)}
                </ThemedText>
              </View>
              <Switch
                value={setting.enabled}
                onValueChange={() => toggleSetting(setting.key)}
                trackColor={{ false: colors.disabled, true: colors.input_text }}
                thumbColor={colors.white}
              />
            </ThemedView>
          ))}
        </View>
      </ScrollView>
    </>
  );
}
const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    marginBottom: scale(20),
  },
  settingsList: {
    gap: scale(12),
  },
  settingItem: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingVertical: scale(16),
    borderRadius: scale(12),
  },
  settingInfo: {
    flex: 1,
    marginRight: scale(16),
  },
  settingTitle: {
    marginBottom: scale(4),
  },
  settingDescription: {
    opacity: 0.6,
  },
});
