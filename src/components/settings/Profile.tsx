import DefaultButton from "@/src/components/buttons/Default";
import { DeleteConfirmationModal } from "@/src/components/model/DeleteConfirmationModal";
import SettingsProfileInfo from "@/src/components/shared/SettingsProfileInfo";
import { Colors } from "@/src/constants/Colors";
import { useSession } from "@/src/context/AuthContext";
import { useLanguage } from "@/src/context/LanguageContext";
import { useTheme } from "@/src/context/ThemeContext";
import { MaterialIcons } from "@expo/vector-icons";
import React, { useState } from "react";
import { StyleSheet, View } from "react-native";

export default function ProfileSettings() {
  const { signOut } = useSession();
  const { t } = useLanguage();
  const [isDeleteModalVisible, setDeleteModalVisible] = useState(false);
  const { currentTheme } = useTheme();
  const colors = Colors[currentTheme ?? "dark"];
  return (
    <>
      <View
        style={styles.container}
        accessible
        accessibilityLabel={t("settings.profile.accessibilityLabel")}
      >
        <View>
          <SettingsProfileInfo />
        </View>
        <View>
          <DefaultButton
            onPress={signOut}
            title={t("common.logout")}
            color={colors.primary}
          >
            <MaterialIcons name="logout" size={24} color="white" />
          </DefaultButton>

          <DefaultButton
            onPress={() => setDeleteModalVisible(true)}
            title={t("common.delete.title")}
            color={colors.error}
          >
            <MaterialIcons name="delete" size={24} color="white" />
          </DefaultButton>
        </View>
      </View>

      <DeleteConfirmationModal
        visible={isDeleteModalVisible}
        onClose={() => setDeleteModalVisible(false)}
        onConfirm={signOut}
      />
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flexGrow: 1,
    justifyContent: "space-between",
  },
});
