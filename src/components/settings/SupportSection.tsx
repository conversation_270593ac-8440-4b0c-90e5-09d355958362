import { scale } from "@/src/_helper/Scaler";
import { useLanguage } from "@/src/context/LanguageContext";
import { Href, useRouter } from "expo-router";
import React from "react";
import { StyleSheet, View } from "react-native";
import { SettingItem } from "./SettingItem";

export const SupportSection = ({
  role = "client",
}: {
  role?: "client" | "vendor";
}) => {
  const router = useRouter();
  const { t } = useLanguage();

  const route = (path: string) => `/(${role})/(settings)/${path}` as Href;
  return (
    <View style={styles.section}>
      <View style={styles.settingsList}>
        <SettingItem
          title={t("settings.sections.support.help.title")}
          onPress={() => router.push(route("help"))}
          icon="question"
        />
        <SettingItem
          title={t("settings.sections.support.about.title")}
          onPress={() => router.push(route("about"))}
          icon="info"
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  section: {
    marginVertical: scale(12),
  },
  sectionTitle: {
    marginBottom: scale(12),
    opacity: 0.8,
  },
  settingsList: {
    gap: scale(8),
  },
});
