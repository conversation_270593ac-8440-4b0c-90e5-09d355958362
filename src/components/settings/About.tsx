import { scale, scaleFont } from "@/src/_helper/Scaler";
import { ThemedText } from "@/src/components/ui/ThemedText";
import { ThemedView } from "@/src/components/ui/ThemedView";
import { Colors } from "@/src/constants/Colors";
import { useLanguage } from "@/src/context/LanguageContext";
import { useTheme } from "@/src/context/ThemeContext";
import { MaterialIcons } from "@expo/vector-icons";
import React from "react";
import {
  Image,
  Linking,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  View,
} from "react-native";

type SocialLink = {
  id: string;
  key: "website" | "twitter" | "instagram";
  icon: keyof typeof MaterialIcons.glyphMap;
  url: string;
};

export default function AboutSettings() {
  const { currentTheme } = useTheme();
  const colors = Colors[currentTheme ?? "dark"];
  const { t } = useLanguage();

  const appVersion = "1.0.0";
  const buildNumber = "100";

  const socialLinks: SocialLink[] = [
    {
      id: "1",
      key: "website",
      icon: "language",
      url: "https://www.hotelna.info",
    },
  ];

  const handleLinkPress = (url: string) => {
    Linking.openURL(url);
  };

  return (
    <>
      <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
        <View style={styles.header}>
          <Image
            source={require("@/src/assets/images/icon.png")}
            style={styles.logo}
            resizeMode={"contain"}
          />
          <ThemedText style={styles.title} size={28}>
            Locasa
          </ThemedText>
          <ThemedText style={styles.version}>
            {t("about.version", { version: appVersion, buildNumber })}
          </ThemedText>
        </View>

        <View style={styles.section}>
          <ThemedText style={styles.sectionTitle} size={20}>
            {t("about.title")}
          </ThemedText>
          <ThemedView style={styles.contentCard}>
            <ThemedText style={styles.description}>
              {t("about.description")}
            </ThemedText>
          </ThemedView>
        </View>

        <View style={styles.section}>
          <ThemedText style={styles.sectionTitle} size={20}>
            {t("about.connect")}
          </ThemedText>
          <View style={styles.socialLinks}>
            {socialLinks.map((link) => (
              <TouchableOpacity
                key={link.id}
                onPress={() => handleLinkPress(link.url)}
              >
                <ThemedView style={styles.socialLink}>
                  <MaterialIcons
                    name={link.icon}
                    size={scaleFont(24)}
                    color={colors.primary}
                  />
                  <ThemedText size={16}>
                    {t(`about.social.${link.key}`)}
                  </ThemedText>
                </ThemedView>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        <View style={styles.section}>
          <ThemedText style={styles.sectionTitle} size={20}>
            {t("about.legal.title")}
          </ThemedText>
          <View style={styles.legalLinks}>
            <TouchableOpacity
              onPress={() =>
                handleLinkPress("https://www.hotelna.info/privacy.html")
              }
            >
              <ThemedText style={styles.legalLink} size={16}>
                {t("about.legal.privacyPolicy")}
              </ThemedText>
            </TouchableOpacity>
            <TouchableOpacity
              onPress={() =>
                handleLinkPress("https://www.hotelna.info/privacy.html")
              }
            >
              <ThemedText style={styles.legalLink} size={16}>
                {t("about.legal.termsOfService")}
              </ThemedText>
            </TouchableOpacity>
            <TouchableOpacity
              onPress={() =>
                handleLinkPress("https://www.hotelna.info/privacy.html")
              }
            >
              <ThemedText style={styles.legalLink} size={16}>
                {t("about.legal.licenses")}
              </ThemedText>
            </TouchableOpacity>
          </View>
          <ThemedText style={styles.copyright} size={12}>
            {t("about.copyright", { year: new Date().getFullYear() })}
          </ThemedText>
        </View>
      </ScrollView>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    alignItems: "center",
    marginBottom: scale(32),
  },
  logo: {
    width: scale(120),
    height: scale(120),
    marginBottom: scale(16),
    borderRadius: scale(60),
  },
  title: {
    marginBottom: scale(8),
  },
  version: {
    opacity: 0.6,
  },
  section: {
    marginBottom: scale(24),
  },
  sectionTitle: {
    marginBottom: scale(16),
  },
  contentCard: {
    padding: scale(16),
    borderRadius: scale(12),
  },
  description: {
    lineHeight: scale(22),
    opacity: 0.8,
  },
  socialLinks: {
    flexDirection: "row",
    justifyContent: "space-around",
    flexWrap: "wrap",
    gap: scale(12),
  },
  socialLink: {
    flexDirection: "row",
    alignItems: "center",
    padding: scale(12),
    borderRadius: scale(8),
    gap: scale(8),
    minWidth: scale(120),
  },

  legalLinks: {
    gap: scale(12),
  },
  legalLink: {
    opacity: 0.8,
    textDecorationLine: "underline",
  },
  copyright: {
    opacity: 0.6,
    textAlign: "center",
    marginTop: scale(32),
    marginBottom: scale(32),
  },
});
