import { scale } from "@/src/_helper/Scaler";
import { useLanguage } from "@/src/context/LanguageContext";
import { Href, useRouter } from "expo-router";
import React from "react";
import { StyleSheet, View } from "react-native";
import { SettingItem } from "./SettingItem";

export const AccountSection = ({
  role = "client",
}: {
  role?: "client" | "vendor";
}) => {
  const router = useRouter();
  const { t } = useLanguage();
  // Helper to prefix route with role
  const route = (path: string) => `/(${role})/(settings)/${path}` as Href;

  return (
    <View style={styles.section}>
      <View style={styles.settingsList}>
        <SettingItem
          title={t("settings.sections.account.profile.title")}
          onPress={() => router.push(route("profile"))}
          icon="user"
        />
        <SettingItem
          title={t("settings.sections.account.notifications.title")}
          onPress={() => router.push(route("notifications"))}
          icon="bell"
        />
        <SettingItem
          title={t("settings.sections.account.security.title")}
          onPress={() => router.push(route("security"))}
          icon="lock"
        />
        {role === "client" ? (
          <>
            <SettingItem
              title={t("settings.sections.account.orders.title")}
              onPress={() => router.push(route("orders"))}
              icon="shopping-bag"
            />
            <SettingItem
              title={t("settings.sections.account.locations.title")}
              onPress={() => router.push(route("locations"))}
              icon="map-pin"
            />
          </>
        ) : null}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  section: {
    marginVertical: scale(12),
  },
  sectionTitle: {
    marginBottom: scale(6),
    opacity: 0.8,
  },
  settingsList: {
    gap: scale(8),
  },
});
