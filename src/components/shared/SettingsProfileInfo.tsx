import { scale } from "@/src/_helper/Scaler";
import { ErrorComponent } from "@/src/components/ui/ErrorComponent";
import { ThemedText } from "@/src/components/ui/ThemedText";
import { useSession } from "@/src/context/AuthContext";
import { useLanguage } from "@/src/context/LanguageContext";
import useGetUserProfile from "@/src/services/querys/profile/useGetUserProfile";
import React from "react";
import { StyleSheet, View } from "react-native";
import { SettingsProfileInfoSkeleton } from "../loader/SettingsProfileSkelaton";

export default function SettingsProfileInfo() {
  const { t } = useLanguage();

  const { isLoading: isSessionLoading, session } = useSession();
  const {
    data,
    isLoading: isLoadingProfile,
    error,
    refetch,
  } = useGetUserProfile(session?.userId);
  const handleRefetch = async () => await refetch();
  if (error) {
    return (
      <ErrorComponent
        error={t("backend.server_error")}
        onRetry={handleRefetch}
      />
    );
  }

  return (
    <View
      style={styles.container}
      accessible
      accessibilityLabel={
        t("settings.profile.accessibilityLabel") || "User Profile Information"
      }
    >
      {isSessionLoading || isLoadingProfile || !data?.data ? (
        <SettingsProfileInfoSkeleton />
      ) : (
        <View>
          <View style={styles.avatarWrapper}>
            <View style={styles.avatar}>
              <View style={styles.avatarPlaceholder}>
                <ThemedText size={48}>
                  {data?.data?.name?.charAt(0).toUpperCase() || "?"}
                </ThemedText>
              </View>
            </View>
          </View>

          <View style={styles.infoContainer}>
            <InfoItem
              label={t("settings.profile.form.fullName.label") || "Full Name"}
              value={data?.data?.name}
            />
            <InfoItem
              label={t("settings.profile.form.email.label") || "Email"}
              value={data?.data?.email}
            />
            <InfoItem
              label={t("settings.profile.form.phone.label") || "Phone"}
              value={data?.data?.phone}
            />
          </View>
        </View>
      )}
    </View>
  );
}

function InfoItem({ label, value }: { label: string; value?: string }) {
  return (
    <View style={styles.infoItem}>
      <ThemedText style={styles.label}>{label}</ThemedText>
      <ThemedText>{value && value.trim() !== "" ? value : "-"}</ThemedText>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    width: "100%",
  },
  avatarWrapper: {
    marginBottom: scale(10),
    width: "100%",
    alignItems: "center",
    justifyContent: "center",
  },
  avatar: {
    backgroundColor: "#ccc",
    borderRadius: scale(60),
    height: scale(120),
    width: scale(120),
    alignItems: "center",
    justifyContent: "center",
  },
  avatarPlaceholder: {
    width: scale(120),
    height: scale(120),
    borderRadius: scale(60),
    justifyContent: "center",
    alignItems: "center",
  },
  infoContainer: {
    width: "100%",
    borderRadius: scale(12),
    paddingVertical: scale(20),
  },
  infoItem: {
    marginBottom: scale(20),
  },
  label: {
    marginBottom: scale(6),
  },
});
