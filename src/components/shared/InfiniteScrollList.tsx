import { FlashList, FlashListProps } from "@shopify/flash-list";
import React from "react";
import { RefreshControl } from "react-native";

interface InfiniteScrollListProps<T>
  extends Omit<
    FlashListProps<T>,
    "refreshing" | "onRefresh" | "ListEmptyComponent" | "ListFooterComponent"
  > {
  isLoading?: boolean;
  isFetchingNextPage?: boolean;
  hasNextPage?: boolean;
  fetchNextPage?: () => void;
  refreshing?: boolean;
  onRefresh?: () => void;
  skeletonComponent?: React.ReactElement;
  emptyComponent?: React.ReactElement;
  count: number;
}

export function InfiniteScrollList<T>({
  isLoading = false,
  isFetchingNextPage = false,
  hasNextPage = false,
  fetchNextPage,
  refreshing = false,
  onRefresh,
  skeletonComponent,
  emptyComponent,
  data,
  renderItem,
  estimatedItemSize,
  count = 5,
  ...rest
}: InfiniteScrollListProps<T>) {
  // Show skeleton during initial loading OR during refresh (when data exists but we're refreshing)
  const showSkeleton = (isLoading || refreshing) && skeletonComponent;

  // Create unique skeleton data to avoid duplicate keys
  const skeletonData = showSkeleton
    ? Array.from(
        { length: count },
        (_, index) => ({ __skeleton: true, __index: index } as T)
      )
    : data;

  return (
    <FlashList
      data={skeletonData}
      renderItem={(props) =>
        showSkeleton && skeletonComponent ? (
          <>{skeletonComponent}</>
        ) : (
          renderItem?.(props) ?? null
        )
      }
      keyExtractor={(item, index) => {
        if (showSkeleton) {
          return `skeleton-${index}`;
        }
        if (rest.keyExtractor) {
          const key = rest.keyExtractor(item, index);
          // Ensure the key is defined, else fallback:
          return key !== undefined && key !== null ? key : `fallback-${index}`;
        }
        return `item-${index}`;
      }}
      estimatedItemSize={estimatedItemSize}
      onEndReached={() => {
        if (hasNextPage && !isFetchingNextPage) {
          fetchNextPage?.();
        }
      }}
      onEndReachedThreshold={0.5}
      ListFooterComponent={
        isFetchingNextPage && skeletonComponent
          ? () => <>{skeletonComponent}</>
          : undefined
      }
      ListEmptyComponent={
        !isLoading && !refreshing && emptyComponent
          ? () => <>{emptyComponent}</>
          : undefined
      }
      showsVerticalScrollIndicator={false}
      refreshControl={
        onRefresh ? (
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        ) : undefined
      }
      {...rest}
    />
  );
}
