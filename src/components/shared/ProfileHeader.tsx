import { scale } from "@/src/_helper/Scaler";
import { ThemedText } from "@/src/components/ui/ThemedText";
import { Colors } from "@/src/constants/Colors";
import { useSession } from "@/src/context/AuthContext";
import { useTheme } from "@/src/context/ThemeContext";
import useGetUserProfile from "@/src/services/querys/profile/useGetUserProfile";
import React from "react";
import { StyleSheet, View } from "react-native";
import { ProfileHeaderSkeleton } from "../loader/ProfileHeaderSkelaton";
import { ErrorComponentSmall } from "../ui/ErrorComponentSmall";

export function ProfileHeader() {
  const { currentTheme } = useTheme();

  const { isLoading: isSessionLoading, session } = useSession();

  const {
    data,
    isLoading: isLoadingProfile,
    error,
  } = useGetUserProfile(session?.userId);
  if (error) {
    return <ErrorComponentSmall />;
  }

  return (
    <View
      style={{
        justifyContent: "center",
        alignItems: "center",
        marginBottom: scale(20),
      }}
    >
      {isSessionLoading || isLoadingProfile || !data ? (
        <ProfileHeaderSkeleton />
      ) : (
        <View style={{ height: scale(190), width: "90%" }}>
          <View style={styles.avatarWrapper}>
            <View style={styles.avatarPlaceholder}>
              <ThemedText size={48}>
                {data?.data?.name?.charAt(0).toUpperCase() || "?"}
              </ThemedText>
            </View>
          </View>
          <ThemedText size={22} type="bold" style={{ alignSelf: "center" }}>
            {data?.data?.name}
          </ThemedText>
          <ThemedText
            style={{
              color: Colors[currentTheme ?? "dark"].primary,
              alignSelf: "center",
            }}
          >
            {data?.data?.email}
          </ThemedText>
        </View>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  avatarWrapper: {
    marginBottom: scale(12),
    borderRadius: scale(60),
    overflow: "hidden",
    backgroundColor: "#ccc",
    width: scale(120),
    height: scale(120),
    alignSelf: "center",
  },
  avatarPlaceholder: {
    width: scale(120),
    height: scale(120),
    borderRadius: scale(60),
    justifyContent: "center",
    alignItems: "center",
  },
});
