import { scale, scaleFont } from "@/src/_helper/Scaler";
import { Colors } from "@/src/constants/Colors";
import { useLanguage } from "@/src/context/LanguageContext";
import { useTheme } from "@/src/context/ThemeContext";
import { MaterialIcons } from "@expo/vector-icons";
import React from "react";
import { StyleSheet } from "react-native";
import { ThemedText } from "../ui/ThemedText";
import { ThemedView } from "../ui/ThemedView";

const ErrorText = ({ error }: { error: string }) => {
  const { currentTheme } = useTheme();
  const { t } = useLanguage();
  return (
    <ThemedView style={[styles.errorContainer, { marginTop: scale(10) }]}>
      <ThemedText
        size={12}
        type="bold"
        style={[{ color: Colors[currentTheme ?? "dark"].error }]}
      >
        {typeof error === "string" ? t(error) : error}
      </ThemedText>
      <MaterialIcons
        name="error"
        size={scaleFont(13)}
        color={Colors[currentTheme ?? "dark"].error}
      />
    </ThemedView>
  );
};

const styles = StyleSheet.create({
  errorContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingHorizontal: scale(3),
  },
});
export default ErrorText;
