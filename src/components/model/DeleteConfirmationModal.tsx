import { scale } from "@/src/_helper/Scaler";
import { ThemedText } from "@/src/components/ui/ThemedText";
import { ThemedView } from "@/src/components/ui/ThemedView";
import { Colors } from "@/src/constants/Colors";
import { useLanguage } from "@/src/context/LanguageContext";
import { useTheme } from "@/src/context/ThemeContext";
import { MaterialIcons } from "@expo/vector-icons";
import React from "react";
import {
  ActivityIndicator,
  Modal,
  StyleSheet,
  TouchableOpacity,
  TouchableWithoutFeedback,
  View,
} from "react-native";

interface DeleteConfirmationModalProps {
  visible: boolean;
  onClose: () => void;
  onConfirm: () => void;
  title?: string;
  message?: string;
  confirmText?: string;
  cancelText?: string;
  isLoading?: boolean;
  itemName?: string;
  itemType?: string;
}

export const DeleteConfirmationModal: React.FC<
  DeleteConfirmationModalProps
> = ({
  visible,
  onClose,
  onConfirm,
  title,
  message,
  confirmText,
  cancelText,
  isLoading = false,
  itemName,
  itemType = "item",
}) => {
  const { currentTheme } = useTheme();
  const colors = Colors[currentTheme ?? "dark"];
  const { t } = useLanguage();
  const styles = getStyles(colors);

  // Default texts with fallbacks
  const modalTitle =
    title ||
    t("common.delete.title") ||
    t("modal.deleteConfirmation.defaultTitle");
  const modalMessage =
    message ||
    (itemName
      ? `Are you sure you want to delete "${itemName}"? This action cannot be undone.`
      : t("common.delete.message") ||
        t("modal.deleteConfirmation.defaultMessage"));
  const confirmButtonText =
    confirmText ||
    t("common.delete.deleteButton") ||
    t("modal.deleteConfirmation.defaultDelete");
  const cancelButtonText =
    cancelText ||
    t("common.delete.cancelButton") ||
    t("modal.deleteConfirmation.defaultCancel");

  const handleBackdropPress = () => {
    if (!isLoading) {
      onClose();
    }
  };

  const handleConfirm = () => {
    if (!isLoading) {
      onConfirm();
    }
  };

  const handleCancel = () => {
    if (!isLoading) {
      onClose();
    }
  };

  return (
    <Modal
      visible={visible}
      transparent
      animationType="fade"
      statusBarTranslucent
    >
      <TouchableWithoutFeedback onPress={handleBackdropPress}>
        <View style={styles.modalOverlay}>
          <TouchableWithoutFeedback onPress={() => {}}>
            <ThemedView
              style={[
                styles.deleteModal,
                { backgroundColor: colors.background },
              ]}
            >
              {/* Warning Icon */}
              <View
                style={[
                  styles.iconContainer,
                  { backgroundColor: colors.error + "20" },
                ]}
              >
                <MaterialIcons
                  name="warning"
                  size={scale(40)}
                  color={colors.error}
                />
              </View>

              {/* Title */}
              <ThemedText
                style={[styles.deleteTitle, { color: colors.text }]}
                size={20}
                type="bold"
              >
                {modalTitle}
              </ThemedText>

              {/* Message */}
              <ThemedText
                style={[styles.deleteMessage, { color: colors.secondary }]}
                size={16}
              >
                {modalMessage}
              </ThemedText>

              {/* Item Name Highlight (if provided) */}
              {itemName && (
                <ThemedText
                  style={[styles.itemName, { color: colors.primary }]}
                  size={16}
                  type="semi-bold"
                >
                  {itemName}
                </ThemedText>
              )}

              {/* Buttons */}
              <View style={styles.deleteButtons}>
                <TouchableOpacity
                  onPress={handleCancel}
                  style={[
                    styles.cancelButton,
                    {
                      backgroundColor:
                        colors.disabled || colors.secondary + "40",
                      opacity: isLoading ? 0.5 : 1,
                    },
                  ]}
                  disabled={isLoading}
                  activeOpacity={0.7}
                >
                  <ThemedText
                    style={[styles.buttonText, { color: colors.text }]}
                    size={16}
                    type="semi-bold"
                  >
                    {cancelButtonText}
                  </ThemedText>
                </TouchableOpacity>

                <TouchableOpacity
                  onPress={handleConfirm}
                  style={[
                    styles.deleteButton,
                    {
                      backgroundColor: colors.error,
                      opacity: isLoading ? 0.8 : 1,
                    },
                  ]}
                  disabled={isLoading}
                  activeOpacity={0.7}
                >
                  {isLoading ? (
                    <View style={styles.loadingContainer}>
                      <ActivityIndicator
                        size="small"
                        color="white"
                        style={styles.loadingIndicator}
                      />
                      <ThemedText
                        style={[styles.buttonText, { color: "white" }]}
                        size={16}
                        type="semi-bold"
                      >
                        Deleting...
                      </ThemedText>
                    </View>
                  ) : (
                    <ThemedText
                      style={[styles.buttonText, { color: "white" }]}
                      size={16}
                      type="semi-bold"
                    >
                      {confirmButtonText}
                    </ThemedText>
                  )}
                </TouchableOpacity>
              </View>
            </ThemedView>
          </TouchableWithoutFeedback>
        </View>
      </TouchableWithoutFeedback>
    </Modal>
  );
};

const getStyles = (colors: any) =>
  StyleSheet.create({
    modalOverlay: {
      flex: 1,
      backgroundColor: colors.modal_overlay,
      justifyContent: "center",
      alignItems: "center",
      paddingHorizontal: scale(20),
    },
    deleteModal: {
      width: "100%",
      maxWidth: scale(400),
      padding: scale(24),
      borderRadius: scale(16),
      alignItems: "center",
      shadowColor: colors.shadow_color,
      shadowOffset: {
        width: 0,
        height: 4,
      },
      shadowOpacity: 0.3,
      shadowRadius: 8,
      elevation: 8,
    },
    iconContainer: {
      width: scale(80),
      height: scale(80),
      borderRadius: scale(40),
      alignItems: "center",
      justifyContent: "center",
      marginBottom: scale(20),
    },
    deleteTitle: {
      textAlign: "center",
      marginBottom: scale(12),
    },
    deleteMessage: {
      textAlign: "center",
      lineHeight: scale(22),
      marginBottom: scale(16),
    },
    itemName: {
      textAlign: "center",
      marginBottom: scale(20),
      fontStyle: "italic",
    },
    deleteButtons: {
      flexDirection: "row",
      gap: scale(12),
      width: "100%",
      justifyContent: "center",
      alignItems: "center",
      marginTop: scale(8),
    },
    cancelButton: {
      flex: 1,
      paddingVertical: scale(14),
      paddingHorizontal: scale(20),
      borderRadius: scale(12),
      alignItems: "center",
      justifyContent: "center",
      minHeight: scale(48),
    },
    deleteButton: {
      flex: 1,
      paddingVertical: scale(14),
      paddingHorizontal: scale(20),
      borderRadius: scale(12),
      alignItems: "center",
      justifyContent: "center",
      minHeight: scale(48),
    },
    buttonText: {
      textAlign: "center",
    },
    loadingContainer: {
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "center",
    },
    loadingIndicator: {
      marginRight: scale(8),
    },
  });

export default DeleteConfirmationModal;
