import React from "react";
import {
  Modal,
  View,
  StyleSheet,
  TouchableOpacity,
  ActivityIndicator,
} from "react-native";
import { scale } from "@/src/_helper/Scaler";
import { ThemedText } from "@/src/components/ui/ThemedText";
import DefaultButton from "@/src/components/buttons/Default";
import { Colors, AppTheme } from "@/src/constants/Colors";
import { useTheme } from "@/src/context/ThemeContext";
import { Ionicons } from "@expo/vector-icons";

interface ConfirmationModalProps {
  visible: boolean;
  title: string;
  message: string;
  confirmText: string;
  cancelText: string;
  onConfirm: () => void;
  onClose: () => void;
  isLoading?: boolean;
}

export const ConfirmationModal: React.FC<ConfirmationModalProps> = ({
  visible,
  title,
  message,
  confirmText,
  cancelText,
  onConfirm,
  onClose,
  isLoading = false,
}) => {
  const { currentTheme } = useTheme();
  const theme = currentTheme ?? "dark";
  const styles = getStyles(theme);

  return (
    <Modal
      visible={visible}
      transparent
      animationType="fade"
      onRequestClose={onClose}
    >
      <View style={styles.overlay}>
        <View style={styles.modalContainer}>
          {/* Icon */}
          <View style={styles.iconContainer}>
            <Ionicons
              name="checkmark-circle"
              size={scale(60)}
              color={Colors[theme].primary}
            />
          </View>

          {/* Title */}
          <ThemedText type="bold" size={20} style={styles.title}>
            {title}
          </ThemedText>

          {/* Message */}
          <ThemedText
            size={16}
            style={[styles.message, { color: Colors[theme].secondary }]}
          >
            {message}
          </ThemedText>

          {/* Buttons */}
          <View style={styles.buttonContainer}>
            <TouchableOpacity
              style={[styles.cancelButton, { borderColor: Colors[theme].input_text }]}
              onPress={onClose}
              disabled={isLoading}
            >
              <ThemedText
                type="semi-bold"
                size={16}
                style={{ color: Colors[theme].secondary }}
              >
                {cancelText}
              </ThemedText>
            </TouchableOpacity>

            <DefaultButton
              title={confirmText}
              onPress={onConfirm}
              style={styles.confirmButton}
              disabled={isLoading}
              color={Colors[theme].primary}
            >
              {isLoading && (
                <ActivityIndicator
                  size="small"
                  color={Colors[theme].white}
                  style={styles.loadingIndicator}
                />
              )}
            </DefaultButton>
          </View>
        </View>
      </View>
    </Modal>
  );
};

const getStyles = (theme: AppTheme) =>
  StyleSheet.create({
    overlay: {
      flex: 1,
      backgroundColor: "rgba(0, 0, 0, 0.5)",
      justifyContent: "center",
      alignItems: "center",
      paddingHorizontal: scale(20),
    },
    modalContainer: {
      backgroundColor: Colors[theme].background,
      borderRadius: scale(16),
      padding: scale(24),
      width: "100%",
      maxWidth: scale(400),
      alignItems: "center",
    },
    iconContainer: {
      marginBottom: scale(16),
    },
    title: {
      textAlign: "center",
      marginBottom: scale(12),
    },
    message: {
      textAlign: "center",
      lineHeight: scale(22),
      marginBottom: scale(24),
    },
    buttonContainer: {
      flexDirection: "row",
      width: "100%",
      gap: scale(12),
    },
    cancelButton: {
      flex: 1,
      paddingVertical: scale(14),
      paddingHorizontal: scale(20),
      borderRadius: scale(12),
      borderWidth: 1,
      alignItems: "center",
      justifyContent: "center",
    },
    confirmButton: {
      flex: 1,
      marginTop: 0,
      marginBottom: 0,
      borderRadius: scale(12),
      paddingVertical: scale(14),
    },
    loadingIndicator: {
      marginLeft: scale(8),
    },
  });
