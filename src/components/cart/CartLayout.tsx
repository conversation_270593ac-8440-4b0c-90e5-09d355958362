import { useCart } from "@/src/hooks/useCart";
import React, { createContext, useCallback, useContext, useState } from "react";
import { View } from "react-native";
import CartDetailsBottomSheet from "../bottom-sheet/CartDetailsBottomSheet";
import FloatingCartButton from "./FloatingCartButton";

// Create context for cart layout actions
interface CartLayoutContextType {
  showCartDetails: () => void;
}

const CartLayoutContext = createContext<CartLayoutContextType | undefined>(
  undefined
);

// Hook to use cart layout context
export function useCartLayout() {
  const context = useContext(CartLayoutContext);
  if (context === undefined) {
    throw new Error("useCartLayout must be used within a CartLayout");
  }
  return context;
}

interface CartLayoutProps {
  children: React.ReactNode;
  hideFloatingButton?: boolean;
}

export default function CartLayout({
  children,
  hideFloatingButton = false,
}: CartLayoutProps) {
  const { cartItems, isEmpty } = useCart();
  const [showCartDetailsSheet, setShowCartDetailsSheet] = useState(false);

  // Show cart details function
  const showCartDetails = useCallback(() => {
    setShowCartDetailsSheet(true);
  }, []);

  const handleCloseCartDetails = useCallback(() => {
    setShowCartDetailsSheet(false);
  }, []);

  const contextValue: CartLayoutContextType = {
    showCartDetails,
  };

  return (
    <CartLayoutContext.Provider value={contextValue}>
      <View
        style={{
          flex: 1,
          position: "relative",
        }}
      >
        {/* Scrollable content area */}
        <View
          style={{
            flex: 1,
            paddingBottom: !hideFloatingButton && !isEmpty ? 80 : 0, // Add padding to prevent content from hiding behind button
          }}
        >
          {children}
        </View>

        {/* Fixed floating button at bottom - Only render when cart has items */}
        {!hideFloatingButton && !isEmpty && (
          <View
            style={{
              position: "absolute",
              bottom: 10, // Adjust this value as needed
              left: 0,
              right: 0,
              alignItems: "center",
              zIndex: 1000,
            }}
          >
            <FloatingCartButton onPress={showCartDetails} />
          </View>
        )}

        {/* Global Cart Details Bottom Sheet */}
        <CartDetailsBottomSheet
          isVisible={showCartDetailsSheet}
          onClose={handleCloseCartDetails}
        />
      </View>
    </CartLayoutContext.Provider>
  );
}
