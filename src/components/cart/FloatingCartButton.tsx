import { scale, scaleFont } from "@/src/_helper/Scaler";
import { Colors } from "@/src/constants/Colors";
import { useLanguage } from "@/src/context/LanguageContext";
import { useTheme } from "@/src/context/ThemeContext";
import { useCart } from "@/src/hooks/useCart";
import { MaterialIcons } from "@expo/vector-icons";
import React from "react";
import { Dimensions, StyleSheet, TouchableOpacity, View } from "react-native";
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withSpring,
} from "react-native-reanimated";
import { ThemedText } from "../ui/ThemedText";

const { width: SCREEN_WIDTH } = Dimensions.get("window");

interface FloatingCartButtonProps {
  onPress: () => void;
  bottomOffset?: number; // Allow custom bottom offset to avoid overlapping
}

const AnimatedTouchableOpacity =
  Animated.createAnimatedComponent(TouchableOpacity);

export default function FloatingCartButton({
  onPress,
  bottomOffset = 5,
}: FloatingCartButtonProps) {
  const { currentTheme } = useTheme();
  const { t } = useLanguage();
  const { cartItems, totalItems, total, isEmpty } = useCart();

  const scale_animation = useSharedValue(1);

  const animatedStyle = useAnimatedStyle(() => {
    return {
      transform: [{ scale: scale_animation.value }],
    };
  });

  const handlePress = () => {
    scale_animation.value = withSpring(0.95, { damping: 10 }, () => {
      scale_animation.value = withSpring(1, { damping: 8 });
    });
    onPress();
  };

  return (
    <AnimatedTouchableOpacity
      style={[
        styles.container,
        animatedStyle,
        {
          backgroundColor: Colors[currentTheme ?? "dark"].primary,
          shadowColor: Colors[currentTheme ?? "dark"].text,
          bottom: scale(bottomOffset),
        },
      ]}
      onPress={handlePress}
      activeOpacity={0.8}
    >
      <View style={styles.content}>
        {/* Cart Icon with Badge */}
        <View style={styles.iconContainer}>
          <MaterialIcons
            name="shopping-cart"
            size={scaleFont(24)}
            color={
              currentTheme === "dark" ? Colors.dark.black : Colors.light.white
            }
          />
          {totalItems > 0 && (
            <View
              style={[
                styles.badge,
                {
                  backgroundColor: Colors[currentTheme ?? "dark"].warning,
                },
              ]}
            >
              <ThemedText
                size={10}
                type="bold"
                style={{
                  color:
                    currentTheme === "dark"
                      ? Colors.dark.black
                      : Colors.light.white,
                }}
              >
                {totalItems > 99 ? "99+" : totalItems.toString()}
              </ThemedText>
            </View>
          )}
        </View>

        {/* Cart Info */}
        <View style={styles.info}>
          <ThemedText
            size={12}
            type="semi-bold"
            style={{
              color:
                currentTheme === "dark"
                  ? Colors.dark.black
                  : Colors.light.white,
            }}
          >
            {t("cart.viewCart")}
          </ThemedText>
          <ThemedText
            size={14}
            type="bold"
            style={{
              color:
                currentTheme === "dark"
                  ? Colors.dark.black
                  : Colors.light.white,
            }}
          >
            ${total.toFixed(2)}
          </ThemedText>
        </View>

        {/* Arrow Icon */}
        <MaterialIcons
          name="keyboard-arrow-up"
          size={scaleFont(20)}
          color={
            currentTheme === "dark" ? Colors.dark.black : Colors.light.white
          }
        />
      </View>
    </AnimatedTouchableOpacity>
  );
}

const styles = StyleSheet.create({
  container: {
    position: "absolute",
    left: scale(20),
    right: scale(20),
    height: scale(60),
    borderRadius: scale(30),
    elevation: 8, // elevation should be an integer
    shadowOffset: {
      width: 0,
      height: scale(4),
    },
    shadowOpacity: 0.3,
    shadowRadius: scale(8),
    zIndex: 1000,
  },
  content: {
    flex: 1,
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: scale(20),
    gap: scale(12),
  },
  iconContainer: {
    position: "relative",
  },
  badge: {
    position: "absolute",
    top: scale(-8),
    right: scale(-8),
    minWidth: scale(18),
    height: scale(18),
    borderRadius: scale(9),
    justifyContent: "center",
    alignItems: "center",
    paddingHorizontal: scale(4),
  },
  info: {
    flex: 1,
    alignItems: "flex-start",
  },
});
