import { scale } from "@/src/_helper/Scaler";
import React, { PropsWithChildren } from "react";
import { ScrollView, StyleSheet, View } from "react-native";
import { KeyboardAvoidingView } from "react-native-keyboard-controller";
import { ThemedView } from "../ui/ThemedView";
interface MainContainerProps extends PropsWithChildren {
  disableScroll?: boolean;
}

export function MainContainerForScreensWithInput({
  children,
  disableScroll,
}: MainContainerProps) {
  const ContentWrapper = disableScroll ? View : ScrollView;

  return (
    <KeyboardAvoidingView
      behavior={"padding"}
      keyboardVerticalOffset={scale(100)}
      style={{
        flex: 1,
      }}
    >
      <ContentWrapper
        {...(!disableScroll && {
          contentContainerStyle: styles.scrollContent,
          keyboardShouldPersistTaps: "handled",
          showsHorizontalScrollIndicator: false,
          showsVerticalScrollIndicator: false,
          bounces: false, // disables iOS bounce effect
          overScrollMode: "never", // disables Android overscroll glow
        })}
      >
        <ThemedView style={styles.content}>{children}</ThemedView>
      </ContentWrapper>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
  },
  content: {
    flexGrow: 1,
    width: "100%",
    paddingHorizontal: scale(15),
    paddingVertical: scale(20),
  },
});
