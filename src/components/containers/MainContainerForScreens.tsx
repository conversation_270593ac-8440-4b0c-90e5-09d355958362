import { scale } from "@/src/_helper/Scaler";
import React, { PropsWithChildren } from "react";
import { ScrollView, StyleSheet, View } from "react-native";
import { ThemedView } from "../ui/ThemedView";
interface MainContainerProps extends PropsWithChildren {
  noScroll?: boolean;
}

export function MainContainerForScreens({
  children,
  noScroll,
}: MainContainerProps) {
  const ContentWrapper = noScroll ? View : ScrollView;

  return (
    <ContentWrapper
      {...(!noScroll
        ? {
            contentContainerStyle: styles.scrollContent,
            keyboardShouldPersistTaps: "handled",
            showsHorizontalScrollIndicator: false,
            showsVerticalScrollIndicator: false,
            bounces: false, // disables iOS bounce effect
            overScrollMode: "never", // disables Android overscroll glow
          }
        : { flex: 1 })}
    >
      <ThemedView style={styles.content}>{children}</ThemedView>
    </ContentWrapper>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
  },
  content: {
    flexGrow: 1,
    width: "100%",
    paddingHorizontal: scale(20),
    paddingVertical: scale(20),
  },
});
