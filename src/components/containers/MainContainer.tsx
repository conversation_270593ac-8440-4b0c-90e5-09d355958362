import { Colors } from "@/src/constants/Colors";
import { useTheme } from "@/src/context/ThemeContext";
import React, { PropsWithChildren } from "react";
import { StatusBar, StyleSheet } from "react-native";
import { SafeAreaProvider, SafeAreaView } from "react-native-safe-area-context";
import { ThemedView } from "../ui/ThemedView";

export function MainContainer({ children }: PropsWithChildren) {
  const { currentTheme } = useTheme();
  return (
    <SafeAreaProvider>
      <SafeAreaView
        edges={["bottom"]}
        style={[
          styles.safeArea,

          {
            backgroundColor:
              currentTheme === "dark"
                ? Colors.dark.background
                : Colors.light.black,
          },
        ]}
      >
        <ThemedView style={styles.content}>{children}</ThemedView>
        <StatusBar
          translucent
          backgroundColor="transparent"
          barStyle={currentTheme === "dark" ? "light-content" : "dark-content"}
        />
      </SafeAreaView>
    </SafeAreaProvider>
  );
}

const styles = StyleSheet.create({
  content: {
    flex: 1,
  },
  safeArea: {
    flex: 1,
  },
});
