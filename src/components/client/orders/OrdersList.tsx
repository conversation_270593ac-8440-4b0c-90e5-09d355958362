import { InfiniteScrollList } from "@/src/components/shared/InfiniteScrollList";
import { useLanguage } from "@/src/context/LanguageContext";
import useGetOrders from "@/src/services/querys/client/useGetOrders";
import React, { useMemo, useState } from "react";
import { StyleSheet, View } from "react-native";
import SkeletonOrderListClient from "../../loader/SkeletonOrderList";
import EmptyOrderList from "../../ui/EmptyOrderList";
import { ErrorComponent } from "../../ui/ErrorComponent";
import { ThemedText } from "../../ui/ThemedText";
import OrderCard from "./OrderCard";

const OrdersList = () => {
  const [refreshing, setRefreshing] = useState(false);
  const { t } = useLanguage();

  const {
    data,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
    isLoading,
    isError,
    error,
    refetch,
  } = useGetOrders();

  const orders = useMemo(() => {
    return data?.pages?.flatMap((page) => page.data.items) || [];
  }, [data]);

  const totalItems = data?.pages?.[0]?.data?.totalItems || 0;

  const handleRefresh = async () => {
    setRefreshing(true);
    await refetch();
    setRefreshing(false);
  };

  const handleRefetch = async () => await refetch();

  if (isError) {
    return <ErrorComponent error={error.message} onRetry={handleRefetch} />;
  }

  return (
    <View style={styles.ordersSection}>
      <ThemedText type="bold" size={18} style={{ marginBottom: 16 }}>
        {t("orders.title")} ({totalItems})
      </ThemedText>
      <InfiniteScrollList
        data={orders}
        isLoading={isLoading}
        isFetchingNextPage={isFetchingNextPage}
        hasNextPage={hasNextPage}
        fetchNextPage={fetchNextPage}
        refreshing={refreshing}
        onRefresh={handleRefresh}
        estimatedItemSize={120}
        renderItem={({ item }) => <OrderCard item={item} />}
        skeletonComponent={<SkeletonOrderListClient />}
        emptyComponent={<EmptyOrderList />}
        count={5}
      />
    </View>
  );
};

export default OrdersList;

const styles = StyleSheet.create({
  ordersSection: {
    flex: 1,
  },
});
