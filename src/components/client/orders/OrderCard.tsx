import { scale, scaleFont } from "@/src/_helper/Scaler";
import { ThemedText } from "@/src/components/ui/ThemedText";
import { Colors } from "@/src/constants/Colors";
import { useLanguage } from "@/src/context/LanguageContext";
import { useTheme } from "@/src/context/ThemeContext";
import { IOrder } from "@/src/types";
import { MaterialIcons } from "@expo/vector-icons";
import { router } from "expo-router";
import React from "react";
import { Image, StyleSheet, TouchableOpacity, View } from "react-native";

interface OrderCardProps {
  item: IOrder;
}

export default function OrderCard({ item }: OrderCardProps) {
  const { currentTheme } = useTheme();
  const { t } = useLanguage();
  const colors = Colors[currentTheme ?? "dark"];

  const getStatusColor = (status?: string) => {
    if (!status) return colors.text;
    switch (status.toLowerCase()) {
      case "pending":
        return Colors[currentTheme ?? "dark"].orange;
      case "confirmed":
        return Colors[currentTheme ?? "dark"].green;
      case "accepted":
        return Colors[currentTheme ?? "dark"].blue;
      case "delivered":
        return Colors[currentTheme ?? "dark"].green;
      case "cancelled":
        return Colors[currentTheme ?? "dark"].error;
      default:
        return colors.text;
    }
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return "N/A";
    const date = new Date(dateString);
    return date.toLocaleDateString();
  };

  const formatPrice = (price?: number) => {
    if (!price) return "$0.00";
    return `$${price.toFixed(2)}`;
  };

  return (
    <TouchableOpacity
      style={[
        styles.card,
        {
          shadowColor: colors.text,
        },
      ]}
      onPress={() =>
        router.push(`/(client)/(screens)/orders/${item._id}/order-details`)
      }
      activeOpacity={0.7}
    >
      <View style={styles.content}>
        {/* Product Image */}
        <View style={styles.imageContainer}>
          {item.products?.[0]?.product?.images?.[0] ? (
            <Image
              source={{ uri: item.products[0].product.images[0] }}
              style={styles.productImage}
              resizeMode="cover"
            />
          ) : (
            <View
              style={[
                styles.placeholderImage,
                { backgroundColor: colors.primary + "20" },
              ]}
            >
              <MaterialIcons
                name="shopping-bag"
                size={scaleFont(24)}
                color={colors.text}
              />
            </View>
          )}
        </View>

        {/* Order Info */}
        <View style={styles.info}>
          <View style={styles.headerSection}>
            <ThemedText type="semi-bold" size={16} style={styles.orderTitle}>
              {item.products && item.products.length > 1
                ? t("orders.multipleProducts", { count: item.products.length })
                : item.products?.[0]?.product?.name ||
                  t("orderDetails.fallbacks.unknownProduct")}
            </ThemedText>
            <View
              style={[
                styles.statusBadge,
                { backgroundColor: getStatusColor(item.orderStatus) },
              ]}
            >
              <ThemedText size={10} type="semi-bold" style={{ color: "white" }}>
                {item.orderStatus
                  ? t(`orderStatus.${item.orderStatus.toLowerCase()}`)
                  : "N/A"}
              </ThemedText>
            </View>
          </View>

          <View style={styles.detailsSection}>
            {/* Brand */}
            <View style={styles.detailRow}>
              <MaterialIcons
                name="store"
                size={scaleFont(14)}
                color={colors.text}
                style={styles.detailIcon}
              />
              <ThemedText size={14} style={styles.detailText}>
                {item.brand?.name || t("orderDetails.fallbacks.unknownBrand")}
              </ThemedText>
            </View>

            {/* Order Date */}
            <View style={styles.detailRow}>
              <MaterialIcons
                name="calendar-today"
                size={scaleFont(14)}
                color={colors.text}
                style={styles.detailIcon}
              />
              <ThemedText size={14} style={styles.detailText}>
                {formatDate(item.orderDate)}
              </ThemedText>
            </View>

            {/* Total Price */}
            <View style={styles.detailRow}>
              <MaterialIcons
                name="attach-money"
                size={scaleFont(14)}
                color={colors.text}
                style={styles.detailIcon}
              />
              <ThemedText size={14} type="semi-bold" style={styles.priceText}>
                {formatPrice(item.totalPrice)}
              </ThemedText>
            </View>
          </View>
        </View>

        {/* Chevron */}
        <MaterialIcons
          name="chevron-right"
          size={scaleFont(24)}
          color={colors.text}
          style={styles.chevron}
        />
      </View>
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  card: {
    borderRadius: scale(16),
    marginBottom: scale(12),
    paddingVertical: scale(16),
  },
  content: {
    flexDirection: "row",
    alignItems: "center",
  },
  imageContainer: {
    marginRight: scale(16),
  },
  productImage: {
    width: scale(60),
    height: scale(60),
    borderRadius: scale(8),
  },
  placeholderImage: {
    width: scale(60),
    height: scale(60),
    borderRadius: scale(8),
    alignItems: "center",
    justifyContent: "center",
  },
  info: {
    flex: 1,
  },
  headerSection: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    marginBottom: scale(8),
  },
  orderTitle: {
    flex: 1,
    marginRight: scale(8),
  },
  statusBadge: {
    paddingHorizontal: scale(8),
    paddingVertical: scale(4),
    borderRadius: scale(12),
  },
  detailsSection: {
    gap: scale(4),
  },
  detailRow: {
    flexDirection: "row",
    alignItems: "center",
  },
  detailIcon: {
    marginRight: scale(8),
    width: scale(16),
  },
  detailText: {
    opacity: 0.8,
  },
  priceText: {
    opacity: 0.9,
  },
  chevron: {
    marginLeft: scale(8),
  },
});
