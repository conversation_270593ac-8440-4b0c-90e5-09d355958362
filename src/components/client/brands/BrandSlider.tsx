import { scale } from "@/src/_helper/Scaler";
import { InfiniteScrollList } from "@/src/components/shared/InfiniteScrollList";
import { ThemedText } from "@/src/components/ui/ThemedText";

import { Colors } from "@/src/constants/Colors";
import { useLanguage } from "@/src/context/LanguageContext";
import { useTheme } from "@/src/context/ThemeContext";
import useGetBrands from "@/src/services/querys/client/useGetBrands";
import { IClientBrand } from "@/src/types";
import { router } from "expo-router";
import React, { useMemo } from "react";
import { StyleSheet, View } from "react-native";
import SkeletonFavoriteBrandCard from "../../loader/FavoriteBrandSkelaton";
import EmptyFavoriteBrandsList from "../../ui/EmptyFavoriteBrandsList";
import { ErrorComponentSmall } from "../../ui/ErrorComponentSmall";
import BrandCard from "./BrandCard";

export default function BrandSlider() {
  const { currentTheme } = useTheme();
  const { t } = useLanguage();
  const {
    data,
    isLoading,
    hasNextPage,
    fetchNextPage,
    isFetchingNextPage,
    error,
  } = useGetBrands();
  const totalItems = data?.pages?.[0]?.data?.totalItems ?? 0;

  const brands = useMemo(() => {
    // Add null/undefined checks for data and pages

    return data?.pages.flatMap((page) => {
      // Add null/undefined check for page data
      if (!page?.data?.items) {
        return [];
      }

      return page.data.items
        .filter((brand: IClientBrand) => brand && brand._id) // Filter out invalid items
        .map((brand: IClientBrand) => ({
          id: brand._id,
          title: brand.name || "",
          subtitle: brand.description || "",
          image: { uri: brand.logo || "" },
        }));
    });
  }, [data]);

  const handleBrandPress = (id: string) => {
    if (id) {
      router.push(`/(client)/(screens)/brands/${id}/brand-details`);
    }
  };

  const renderBrandItem = ({ item }: any) => (
    <BrandCard {...item} onPress={() => handleBrandPress(item?.id)} />
  );

  // Safe key extractor with fallback
  const keyExtractor = (item: any, index: number) => {
    return item?.id?.toString() || `brand-${index}`;
  };
  const handleViewAll = () => {
    router.push("/(client)/(screens)/brands-list");
  };
  if (error) {
    return (
      <>
        <View style={styles.header}>
          <ThemedText size={18} type="bold">
            {t("client.sliders.featuredBrands")} ({totalItems})
          </ThemedText>
          <ThemedText
            style={{ color: Colors[currentTheme ?? "dark"].secondary }}
            onPress={handleViewAll}
          >
            {t("client.sliders.viewAll")}
          </ThemedText>
        </View>
        <ErrorComponentSmall />
      </>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <ThemedText size={18} type="bold">
          {t("client.sliders.featuredBrands")} ({totalItems})
        </ThemedText>
        <ThemedText
          style={{ color: Colors[currentTheme ?? "dark"].secondary }}
          onPress={handleViewAll}
        >
          {t("client.sliders.viewAll")}
        </ThemedText>
      </View>
      <InfiniteScrollList
        data={brands}
        renderItem={renderBrandItem}
        keyExtractor={keyExtractor}
        onEndReached={fetchNextPage}
        onEndReachedThreshold={0.1}
        hasNextPage={hasNextPage}
        isFetchingNextPage={isFetchingNextPage}
        isLoading={isLoading}
        estimatedItemSize={80}
        skeletonComponent={<SkeletonFavoriteBrandCard />}
        emptyComponent={<EmptyFavoriteBrandsList />}
        count={6}
        horizontal={true}
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={{ paddingRight: scale(10) }}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    marginTop: scale(10),
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: scale(10),
  },
});
