import { scale } from "@/src/_helper/Scaler";
import SkeletonBrandList from "@/src/components/loader/SkeletonBrandList";
import { InfiniteScrollList } from "@/src/components/shared/InfiniteScrollList";
import { ThemedText } from "@/src/components/ui/ThemedText";
import { useLanguage } from "@/src/context/LanguageContext";
import useGetBrands from "@/src/services/querys/client/useGetBrands";
import { IClientBrand } from "@/src/types";
import React, { useMemo, useState } from "react";
import { StyleSheet, View } from "react-native";
import EmptyBrandList from "../../ui/EmptyBrandList";
import { ErrorComponent } from "../../ui/ErrorComponent";
import BrandListItem from "./BrandListItem";

export default function BrandList() {
  const { t } = useLanguage();
  const {
    data,
    isLoading,
    hasNextPage,
    fetchNextPage,
    isFetchingNextPage,
    refetch,
    error,
  } = useGetBrands();
  const totalItems = data?.pages?.[0]?.data?.totalItems ?? 0;

  const [refreshing, setRefreshing] = useState(false);

  const brands = useMemo(() => {
    return (
      data?.pages.flatMap((page) =>
        page.data.items.map((brand: IClientBrand) => ({
          id: brand._id,
          name: brand.name,
          description: brand.description,
          logo: { uri: brand.logo },
        }))
      ) || []
    );
  }, [data]);

  const handleRefetch = async () => await refetch();
  const handleRefresh = async () => {
    setRefreshing(true);
    try {
      await refetch();
    } finally {
      setRefreshing(false);
    }
  };

  const renderBrandItem = ({ item }: any) => (
    <BrandListItem
      id={item.id}
      image={item.logo}
      title={item.name}
      description={item.description}
    />
  );

  if (error) {
    return (
      <ErrorComponent
        error={t("error.generic.title")}
        onRetry={handleRefetch}
      />
    );
  }

  return (
    <View style={styles.section}>
      <ThemedText type="bold" size={20} style={{ marginBottom: scale(16) }}>
        {t("client.brands.titleWithCount", { count: totalItems })}
      </ThemedText>
      <InfiniteScrollList
        data={brands}
        isLoading={isLoading}
        isFetchingNextPage={isFetchingNextPage}
        hasNextPage={hasNextPage}
        fetchNextPage={fetchNextPage}
        refreshing={refreshing}
        onRefresh={handleRefresh}
        estimatedItemSize={300}
        renderItem={renderBrandItem}
        skeletonComponent={<SkeletonBrandList />}
        emptyComponent={<EmptyBrandList />}
        count={6}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  section: { flex: 1 },
  brandCard: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    borderRadius: scale(16),
    padding: scale(16),
    marginBottom: scale(16),
  },
  brandContent: {
    flexDirection: "row",
    alignItems: "center",
    flex: 1,
  },
  brandIcon: {
    width: scale(48),
    height: scale(48),
    borderRadius: scale(12),
    justifyContent: "center",
    alignItems: "center",
    marginRight: scale(16),
  },
  logoImage: {
    width: scale(32),
    height: scale(32),
    borderRadius: scale(8),
  },
  brandInfo: {
    flex: 1,
  },
  editButton: {
    padding: scale(8),
  },
});
