// FavoriteBrands.tsx
import { scale } from "@/src/_helper/Scaler";
import { InfiniteScrollList } from "@/src/components/shared/InfiniteScrollList";
import { ThemedText } from "@/src/components/ui/ThemedText";
import { useLanguage } from "@/src/context/LanguageContext";

import useGetFavoriteBrand from "@/src/services/querys/client/useGetFavoriteBrand";
import { IClientBrand } from "@/src/types";
import { router } from "expo-router";
import React, { useMemo, useState } from "react";
import { StyleSheet, View } from "react-native";
import SkeletonFavoriteBrandCard from "../../loader/FavoriteBrandSkelaton";
import EmptyFavoriteBrandsList from "../../ui/EmptyFavoriteBrandsList";
import { ErrorComponentSmall } from "../../ui/ErrorComponentSmall";
import FavoriteBrandCard from "./FavoriteBrandCard";

export default function FavoriteBrands() {
  const { t } = useLanguage();
  const {
    data,
    isLoading,
    hasNextPage,
    fetchNextPage,
    isFetchingNextPage,
    refetch,
    error,
  } = useGetFavoriteBrand();
  const totalItems = data?.pages?.[0]?.data?.totalItems ?? 0;

  const [refreshing, setRefreshing] = useState(false);

  const favoriteBrands = useMemo(() => {
    // Add null/undefined checks for data and pages

    return data?.pages.flatMap((page) => {
      // Add null/undefined check for page data
      if (!page?.data?.items) {
        return [];
      }

      return page.data.items
        .filter((brand: IClientBrand) => brand && brand._id) // Filter out invalid items
        .map((brand: IClientBrand) => ({
          id: brand._id,
          title: brand.name || "",
          image: { uri: brand.logo || "" },
        }));
    });
  }, [data]);

  const handleRefresh = async () => {
    setRefreshing(true);
    try {
      await refetch();
    } finally {
      setRefreshing(false);
    }
  };

  const handleBrandPress = (id: string) => {
    if (id) {
      router.push(`/(client)/(screens)/brands/${id}/brand-details`);
    }
  };

  const renderFavoriteBrandItem = ({ item }: any) => (
    <FavoriteBrandCard {...item} onPress={() => handleBrandPress(item?.id)} />
  );

  // Safe key extractor with fallback
  const keyExtractor = (item: any, index: number) => {
    return item?.id?.toString() || `brand-${index}`;
  };

  if (error) {
    return (
      <>
        <View style={styles.header}>
          <ThemedText size={18} type="bold">
            {t("client.favorites.brands")} ({totalItems})
          </ThemedText>
        </View>
        <ErrorComponentSmall />
      </>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <ThemedText size={18} type="bold">
          {t("client.favorites.brands")} ({totalItems})
        </ThemedText>
      </View>
      <InfiniteScrollList
        data={favoriteBrands}
        renderItem={renderFavoriteBrandItem}
        keyExtractor={keyExtractor}
        estimatedItemSize={30}
        onEndReached={fetchNextPage}
        onEndReachedThreshold={0.1}
        hasNextPage={hasNextPage}
        isFetchingNextPage={isFetchingNextPage}
        isLoading={isLoading}
        onRefresh={handleRefresh}
        refreshing={refreshing}
        skeletonComponent={<SkeletonFavoriteBrandCard />}
        emptyComponent={<EmptyFavoriteBrandsList />}
        count={6}
        horizontal={true}
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={{ paddingRight: scale(10) }}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    marginTop: scale(10),
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: scale(10),
  },
});
