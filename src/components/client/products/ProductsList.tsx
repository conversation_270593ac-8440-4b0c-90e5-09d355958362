import { scale } from "@/src/_helper/Scaler";
import { InfiniteScrollList } from "@/src/components/shared/InfiniteScrollList";
import EmptyProductList from "@/src/components/ui/EmptyProductList";
import { ErrorComponent } from "@/src/components/ui/ErrorComponent";
import { useLanguage } from "@/src/context/LanguageContext";
import useGetProducts from "@/src/services/querys/client/useGetProducts";
import { IClientProduct } from "@/src/types";
import React, { useMemo, useState } from "react";
import { ImageProps, StyleSheet, View } from "react-native";
import SkeletonProductsListItem from "../../loader/SkelatonProductItem";
import { ThemedText } from "../../ui/ThemedText";
import ProductsListItem from "./ProductsListItem";

const ProductsList = () => {
  const { t } = useLanguage();
  const {
    data,
    isLoading,
    hasNextPage,
    fetchNextPage,
    isFetchingNextPage,
    refetch,
    error,
  } = useGetProducts();

  const [refreshing, setRefreshing] = useState(false);
  const totalItems = data?.pages?.[0]?.data?.totalItems ?? 0;
  const products = useMemo(() => {
    return (
      data?.pages.flatMap((page) =>
        page.data.items.map((product: IClientProduct) => ({
          id: product._id,
          title: product.name,
          image:
            product.images.length > 0
              ? { uri: product.images[0] }
              : (require("@/src/assets/images/icon.png") as ImageProps), // fallback image
          price: `${product.price}`,
        }))
      ) || []
    );
  }, [data]);

  const handleRefetch = async () => await refetch();
  const handleRefresh = async () => {
    setRefreshing(true);
    try {
      await refetch();
    } finally {
      setRefreshing(false);
    }
  };

  if (error) {
    return (
      <ErrorComponent
        error={t("error.generic.title")}
        onRetry={handleRefetch}
      />
    );
  }

  return (
    <View style={styles.productsSection}>
      <ThemedText type="bold" size={20} style={{ marginBottom: scale(16) }}>
        {t("client.products.titleWithCount", { count: totalItems })}
      </ThemedText>
      <InfiniteScrollList
        data={products}
        isLoading={isLoading}
        isFetchingNextPage={isFetchingNextPage}
        hasNextPage={hasNextPage}
        fetchNextPage={fetchNextPage}
        refreshing={refreshing}
        onRefresh={handleRefresh}
        estimatedItemSize={300}
        numColumns={2}
        renderItem={({ item }) => (
          <ProductsListItem
            image={item.image}
            title={item.title}
            price={item.price}
            id={item.id}
          />
        )}
        skeletonComponent={<SkeletonProductsListItem />}
        emptyComponent={<EmptyProductList />}
        count={6}
      />
    </View>
  );
};

export default ProductsList;

const styles = StyleSheet.create({
  productsSection: {
    flex: 1,
  },
});
