import { scale } from "@/src/_helper/Scaler";
import { Colors } from "@/src/constants/Colors";
import { useTheme } from "@/src/context/ThemeContext";
import { router } from "expo-router";
import React from "react";
import {
  Image,
  ImageURISource,
  StyleSheet,
  TouchableOpacity,
  View,
} from "react-native";
import { ThemedText } from "../../ui/ThemedText";

const ProductsListItem = ({
  image,
  title,
  price,
  id,
}: {
  image: ImageURISource;
  title: string;
  price: string;
  id: string;
}) => {
  const { currentTheme } = useTheme();

  return (
    <TouchableOpacity
      style={styles.container}
      activeOpacity={0.7}
      onPress={() => {
        router.push(`/(client)/(screens)/products/${id}/product`);
      }}
    >
      <View
        style={{
          backgroundColor: Colors[currentTheme ?? "dark"].secondary,
          justifyContent: "center",
          alignItems: "center",
          borderRadius: scale(12),
          height: scale(160),
          width: scale(160),
        }}
      >
        <Image
          source={image || require("@/src/assets/images/icon.png")}
          style={styles.image}
          resizeMode="cover"
        />
      </View>

      <View
        style={{
          display: "flex",
          flexDirection: "column",
          justifyContent: "flex-start",
          alignSelf: "flex-start",
        }}
      >
        <ThemedText size={16} type="semi-bold">
          {title}
        </ThemedText>
        <ThemedText
          type="semi-bold"
          style={{ color: Colors[currentTheme ?? "dark"].secondary }}
        >
          {price}
        </ThemedText>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: "flex-start",
    flexDirection: "column",
    gap: scale(20),
    marginBottom: scale(20),
    width: scale(160),
    marginRight: scale(10),
  },
  image: {
    width: scale(160),
    height: scale(160),
    borderRadius: scale(12),
  },
});

export default ProductsListItem;
