import { scale } from "@/src/_helper/Scaler";
import { ThemedText } from "@/src/components/ui/ThemedText";
import { ThemedView } from "@/src/components/ui/ThemedView";
import { Colors } from "@/src/constants/Colors";
import { useTheme } from "@/src/context/ThemeContext";
import { INotification } from "@/src/types/notifications";
import { FontAwesome, Ionicons, MaterialIcons } from "@expo/vector-icons";
import React from "react";
import { StyleSheet, View } from "react-native";

interface NotificationListItemProps {
  notification: INotification;
}

const NotificationListItem: React.FC<NotificationListItemProps> = ({
  notification,
}) => {
  const { currentTheme } = useTheme();
  const colors = Colors[currentTheme ?? "dark"];

  const getNotificationIcon = () => {
    let IconComponent: React.ComponentType<any> = FontAwesome;
    let iconName: string = "bell";
    let iconColor = colors.primary;

    switch (notification.type) {
      case "order":
        IconComponent = MaterialIcons;
        iconName = "shopping-bag" as keyof typeof MaterialIcons.glyphMap;
        iconColor = colors.primary;
        break;
      case "review":
        IconComponent = Ionicons;
        iconName = "star" as keyof typeof Ionicons.glyphMap;
        iconColor = colors.notification_review;
        break;
      case "favorite":
        IconComponent = Ionicons;
        iconName = "heart" as keyof typeof Ionicons.glyphMap;
        iconColor = colors.notification_favorite;
        break;
      case "reccomendation":
        IconComponent = Ionicons;
        iconName = "thumbs-up" as keyof typeof Ionicons.glyphMap;
        iconColor = colors.notification_recommendation;
        break;
      case "other":
      default:
        IconComponent = FontAwesome;
        iconName = "bell" as keyof typeof FontAwesome.glyphMap;
        iconColor = colors.primary;
        break;
    }

    return { IconComponent, iconName, iconColor };
  };

  const { IconComponent, iconName, iconColor } = getNotificationIcon();

  // Format timestamp if needed (you might want to add a utility function for this)
  const formatTimestamp = (timestamp: string) => {
    // Add your timestamp formatting logic here
    // For now, returning as is
    return timestamp;
  };

  return (
    <ThemedView style={[styles.card]}>
      <View style={styles.content}>
        {/* Notification Icon */}
        <View
          style={[styles.iconContainer, { backgroundColor: iconColor + "20" }]}
        >
          <IconComponent name={iconName} size={24} color={iconColor} />
        </View>

        {/* Notification Content */}
        <View style={styles.textContent}>
          <ThemedText
            size={16}
            type="semi-bold"
            style={[styles.title, { color: colors.text }]}
            numberOfLines={2}
          >
            {notification.title}
          </ThemedText>

          <ThemedText
            size={14}
            style={[styles.message, { color: colors.secondary }]}
            numberOfLines={3}
          >
            {notification.message}
          </ThemedText>
        </View>
      </View>
    </ThemedView>
  );
};

const styles = StyleSheet.create({
  card: {
    borderRadius: scale(16),
    paddingVertical: scale(16),
    marginBottom: scale(12),
  },
  content: {
    flexDirection: "row",
    alignItems: "flex-start",
  },
  iconContainer: {
    width: scale(48),
    height: scale(48),
    borderRadius: scale(24),
    alignItems: "center",
    justifyContent: "center",
    marginRight: scale(12),
  },
  textContent: {
    flex: 1,
    gap: scale(4),
  },
  title: {
    lineHeight: scale(20),
  },
  message: {
    lineHeight: scale(18),
  },
  timestamp: {
    marginTop: scale(4),
  },
  rightSection: {
    alignItems: "center",
    justifyContent: "center",
    marginLeft: scale(8),
  },
});

export default NotificationListItem;
