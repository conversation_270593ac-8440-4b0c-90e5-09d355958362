import { scale } from "@/src/_helper/Scaler";
import { Colors } from "@/src/constants/Colors";
import { useTheme } from "@/src/context/ThemeContext";
import React from "react";
import { ImageProps, TouchableOpacity } from "react-native";
import { ThemedText } from "../../ui/ThemedText";

const ShopCategory = ({
  image,
  name,
  onPress,
}: {
  image: ImageProps | null;
  name: string;
  onPress?: () => void;
}) => {
  const { currentTheme } = useTheme();

  return (
    <TouchableOpacity
      onPress={onPress}
      activeOpacity={0.7}
      style={{
        backgroundColor: Colors[currentTheme ?? "dark"].background,
        borderColor: Colors[currentTheme ?? "dark"].border,
        borderWidth: 1,
        borderRadius: scale(10),
        height: scale(70),
        width: scale(160),
        display: "flex",
        flexDirection: "row",
        justifyContent: "flex-start",
        padding: scale(10),
        alignItems: "center",
      }}
    >
      <ThemedText
        numberOfLines={2}
        style={{ color: Colors[currentTheme ?? "dark"].text }}
        size={12}
        type="bold"
      >
        {name}
      </ThemedText>
    </TouchableOpacity>
  );
};

export default ShopCategory;
