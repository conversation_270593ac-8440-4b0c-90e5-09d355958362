import { scale } from "@/src/_helper/Scaler";
import { Colors } from "@/src/constants/Colors";
import { useTheme } from "@/src/context/ThemeContext";
import React from "react";
import { ImageProps, View } from "react-native";
import { ThemedText } from "../../ui/ThemedText";

const ShopCategory = ({
  image,
  name,
}: {
  image: ImageProps | null;
  name: string;
}) => {
  const { currentTheme } = useTheme();

  return (
    <View
      style={{
        backgroundColor: Colors[currentTheme ?? "dark"].background,
        borderColor: Colors[currentTheme ?? "dark"].border,
        borderWidth: 1,
        borderRadius: scale(10),
        height: scale(70),
        width: scale(160),
        display: "flex",
        flexDirection: "row",
        justifyContent: "flex-start",
        padding: scale(10),
        alignItems: "center",
      }}
    >
      <ThemedText
        numberOfLines={2}
        style={{ color: Colors[currentTheme ?? "dark"].text }}
        size={12}
        type="bold"
      >
        {name}
      </ThemedText>
    </View>
  );
};

export default ShopCategory;
