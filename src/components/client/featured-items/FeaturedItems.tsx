import { scale } from "@/src/_helper/Scaler";
import { Colors } from "@/src/constants/Colors";
import { useTheme } from "@/src/context/ThemeContext";
import React, { useState } from "react";
import {
  Dimensions,
  FlatList,
  Image,
  NativeScrollEvent,
  NativeSyntheticEvent,
  StyleSheet,
  View,
} from "react-native";
import { ThemedText } from "../../ui/ThemedText";

const { width } = Dimensions.get("window");

const featuredItems = [
  {
    title: "The Artisan Collective",
    description: "Discover unique handcrafted goods from local artisans....",
    image: require("@/src/assets/images/icon.png"),
  },
  {
    title: "Vintage Finds",
    description: "Timeless vintage pieces curated for modern living.",
    image: require("@/src/assets/images/icon.png"),
  },
  {
    title: "Local Pottery Studio",
    description: "Beautiful ceramics made by local artists.",
    image: require("@/src/assets/images/icon.png"),
  },
  {
    title: "Handwoven Textiles",
    description: "Explore rich textures and patterns from local weavers.",
    image: require("@/src/assets/images/icon.png"),
  },
];

const FeaturedItems = () => {
  const { currentTheme } = useTheme();
  const [activeIndex, setActiveIndex] = useState(0);

  // Handle scroll to update active dot index
  const onScroll = (event: NativeSyntheticEvent<NativeScrollEvent>) => {
    const slideSize = event.nativeEvent.layoutMeasurement.width;
    const index = Math.floor(event.nativeEvent.contentOffset.x / slideSize);
    setActiveIndex(index);
  };

  const renderItem = ({ item }: any) => (
    <View
      style={{
        width,
        justifyContent: "flex-start",
      }}
    >
      <View
        style={{
          flexDirection: "row",
          gap: scale(10),
          paddingHorizontal: scale(15),
          alignItems: "center",
        }}
      >
        <View style={{ flex: 1 }}>
          <ThemedText
            style={{ color: Colors[currentTheme ?? "dark"].secondary }}
          >
            Featured
          </ThemedText>
          <ThemedText size={16} type="bold">
            {item.title}
          </ThemedText>
          <ThemedText
            style={{ color: Colors[currentTheme ?? "dark"].secondary }}
            numberOfLines={0} // allow wrapping
          >
            {item.description}
          </ThemedText>
        </View>
        <Image
          source={item.image}
          style={{
            height: scale(93),
            width: scale(138),
            backgroundColor: Colors[currentTheme ?? "dark"].secondary,
            borderRadius: scale(10),
          }}
          resizeMode="cover"
        />
      </View>
    </View>
  );

  return (
    <View>
      <FlatList
        data={featuredItems}
        renderItem={renderItem}
        keyExtractor={(_, index) => index.toString()}
        horizontal
        pagingEnabled
        snapToInterval={width}
        snapToAlignment="start"
        decelerationRate="fast"
        showsHorizontalScrollIndicator={false}
        onScroll={onScroll}
        scrollEventThrottle={16}
      />

      {/* Dots container */}
      <View style={styles.dotsContainer}>
        {featuredItems.map((_, index) => (
          <View
            key={index}
            style={[
              styles.dot,
              {
                backgroundColor:
                  index === activeIndex
                    ? Colors[currentTheme ?? "dark"].primary
                    : Colors[currentTheme ?? "dark"].border, // add transparency
              },
            ]}
          />
        ))}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  dotsContainer: {
    flexDirection: "row",
    justifyContent: "center",
    marginTop: scale(10),
  },
  dot: {
    width: scale(8),
    height: scale(8),
    borderRadius: scale(20),
    marginHorizontal: scale(4),
  },
});

export default FeaturedItems;
