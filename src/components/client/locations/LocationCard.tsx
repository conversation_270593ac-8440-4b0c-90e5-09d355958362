import { scale, scaleFont } from "@/src/_helper/Scaler";
import { ThemedText } from "@/src/components/ui/ThemedText";
import { ThemedView } from "@/src/components/ui/ThemedView";
import { Colors } from "@/src/constants/Colors";
import { useLanguage } from "@/src/context/LanguageContext";
import { useTheme } from "@/src/context/ThemeContext";
import { ILocation } from "@/src/types/location";
import { MaterialIcons } from "@expo/vector-icons";
import React from "react";
import { StyleSheet, TouchableOpacity, View } from "react-native";

interface LocationCardProps {
  item: ILocation;
  onEdit?: (location: ILocation) => void;
  onDelete?: (location: ILocation) => void;
}

export default function LocationCard({
  item,
  onEdit,
  onDelete,
}: LocationCardProps) {
  const { currentTheme } = useTheme();
  const { t } = useLanguage();
  const colors = Colors[currentTheme ?? "dark"];

  const getTypeIcon = (type: string) => {
    switch (type) {
      case "home":
        return "home";
      case "work":
      case "workplace":
        return "work";
      case "brand":
        return "store";
      default:
        return "place";
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case "home":
        return colors.location_home;
      case "work":
      case "workplace":
        return colors.location_work;
      case "brand":
        return colors.location_brand;
      default:
        return colors.error;
    }
  };

  const handleEdit = () => {
    onEdit?.(item);
  };

  const handleDelete = () => {
    onDelete?.(item);
  };

  return (
    <ThemedView
      style={[
        styles.card,
        {
          shadowColor: colors.text,
        },
      ]}
    >
      <View style={styles.content}>
        {/* Location Icon */}
        <View
          style={[
            styles.iconContainer,
            { backgroundColor: getTypeColor(item.type) + "20" },
          ]}
        >
          <MaterialIcons
            name={getTypeIcon(item.type)}
            size={scaleFont(24)}
            color={getTypeColor(item.type)}
          />
        </View>

        {/* Location Info */}
        <View style={styles.info}>
          <View style={styles.headerSection}>
            <ThemedText type="semi-bold" size={16} style={styles.addressText}>
              {item.address}
            </ThemedText>
            <View
              style={[
                styles.typeBadge,
                { backgroundColor: getTypeColor(item.type) },
              ]}
            >
              <ThemedText size={10} type="semi-bold" style={{ color: "black" }}>
                {t(`addresses.types.${item.type}`).toUpperCase()}
              </ThemedText>
            </View>
          </View>

          <View style={styles.detailsSection}>
            {/* City, State */}
            <View style={styles.detailRow}>
              <MaterialIcons
                name="location-city"
                size={scaleFont(14)}
                color={colors.text}
                style={styles.detailIcon}
              />
              <ThemedText size={14} style={styles.detailText}>
                {item.city}, {item.state}
              </ThemedText>
            </View>

            {/* Country */}
            <View style={styles.detailRow}>
              <MaterialIcons
                name="public"
                size={scaleFont(14)}
                color={colors.text}
                style={styles.detailIcon}
              />
              <ThemedText size={14} style={styles.detailText}>
                {item.country}
              </ThemedText>
            </View>

            {/* Zip Code */}
            {item.zipCode && (
              <View style={styles.detailRow}>
                <MaterialIcons
                  name="markunread-mailbox"
                  size={scaleFont(14)}
                  color={colors.text}
                  style={styles.detailIcon}
                />
                <ThemedText size={14} style={styles.detailText}>
                  {item.zipCode}
                </ThemedText>
              </View>
            )}
          </View>
        </View>

        {/* Action Buttons */}
        <View style={styles.actionsContainer}>
          <TouchableOpacity
            style={[
              styles.actionButton,
              { backgroundColor: colors.primary + "20" },
            ]}
            onPress={handleEdit}
            activeOpacity={0.7}
          >
            <MaterialIcons
              name="edit"
              size={scaleFont(18)}
              color={colors.primary}
            />
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.actionButton, { backgroundColor: "#F44336" + "20" }]}
            onPress={handleDelete}
            activeOpacity={0.7}
          >
            <MaterialIcons name="delete" size={scaleFont(18)} color="#F44336" />
          </TouchableOpacity>
        </View>
      </View>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  card: {
    borderRadius: scale(16),
    padding: scale(16),
    marginBottom: scale(12),
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 3,
  },
  content: {
    flexDirection: "row",
    alignItems: "flex-start",
  },
  iconContainer: {
    width: scale(50),
    height: scale(50),
    borderRadius: scale(25),
    alignItems: "center",
    justifyContent: "center",
    marginRight: scale(16),
  },
  info: {
    flex: 1,
  },
  headerSection: {
    flexDirection: "row",
    alignItems: "flex-start",
    justifyContent: "space-between",
    marginBottom: scale(8),
  },
  addressText: {
    flex: 1,
    marginRight: scale(8),
  },
  typeBadge: {
    paddingHorizontal: scale(8),
    paddingVertical: scale(4),
    borderRadius: scale(12),
  },
  detailsSection: {
    gap: scale(4),
  },
  detailRow: {
    flexDirection: "row",
    alignItems: "center",
  },
  detailIcon: {
    marginRight: scale(8),
    width: scale(16),
  },
  detailText: {
    opacity: 0.8,
  },
  actionsContainer: {
    flexDirection: "column",
    gap: scale(8),
    marginLeft: scale(8),
  },
  actionButton: {
    width: scale(36),
    height: scale(36),
    borderRadius: scale(18),
    alignItems: "center",
    justifyContent: "center",
  },
});
