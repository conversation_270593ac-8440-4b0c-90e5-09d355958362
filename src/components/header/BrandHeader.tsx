import { scale } from "@/src/_helper/Scaler";
import { ThemedText } from "@/src/components/ui/ThemedText";
import { Colors } from "@/src/constants/Colors";
import { useSession } from "@/src/context/AuthContext";
import { useLanguage } from "@/src/context/LanguageContext";
import { useTheme } from "@/src/context/ThemeContext";
import { StyleSheet, View } from "react-native";

export default function BrandHeaderSection() {
  const { session, isLoading } = useSession();
  const { currentTheme } = useTheme();
  const { t } = useLanguage();
  const theme = currentTheme ?? "dark";

  return (
    <View style={styles.header}>
      <ThemedText type="bold" size={22} numberOfLines={1} ellipsizeMode="tail">
        {t("vendor.headers.welcomeBack")},{" "}
        <ThemedText
          type="bold"
          size={22}
          style={{
            color: Colors[theme].primary,
          }}
        >
          {isLoading ? "..." : session?.name}
        </ThemedText>
      </ThemedText>
      <ThemedText size={16}>{t("vendor.headers.brandsSubtitle")}</ThemedText>
    </View>
  );
}

const styles = StyleSheet.create({
  header: {
    marginBottom: scale(15),
  },
});
