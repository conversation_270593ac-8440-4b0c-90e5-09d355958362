import { scale } from "@/src/_helper/Scaler";
import { ThemedText } from "@/src/components/ui/ThemedText";
import { useLanguage } from "@/src/context/LanguageContext";
import React from "react";
import { StyleSheet, View } from "react-native";

const ProductsHeader = () => {
  const { t } = useLanguage();

  return (
    <View style={styles.header}>
      <ThemedText type="bold" size={22}>
        {t("vendor.headers.products")}
      </ThemedText>
      <ThemedText size={16}>{t("vendor.headers.productsSubtitle")}</ThemedText>
    </View>
  );
};

export default ProductsHeader;

const styles = StyleSheet.create({
  header: {
    marginTop: scale(20),
    marginBottom: scale(12),
  },
});
