import { scale, scaleFont } from "@/src/_helper/Scaler";
import { Colors } from "@/src/constants/Colors";
import { useLanguage } from "@/src/context/LanguageContext";
import { useTheme } from "@/src/context/ThemeContext";
import Entypo from "@expo/vector-icons/Entypo";
import { useLocalSearchParams } from "expo-router";
import React from "react";
import {
  Share as NativeShare,
  StyleSheet,
  TouchableOpacity,
} from "react-native";

const Share: React.FC<{ type: string }> = ({ type }) => {
  const { currentTheme } = useTheme();
  const { t } = useLanguage();
  const { id } = useLocalSearchParams() as { id: string };
  const handleShare = async () => {
    try {
      const result = await NativeShare.share({
        message: `https://locasa.app/${type}/${id}`,
        url: `https://locasa.app/brands/${id}`, // Some platforms also use this
        title: t("share.title"),
      });

      if (result.action === NativeShare.sharedAction) {
        if (result.activityType) {
          console.log("Shared with activity type:", result.activityType);
        } else {
          console.log("Shared");
        }
      } else if (result.action === NativeShare.dismissedAction) {
        console.log("Share dismissed");
      }
    } catch (error) {
      console.error("Error while sharing:", error);
    }
  };

  return (
    <TouchableOpacity
      accessibilityRole="button"
      accessibilityLabel="Share"
      style={styles.button}
      onPress={handleShare}
    >
      <Entypo
        name="share"
        size={scaleFont(22)}
        color={Colors[currentTheme ?? "dark"].text}
      />
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  button: {
    height: scale(40),
    width: scale(40),
    borderRadius: scale(20),
    justifyContent: "center",
    alignItems: "center",
    marginLeft: scale(50),
  },
});

export default Share;
