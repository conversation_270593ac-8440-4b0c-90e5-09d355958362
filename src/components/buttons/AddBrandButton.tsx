import { scale } from "@/src/_helper/Scaler";
import { ThemedText } from "@/src/components/ui/ThemedText";
import { Colors } from "@/src/constants/Colors";
import { useLanguage } from "@/src/context/LanguageContext";
import { useTheme } from "@/src/context/ThemeContext";
import { router } from "expo-router";
import React from "react";
import { StyleSheet, TouchableOpacity } from "react-native";

export default function AddBrandButton({ active }: { active: boolean }) {
  const { currentTheme } = useTheme();
  const { t } = useLanguage();

  const handleAddNewBrand = () => {
    router.push(`/(vendor)/(screens)/brand/add-brand`);
  };

  return (
    <TouchableOpacity
      style={[
        styles.addButton,
        {
          backgroundColor: Colors[currentTheme ?? "dark"].thirdary,
        },
      ]}
      onPress={handleAddNewBrand}
      activeOpacity={0.8}
    >
      <ThemedText
        type="bold"
        style={{
          color: Colors.dark.black,
        }}
      >
        {t("buttons.addNewBrand")}
      </ThemedText>
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  addButton: {
    paddingVertical: scale(16),
    paddingHorizontal: scale(24),
    borderRadius: scale(25),
    alignSelf: "flex-start",
    marginBottom: scale(40),
  },
});
