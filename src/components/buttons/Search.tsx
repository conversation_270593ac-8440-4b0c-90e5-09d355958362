// SearchAndNotification.tsx
import { scale, scaleFont } from "@/src/_helper/Scaler";
import { Colors } from "@/src/constants/Colors";
import { useTheme } from "@/src/context/ThemeContext";
import { Fontisto } from "@expo/vector-icons";
import React, { useState } from "react";
import { View } from "react-native";
import SearchModal from "../search/SearchModel";

const Search: React.FC = () => {
  const [showSearch, setShowSearch] = useState(false);
  const { currentTheme } = useTheme();
  const openSearch = () => setShowSearch(true);
  const closeSearch = () => setShowSearch(false);

  return (
    <View
      style={{
        justifyContent: "flex-end",
        alignItems: "flex-end",
        paddingHorizontal: scale(20),
      }}
    >
      <Fontisto
        onPress={openSearch}
        name="search"
        size={scaleFont(22)}
        color={Colors[currentTheme ?? "dark"].text}
      />
      <SearchModal visible={showSearch} onClose={closeSearch} />
    </View>
  );
};

export default Search;
