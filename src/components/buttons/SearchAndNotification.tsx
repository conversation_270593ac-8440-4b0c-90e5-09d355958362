// SearchAndNotification.tsx
import { scale } from "@/src/_helper/Scaler";
import { router } from "expo-router";
import React, { useState } from "react";
import { View } from "react-native";
import SearchModal from "../search/SearchModel";
import SearchButtonGroup from "./SearchButtonGroup";

const SearchAndNotification: React.FC = () => {
  const [showSearch, setShowSearch] = useState(false);

  const openSearch = () => setShowSearch(true);
  const closeSearch = () => setShowSearch(false);

  const handleNotification = () => {
    router.push("/notification");
  };

  return (
    <View style={{ paddingHorizontal: scale(16) }}>
      <SearchButtonGroup
        onSearchPress={openSearch}
        onNotificationPress={handleNotification}
      />
      <SearchModal visible={showSearch} onClose={closeSearch} />
    </View>
  );
};

export default SearchAndNotification;
