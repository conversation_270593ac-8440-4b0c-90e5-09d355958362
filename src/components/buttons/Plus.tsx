// SearchAndNotification.tsx
import { scale, scaleFont } from "@/src/_helper/Scaler";
import { Colors } from "@/src/constants/Colors";
import { useTheme } from "@/src/context/ThemeContext";
import { Entypo, MaterialIcons } from "@expo/vector-icons";
import { router } from "expo-router";
import React from "react";
import { View } from "react-native";

const Plus: React.FC = () => {
  const { currentTheme } = useTheme();

  return (
    <View
      style={{
        flexDirection: "row",
        justifyContent: "flex-end",
        alignItems: "flex-end",
        paddingRight: scale(15),
        gap: scale(10),
      }}
    >
      <Entypo
        onPress={() => {
          router.push("/(vendor)/(screens)/brand/add-brand");
        }}
        name="plus"
        size={scaleFont(30)}
        color={Colors[currentTheme ?? "dark"].text}
      />
      <MaterialIcons
        onPress={() => {
          router.push("/(vendor)/(screens)/notification");
        }}
        name="notifications-none"
        size={scaleFont(30)}
        color={Colors[currentTheme ?? "dark"].text}
      />
    </View>
  );
};

export default Plus;
