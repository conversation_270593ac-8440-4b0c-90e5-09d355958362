import { scale, scaleFont } from "@/src/_helper/Scaler";
import { Colors } from "@/src/constants/Colors";
import { useTheme } from "@/src/context/ThemeContext";
import React from "react";
import {
  ActivityIndicator,
  StyleSheet,
  TouchableOpacity,
  View,
  ViewStyle,
} from "react-native";
import { ThemedText } from "../ui/ThemedText";

export interface DefaultButtonProps {
  onPress: () => void;
  title: string;
  titleTranslationKey?: string;
  disabled?: boolean;
  style?: any;
  isSubmitting?: boolean;
  color?: string;
  size?: number | "small" | "large" | undefined;
  active?: boolean;
  children?: React.ReactNode; // for icons
}

const DefaultButton: React.FC<DefaultButtonProps> = ({
  onPress,
  title,
  disabled = false,
  style,
  isSubmitting,
  size = "small",
  color,
  active = false,
  children,
}) => {
  const { currentTheme } = useTheme();

  const containerStyle: ViewStyle[] = [
    styles.button,
    {
      opacity: disabled ? 0.6 : 1,
      backgroundColor: color ? color : Colors[currentTheme ?? "dark"].primary,
    },
    style,
  ];

  return (
    <TouchableOpacity
      style={containerStyle}
      onPress={onPress}
      disabled={disabled}
      activeOpacity={0.8}
    >
      {isSubmitting ? (
        <ActivityIndicator
          size={scaleFont(20)}
          color={
            active
              ? currentTheme === "dark"
                ? Colors.dark.black
                : Colors.light.black
              : currentTheme === "dark"
              ? Colors.dark.black
              : Colors.light.white
          }
        />
      ) : (
        <View style={styles.inner}>
          {children}
          <ThemedText
            style={{
              color: active
                ? currentTheme === "dark"
                  ? Colors.dark.black
                  : Colors.light.black
                : currentTheme === "dark"
                ? Colors.dark.black
                : Colors.light.white,
            }}
            type="bold"
          >
            {title}
          </ThemedText>
        </View>
      )}
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  button: {
    alignSelf: "center",
    width: "95%",
    borderRadius: scale(12),
    justifyContent: "center",
    alignItems: "center",
    paddingVertical: scale(14),
    marginBottom: scale(7),
    marginTop: scale(10),
  },
  inner: {
    flexDirection: "row",
    alignItems: "center",
    gap: scale(8),
  },
});

export default DefaultButton;
