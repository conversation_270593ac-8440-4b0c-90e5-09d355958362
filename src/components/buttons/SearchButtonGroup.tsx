import { scale, scaleFont } from "@/src/_helper/Scaler";
import { Colors } from "@/src/constants/Colors";
import { useTheme } from "@/src/context/ThemeContext";
import Fontisto from "@expo/vector-icons/Fontisto";
import MaterialIcons from "@expo/vector-icons/MaterialIcons";
import React from "react";
import { StyleSheet, View } from "react-native";

type SearchButtonGroupProps = {
  onSearchPress: () => void;
  onNotificationPress: () => void;
};

const SearchButtonGroup: React.FC<SearchButtonGroupProps> = ({
  onSearchPress,
  onNotificationPress,
}) => {
  const { currentTheme } = useTheme();

  return (
    <View style={styles.button}>
      <Fontisto
        onPress={onSearchPress}
        name="search"
        size={scaleFont(22)}
        color={Colors[currentTheme ?? "dark"].text}
      />
      <MaterialIcons
        onPress={onNotificationPress}
        name="notifications-none"
        size={scaleFont(28)}
        color={Colors[currentTheme ?? "dark"].text}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  button: {
    height: scale(50),
    width: "100%",
    gap: scale(25),
    flexDirection: "row",
    alignItems: "center",
  },
});

export default SearchButtonGroup;
