import { scale, scaleFont } from "@/src/_helper/Scaler";
import { Colors } from "@/src/constants/Colors";
import { useLanguage } from "@/src/context/LanguageContext";
import { useTheme } from "@/src/context/ThemeContext";
import Entypo from "@expo/vector-icons/Entypo";
import { router } from "expo-router";
import React from "react";
import { StyleSheet, TouchableOpacity } from "react-native";

const HelpButton: React.FC = () => {
  const { currentTheme } = useTheme();
  const { t } = useLanguage();

  return (
    <TouchableOpacity
      accessibilityRole="button"
      accessibilityLabel={t("buttons.help")}
      onPress={() => {
        router.push("/(user-entry)/(help)/help");
      }}
      style={[styles.button]}
    >
      <Entypo
        name="help-with-circle"
        size={scaleFont(24)}
        color={Colors[currentTheme ?? "dark"].text}
      />
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  button: {
    height: "100%",
    width: scale(100),
    justifyContent: "center",
    alignItems: "flex-end",
  },
});

export default HelpButton;
