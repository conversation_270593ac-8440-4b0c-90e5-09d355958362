import { scale, scaleFont } from "@/src/_helper/Scaler";
import { Colors } from "@/src/constants/Colors";
import { useTheme } from "@/src/context/ThemeContext";
import { Feather } from "@expo/vector-icons";
import React from "react";
import { StyleSheet, View, ViewStyle } from "react-native";
import { Gesture, GestureDetector } from "react-native-gesture-handler";
import Animated, {
  interpolate,
  runOnJS,
  useAnimatedStyle,
  useSharedValue,
  withSpring,
  withTiming,
} from "react-native-reanimated";
import { ThemedText } from "../ui/ThemedText";

export interface SwipeToRightButtonProps {
  onSwipeComplete: () => void;
  title: string;
  disabled?: boolean;
  style?: ViewStyle;
  color?: string;
  swipeThreshold?: number; // Percentage of button width to trigger action (0-1)
  resetAfterComplete?: boolean; // Whether to reset the button after completion
  active?: boolean;
}

const SwipeToRightButton: React.FC<SwipeToRightButtonProps> = ({
  onSwipeComplete,
  title,
  disabled = false,
  style,
  color,
  swipeThreshold = 0.8,
  resetAfterComplete = true,
  active,
}) => {
  const { currentTheme } = useTheme();
  const translateX = useSharedValue(0);
  const buttonWidth = useSharedValue(0);
  const isCompleted = useSharedValue(false);

  // Pre-calculate scaled values to avoid calling scale() on UI thread
  const thumbSize = scale(60);
  const textFadeThreshold = 0.3;

  const handleSwipeComplete = () => {
    isCompleted.value = true;
    onSwipeComplete();

    if (resetAfterComplete) {
      // Reset after a short delay
      setTimeout(() => {
        translateX.value = withSpring(0);
        isCompleted.value = false;
      }, 1000);
    }
  };

  const gesture = Gesture.Pan()
    .enabled(!disabled)
    .onUpdate((event) => {
      if (isCompleted.value) return;

      // Only allow rightward swipes
      const newTranslateX = Math.max(0, event.translationX);
      const maxTranslate = buttonWidth.value - thumbSize; // Account for thumb size

      translateX.value = Math.min(newTranslateX, maxTranslate);
    })
    .onEnd(() => {
      if (isCompleted.value) return;

      const threshold = buttonWidth.value * swipeThreshold;

      if (translateX.value >= threshold) {
        // Complete the swipe
        translateX.value = withTiming(buttonWidth.value - thumbSize, {
          duration: 200,
        });
        runOnJS(handleSwipeComplete)();
      } else {
        // Snap back to start
        translateX.value = withSpring(0);
      }
    });

  const thumbStyle = useAnimatedStyle(() => {
    return {
      transform: [{ translateX: translateX.value }],

      opacity: disabled ? 0.6 : 1,
    };
  });

  const backgroundStyle = useAnimatedStyle(() => {
    const startColor = color || Colors[currentTheme ?? "dark"].primary;

    return {
      backgroundColor: startColor,
      opacity: disabled ? 0.6 : 1,
    };
  });

  const textStyle = useAnimatedStyle(() => {
    const progress = interpolate(
      translateX.value,
      [0, buttonWidth.value * textFadeThreshold],
      [1, 0],
      "clamp"
    );

    return {
      opacity: progress,
    };
  });

  const iconStyle = useAnimatedStyle(() => {
    const progress = interpolate(
      translateX.value,
      [0, buttonWidth.value - thumbSize],
      [0, 1],
      "clamp"
    );

    return {
      transform: [
        {
          rotate: `${interpolate(progress, [0, 1], [0, 360], "clamp")}deg`,
        },
      ],
    };
  });

  // Animated icon component that reacts to isCompleted changes
  const AnimatedIcon = () => {
    // Use a regular state that gets updated when isCompleted changes
    const [currentIcon, setCurrentIcon] = React.useState<
      "arrow-right" | "check"
    >("arrow-right");

    React.useEffect(() => {
      const updateIcon = () => {
        setCurrentIcon(isCompleted.value ? "check" : "arrow-right");
      };

      // Update immediately
      updateIcon();

      // Set up a listener for changes (we'll use a simple interval check)
      const interval = setInterval(updateIcon, 50);

      return () => clearInterval(interval);
    }, []);

    return (
      <Feather
        name={currentIcon}
        size={scaleFont(24)}
        color={Colors[currentTheme ?? "dark"].black}
      />
    );
  };

  return (
    <View style={[styles.container, style]}>
      <Animated.View
        style={[styles.button, backgroundStyle]}
        onLayout={(event) => {
          buttonWidth.value = event.nativeEvent.layout.width;
        }}
      >
        <Animated.View style={[styles.textContainer, textStyle]}>
          <ThemedText
            style={[
              {
                color: active
                  ? currentTheme === "dark"
                    ? Colors.dark.black
                    : Colors.light.black
                  : currentTheme === "dark"
                  ? Colors.dark.black
                  : Colors.light.white,
              },
            ]}
            type="bold"
          >
            {title}
          </ThemedText>
        </Animated.View>

        <GestureDetector gesture={gesture}>
          <Animated.View style={[styles.thumb, thumbStyle]}>
            <Animated.View style={iconStyle}>
              <AnimatedIcon />
            </Animated.View>
          </Animated.View>
        </GestureDetector>
      </Animated.View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    width: "95%",
    alignSelf: "center",
    marginVertical: scale(10),
  },
  button: {
    height: scale(50),
    borderRadius: scale(30),
    justifyContent: "center",
    position: "relative",
    overflow: "hidden",
  },
  textContainer: {
    position: "absolute",
    left: 0,
    right: 0,
    alignItems: "center",
    justifyContent: "center",
    height: "100%",
  },

  thumb: {
    position: "absolute",
    left: scale(4),
    top: scale(4),
    width: scale(44),
    height: scale(44),
    borderRadius: scale(26),
    backgroundColor: "white",
    justifyContent: "center",
    alignItems: "center",
    elevation: 3,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
});

export default SwipeToRightButton;
