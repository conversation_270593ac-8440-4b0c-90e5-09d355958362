import { scaleFont } from "@/src/_helper/Scaler";
import { Colors } from "@/src/constants/Colors";
import { useTheme } from "@/src/context/ThemeContext";
import AntDesign from "@expo/vector-icons/AntDesign";
import { router, useNavigation } from "expo-router";
import React from "react";
import { StyleSheet, TouchableOpacity } from "react-native";
interface CustomBackButtonProps {
  onPress?: () => void;
}

const CustomBackButton: React.FC<CustomBackButtonProps> = ({ onPress }) => {
  const navigation = useNavigation();
  const { currentTheme } = useTheme();

  return (
    <TouchableOpacity
      onPress={() => {
        if (onPress) {
          onPress();
          return;
        }
        if (navigation.canGoBack()) {
          navigation.goBack();
        } else {
          router.replace("/");
        }
      }}
      style={[styles.button]}
    >
      <AntDesign
        name="arrowleft"
        size={scaleFont(24)}
        color={Colors[currentTheme ?? "dark"].text}
      />
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  button: {
    justifyContent: "center",
    height: "100%",
    alignItems: "flex-start",
    alignSelf: "flex-start",
  },
});

export default CustomBackButton;
