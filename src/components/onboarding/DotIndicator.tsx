import { scale } from "@/src/_helper/Scaler";
import { Colors } from "@/src/constants/Colors";
import { useTheme } from "@/src/context/ThemeContext";
import React from "react";
import { StyleSheet, View } from "react-native";

interface DotProps {
  currentIndex: number;
  length: number;
}

export const DotIndicator = ({ currentIndex, length }: DotProps) => {
  const { currentTheme } = useTheme();
  return (
    <View style={styles.container}>
      {Array(length)
        .fill(0)
        .map((_, i) => (
          <View
            key={i}
            style={[
              styles.dot,
              currentIndex === i
                ? [
                    styles.activeDot,
                    {
                      backgroundColor:
                        Colors[currentTheme ?? "dark"].input_text,
                    },
                  ]
                : [
                    styles.inactiveDot,
                    {
                      backgroundColor: Colors[currentTheme ?? "dark"].input,
                    },
                  ],
            ]}
          />
        ))}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: "row",
    justifyContent: "center",
    marginTop: scale(20),
  },
  dot: {
    height: scale(8),
    borderRadius: scale(4),
    marginHorizontal: scale(4),
  },
  activeDot: {
    width: scale(16),
  },
  inactiveDot: {
    width: scale(8),
  },
});
