import { scale, scaleFont } from "@/src/_helper/Scaler";
import { Colors } from "@/src/constants/Colors";
import { useLanguage } from "@/src/context/LanguageContext";
import { useTheme } from "@/src/context/ThemeContext";
import useGetLocations from "@/src/services/querys/client/useGetLocations";
import { ILocation } from "@/src/types/location";
import { Ionicons } from "@expo/vector-icons";
import { useNavigation } from "@react-navigation/native";
import React, { useEffect, useMemo, useState } from "react";
import { Dimensions, Modal, Pressable, StyleSheet, View } from "react-native";
import { Gesture, GestureDetector } from "react-native-gesture-handler";
import Animated, {
  runOnJS,
  useAnimatedStyle,
  useSharedValue,
  withSpring,
} from "react-native-reanimated";
import DefaultButton from "../buttons/Default";
import SkeletonAddressList from "../loader/SkeletonAddressList";
import { InfiniteScrollList } from "../shared/InfiniteScrollList";
import AddressItem from "../ui/AddressItem";
import EmptyAddressList from "../ui/EmptyAddressList";
import { ErrorComponent } from "../ui/ErrorComponent";
import { ThemedText } from "../ui/ThemedText";

import { AddressBottomSheetProps } from "@/src/types";

const { height: SCREEN_HEIGHT, width: SCREEN_WIDTH } = Dimensions.get("window");

export default function AddressBottomSheet({
  isVisible,
  onClose,
  onAddressSelect,
  onAddNewAddress,
}: AddressBottomSheetProps) {
  const { currentTheme } = useTheme();
  const { t } = useLanguage();
  const navigation = useNavigation();
  const translateY = useSharedValue(SCREEN_HEIGHT);
  const context = useSharedValue({ y: 0 });

  const {
    data,
    isLoading,
    hasNextPage,
    fetchNextPage,
    isFetchingNextPage,
    refetch,
    error,
  } = useGetLocations();

  const [refreshing, setRefreshing] = useState(false);

  const addresses = useMemo(() => {
    if (!data?.pages) return [];

    return data.pages
      .flatMap((page) => page?.data?.items || [])
      .filter((item) => item && item._id); // Filter out any invalid items
  }, [data]);

  // const totalItems = data?.pages?.[0]?.data?.totalItems ?? 0; // Can be used for header if needed

  // Handle refresh
  const handleRefresh = async () => {
    setRefreshing(true);
    try {
      await refetch();
    } catch (error) {
      console.error("Error refreshing addresses:", error);
    } finally {
      setRefreshing(false);
    }
  };

  // Handle retry for errors
  const handleRetry = async () => {
    await refetch();
  };

  // Control tab bar visibility
  useEffect(() => {
    navigation.getParent()?.setOptions({
      tabBarStyle: {
        display: isVisible ? "none" : "flex",
      },
    });

    // Cleanup: Show tab bar when component unmounts
    return () => {
      navigation.getParent()?.setOptions({
        tabBarStyle: {
          display: "flex",
        },
      });
    };
  }, [isVisible, navigation]);

  const gesture = Gesture.Pan()
    .onStart(() => {
      context.value = { y: translateY.value };
    })
    .onUpdate((e) => {
      translateY.value = e.translationY + context.value.y;
      translateY.value = Math.max(translateY.value, -SCREEN_HEIGHT * 0.4);
      translateY.value = Math.min(translateY.value, 0);
    })
    .onEnd(() => {
      if (translateY.value > -SCREEN_HEIGHT * 0.1) {
        translateY.value = withSpring(SCREEN_HEIGHT);
        runOnJS(onClose)();
      } else {
        translateY.value = withSpring(0);
      }
    });

  const reanimatedBottomStyle = useAnimatedStyle(() => {
    return {
      transform: [{ translateY: translateY.value }],
    };
  });

  const overlayStyle = useAnimatedStyle(() => {
    return {
      opacity: isVisible ? withSpring(0.5) : withSpring(0),
      pointerEvents: isVisible ? "auto" : "none",
    };
  });

  useEffect(() => {
    if (isVisible) {
      translateY.value = withSpring(0);
    } else {
      translateY.value = withSpring(SCREEN_HEIGHT);
    }
  }, [isVisible, translateY]);

  const handleClose = () => {
    translateY.value = withSpring(SCREEN_HEIGHT);
    runOnJS(onClose)();
  };

  const handleAddressSelect = (address: ILocation) => {
    onAddressSelect(address);
  };

  const handleAddNewAddress = () => {
    if (onAddNewAddress) {
      onAddNewAddress();
    }
  };

  const styles = StyleSheet.create({
    modalContainer: {
      flex: 1,
      justifyContent: "flex-end",
    },
    bottomsheet_container: {
      width: SCREEN_WIDTH,
      height: SCREEN_HEIGHT * 0.7,
      borderTopEndRadius: scale(25),
      borderTopStartRadius: scale(25),
    },
    overlay: {
      position: "absolute",
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      width: SCREEN_WIDTH,
      height: SCREEN_HEIGHT,
      backgroundColor: "black",
    },
    line: {
      width: scale(75),
      height: scale(4),
      backgroundColor: "#E9E9E9",
      borderRadius: scale(20),
      alignSelf: "center",
      marginVertical: scale(10),
    },
    container: {
      width: "100%",
      justifyContent: "center",
      alignItems: "center",
      paddingHorizontal: 0,
    },
    header: {
      alignItems: "center",
      paddingBottom: scale(15),
      paddingHorizontal: scale(20),
      width: "100%",
    },
    listContainer: {
      flex: 1,
      paddingHorizontal: scale(20),
    },
    buttonContainer: {
      backgroundColor: Colors[currentTheme ?? "dark"].background,
      paddingHorizontal: scale(20),
      paddingVertical: scale(20),
    },
    addButton: {
      width: "100%",
    },

    closeButton: {
      position: "absolute",
      top: scale(-50),
      right: scale(20),
      height: scale(40),
      width: scale(40),
      backgroundColor: "white",
      borderRadius: scale(20),
      justifyContent: "center",
      alignItems: "center",
      shadowColor: "#000",
      shadowOffset: {
        width: 0,
        height: 2,
      },
      shadowOpacity: 0.25,
      shadowRadius: 3.84,
      elevation: 5,
    },
  });

  const renderAddressItem = ({ item }: { item: ILocation }) => {
    return <AddressItem address={item} onPress={handleAddressSelect} />;
  };

  const renderEmptyAddresses = () => (
    <EmptyAddressList onAddNewAddress={handleAddNewAddress} />
  );

  // Show error component if there's an error

  return (
    <Modal
      visible={isVisible}
      transparent={true}
      animationType="none"
      statusBarTranslucent={true}
    >
      <View style={styles.modalContainer}>
        {/* Overlay */}
        <Animated.View style={[styles.overlay, overlayStyle]}>
          <Pressable
            style={StyleSheet.absoluteFillObject}
            onPress={handleClose}
          />
        </Animated.View>

        {/* Bottom Sheet */}
        <GestureDetector gesture={gesture}>
          <Animated.View
            style={[
              styles.bottomsheet_container,
              reanimatedBottomStyle,
              {
                backgroundColor: Colors[currentTheme ?? "dark"].background,
              },
            ]}
          >
            <View style={styles.line} />
            {/* Header */}
            <View style={styles.container}>
              <View style={[styles.header]}>
                <ThemedText type="bold" size={20}>
                  {t("addresses.title")}
                </ThemedText>
              </View>
            </View>
            {error ? (
              <ErrorComponent error={error?.message} onRetry={handleRetry} />
            ) : (
              <View style={styles.listContainer}>
                <InfiniteScrollList
                  data={addresses}
                  isLoading={isLoading}
                  isFetchingNextPage={isFetchingNextPage}
                  hasNextPage={hasNextPage}
                  fetchNextPage={fetchNextPage}
                  refreshing={refreshing}
                  onRefresh={handleRefresh}
                  estimatedItemSize={80}
                  renderItem={renderAddressItem}
                  keyExtractor={(item, index) =>
                    item?._id || `address-${index}`
                  }
                  skeletonComponent={<SkeletonAddressList />}
                  emptyComponent={renderEmptyAddresses()}
                  count={6}
                />
              </View>
            )}
            {/* Fixed Button at Bottom - Only show when there are addresses */}
            {addresses.length > 0 && !isLoading && (
              <View style={styles.buttonContainer}>
                <DefaultButton
                  title={t("addresses.actions.addNewAddress")}
                  onPress={handleAddNewAddress}
                  style={styles.addButton}
                >
                  <Ionicons
                    name="add"
                    size={scaleFont(20)}
                    color={
                      currentTheme === "dark"
                        ? Colors.dark.black
                        : Colors.light.white
                    }
                  />
                </DefaultButton>
              </View>
            )}

            <Pressable onPress={handleClose} style={styles.closeButton}>
              <Ionicons name="close" size={scaleFont(24)} color="black" />
            </Pressable>
          </Animated.View>
        </GestureDetector>
      </View>
    </Modal>
  );
}
