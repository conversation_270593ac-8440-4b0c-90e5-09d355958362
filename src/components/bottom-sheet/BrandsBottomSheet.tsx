import { scale, scaleFont } from "@/src/_helper/Scaler";
import EmptyBrandListUI from "@/src/components/ui/EmptyBrandListUI";
import { ThemedText } from "@/src/components/ui/ThemedText";
import { Colors } from "@/src/constants/Colors";
import { useLanguage } from "@/src/context/LanguageContext";
import { useTheme } from "@/src/context/ThemeContext";

import useGetBrands from "@/src/services/querys/vendor/useGetBrands";
import { BrandBottomSheetProps, IVendorBrand } from "@/src/types";
import { Ionicons, MaterialIcons } from "@expo/vector-icons";
import React, { useEffect } from "react";
import {
  Dimensions,
  Image,
  Keyboard,
  Pressable,
  StyleSheet,
  TouchableOpacity,
  View,
} from "react-native";
import { Gesture, GestureDetector } from "react-native-gesture-handler";
import Animated, {
  runOnJS,
  useAnimatedStyle,
  useSharedValue,
  withSpring,
} from "react-native-reanimated";
import SkeletonBrandList from "../loader/SkeletonBrandList";
import { InfiniteScrollList } from "../shared/InfiniteScrollList";

const { height: SCREEN_HEIGHT } = Dimensions.get("window");

export default function BrandBottomSheet({
  isVisible,
  onClose,
  onBrandSelect,
}: BrandBottomSheetProps) {
  const { currentTheme } = useTheme();
  const { t } = useLanguage();

  const {
    data,
    fetchNextPage,
    hasNextPage,
    isLoading,
    isFetchingNextPage,
    refetch,
    isRefetching,
  } = useGetBrands();
  const allBrands = data?.pages.flatMap((page) => page.data.items) ?? [];
  console.log(allBrands);
  const translateY = useSharedValue(SCREEN_HEIGHT);
  const context = useSharedValue({ y: 0 });

  // Create gesture only for the header/drag handle area
  const gesture = Gesture.Pan()
    .onStart(() => {
      context.value = { y: translateY.value };
    })
    .onUpdate((e) => {
      // Only allow downward swipes to close
      if (e.translationY > 0) {
        translateY.value = e.translationY + context.value.y;
        translateY.value = Math.min(translateY.value, SCREEN_HEIGHT);
      }
    })
    .onEnd((e) => {
      // Only close if swiped down significantly
      if (e.translationY > SCREEN_HEIGHT * 0.3) {
        translateY.value = withSpring(SCREEN_HEIGHT);
        runOnJS(onClose)();
      } else {
        translateY.value = withSpring(0);
      }
    });

  const reanimatedBottomStyle = useAnimatedStyle(() => {
    return {
      transform: [{ translateY: translateY.value }],
    };
  });

  const overlayStyle = useAnimatedStyle(() => {
    const opacity =
      isVisible && translateY.value < SCREEN_HEIGHT * 0.5 ? 0.4 : 0;
    return {
      opacity: opacity,
    };
  });

  useEffect(() => {
    if (isVisible) {
      Keyboard.dismiss();
      translateY.value = withSpring(0);
    } else {
      translateY.value = withSpring(SCREEN_HEIGHT);
    }
  }, [isVisible]);

  const handleClose = () => {
    translateY.value = withSpring(SCREEN_HEIGHT);
    setTimeout(onClose, 300); // Wait for animation to complete
  };

  const handleBrandSelect = (brandId: string, brandName: string) => {
    onBrandSelect(brandId, brandName);
    handleClose();
  };

  const styles = StyleSheet.create({
    bottomsheet_container: {
      width: "100%",
      height: SCREEN_HEIGHT * 0.5,
      position: "absolute",
      bottom: 0,
      zIndex: 999,
      borderTopEndRadius: scale(25),
      borderTopStartRadius: scale(25),
    },
    overlay: {
      position: "absolute",
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: Colors[currentTheme ?? "dark"].overlay,
      zIndex: 998,
    },
    dragArea: {
      paddingHorizontal: scale(15),
    },
    line: {
      width: scale(75),
      height: scale(4),
      backgroundColor: Colors[currentTheme ?? "dark"].separator,
      borderRadius: 20,
      alignSelf: "center",
      marginVertical: scale(10),
    },
    header: {
      alignItems: "center",
      paddingBottom: scale(15),
      borderBottomWidth: 1,
      borderBottomColor: Colors[currentTheme ?? "dark"].bottom_sheet_border,
      marginHorizontal: scale(5),
    },
    listContainer: {
      flex: 1,
      paddingHorizontal: scale(20),
      paddingTop: scale(10),
    },
    brandItem: {
      marginVertical: scale(6),
      borderRadius: scale(12),
      overflow: "hidden",
    },
    brandContent: {
      flexDirection: "row",
      alignItems: "center",
      padding: scale(16),
    },
    logoContainer: {
      width: scale(50),
      height: scale(50),
      borderRadius: scale(12),
      overflow: "hidden",
      marginRight: scale(16),
    },
    logoImage: {
      width: "100%",
      height: "100%",
      borderRadius: scale(12),
    },
    logoPlaceholder: {
      width: "100%",
      height: "100%",
      justifyContent: "center",
      alignItems: "center",
      borderRadius: scale(12),
    },
    brandInfo: {
      flex: 1,
      justifyContent: "center",
    },
    brandName: {
      marginBottom: scale(4),
    },
    productCount: {
      opacity: 0.8,
    },
    arrowContainer: {
      justifyContent: "center",
      alignItems: "center",
      width: scale(24),
      height: scale(24),
    },
    closeButton: {
      position: "absolute",
      top: scale(-50),
      right: scale(10),
      height: scale(40),
      width: scale(40),
      backgroundColor: "white",
      borderRadius: scale(20),
      justifyContent: "center",
      alignItems: "center",
    },
  });

  const renderBrandItem = ({ item }: { item: IVendorBrand }) => (
    <TouchableOpacity
      style={[
        styles.brandItem,
        {
          borderColor: Colors[currentTheme ?? "dark"].border,
        },
      ]}
      onPress={() => handleBrandSelect(item._id, item.name)}
      activeOpacity={0.7}
    >
      <View style={styles.brandContent}>
        {/* Brand Logo */}
        <View style={styles.logoContainer}>
          {item.logo ? (
            <Image
              source={{ uri: item.logo }}
              style={styles.logoImage}
              defaultSource={require("@/src/assets/images/icon.png")}
            />
          ) : (
            <View
              style={[
                styles.logoPlaceholder,
                { backgroundColor: Colors[currentTheme ?? "dark"].primary },
              ]}
            >
              <MaterialIcons
                name="business"
                size={24}
                color={Colors[currentTheme ?? "dark"].background}
              />
            </View>
          )}
        </View>

        {/* Brand Info */}
        <View style={styles.brandInfo}>
          <ThemedText type="semi-bold" size={16} style={styles.brandName}>
            {item.name}
          </ThemedText>
          <ThemedText
            type="regular"
            size={14}
            style={[
              styles.productCount,
              { color: Colors[currentTheme ?? "dark"].secondary },
            ]}
          >
            {item.numberOfProducts}{" "}
            {item.numberOfProducts === 1
              ? t("screens.headers.product")
              : t("screens.headers.products")}
          </ThemedText>
        </View>

        {/* Arrow Icon */}
        <View style={styles.arrowContainer}>
          <Ionicons
            name="chevron-forward"
            size={20}
            color={Colors[currentTheme ?? "dark"].secondary}
          />
        </View>
      </View>
    </TouchableOpacity>
  );

  if (!isVisible) return null;

  return (
    <>
      {/* Overlay */}
      <Animated.View
        style={[styles.overlay, overlayStyle]}
        pointerEvents={isVisible ? "auto" : "none"}
      >
        <Pressable
          style={StyleSheet.absoluteFillObject}
          onPress={handleClose}
        />
      </Animated.View>

      {/* Bottom Sheet */}
      <Animated.View
        style={[
          styles.bottomsheet_container,
          reanimatedBottomStyle,
          {
            backgroundColor: Colors[currentTheme ?? "dark"].background,
          },
        ]}
      >
        {/* Draggable Header Area */}
        <GestureDetector gesture={gesture}>
          <View style={styles.dragArea}>
            <View style={styles.line} />
            <View style={styles.header}>
              <ThemedText type="bold" size={20}>
                {t("bottomSheets.brands.title")}
              </ThemedText>
            </View>
          </View>
        </GestureDetector>

        {/* Brand List */}
        <View style={styles.listContainer}>
          <InfiniteScrollList
            data={allBrands}
            isLoading={isLoading}
            isFetchingNextPage={isFetchingNextPage}
            hasNextPage={hasNextPage}
            fetchNextPage={fetchNextPage}
            refreshing={isRefetching}
            onRefresh={refetch}
            estimatedItemSize={60}
            renderItem={renderBrandItem}
            skeletonComponent={<SkeletonBrandList />}
            emptyComponent={<EmptyBrandListUI />}
            contentContainerStyle={{ paddingBottom: scale(20) }}
            count={5}
          />
        </View>

        <Pressable onPress={handleClose} style={styles.closeButton}>
          <Ionicons name="close" size={scaleFont(24)} />
        </Pressable>
      </Animated.View>
    </>
  );
}
