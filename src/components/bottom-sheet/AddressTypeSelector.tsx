import { scale, scaleFont } from "@/src/_helper/Scaler";
import { Colors } from "@/src/constants/Colors";
import { useLanguage } from "@/src/context/LanguageContext";
import { useTheme } from "@/src/context/ThemeContext";
import useAddLocationMutation from "@/src/services/querys/client/useAddLocation";
import { AddressType, AddressTypeSelectorProps } from "@/src/types";
import { ILocation } from "@/src/types/location";
import { Ionicons } from "@expo/vector-icons";
import React, { useEffect, useState } from "react";
import {
  Dimensions,
  Modal,
  Pressable,
  StyleSheet,
  TouchableOpacity,
  View,
} from "react-native";
import { Gesture, GestureDetector } from "react-native-gesture-handler";
import Animated, {
  runOnJS,
  useAnimatedStyle,
  useSharedValue,
  withSpring,
} from "react-native-reanimated";
import DefaultButton from "../buttons/Default";
import { ThemedText } from "../ui/ThemedText";

const { height: SCREEN_HEIGHT, width: SCREEN_WIDTH } = Dimensions.get("window");

export default function AddressTypeSelector({
  isVisible,
  onClose,
  selectedLocation,
}: AddressTypeSelectorProps) {
  const { currentTheme } = useTheme();
  const { t } = useLanguage();

  // Debug logging
  console.log(
    "🏠 AddressTypeSelector received selectedLocation:",
    selectedLocation
  );

  const [selectedType, setSelectedType] = useState<ILocation["type"] | null>(
    null
  );

  const addLocationMutation = useAddLocationMutation();

  const translateY = useSharedValue(SCREEN_HEIGHT);
  const context = useSharedValue({ y: 0 });

  const addressTypes: AddressType[] = [
    {
      type: "home",
      icon: "home",
      title: t("addresses.types.home"),
      description: t("addressType.descriptions.home"),
    },
    {
      type: "workplace",
      icon: "briefcase",
      title: t("addresses.types.work"),
      description: t("addressType.descriptions.work"),
    },
    {
      type: "other",
      icon: "location",
      title: t("addresses.types.other"),
      description: t("addressType.descriptions.other"),
    },
  ];

  useEffect(() => {
    if (isVisible) {
      translateY.value = withSpring(0);
      setSelectedType(null);
    } else {
      translateY.value = withSpring(SCREEN_HEIGHT);
    }
  }, [isVisible, translateY]);

  const gesture = Gesture.Pan()
    .onStart(() => {
      context.value = { y: translateY.value };
    })
    .onUpdate((e) => {
      translateY.value = e.translationY + context.value.y;
      translateY.value = Math.max(translateY.value, -SCREEN_HEIGHT * 0.1);
      translateY.value = Math.min(translateY.value, 0);
    })
    .onEnd(() => {
      if (translateY.value > -SCREEN_HEIGHT * 0.1) {
        translateY.value = withSpring(SCREEN_HEIGHT);
        runOnJS(onClose)();
      } else {
        translateY.value = withSpring(0);
      }
    });

  const reanimatedBottomStyle = useAnimatedStyle(() => {
    return {
      transform: [{ translateY: translateY.value }],
    };
  });

  const overlayStyle = useAnimatedStyle(() => {
    return {
      opacity: isVisible ? withSpring(0.5) : withSpring(0),
      pointerEvents: isVisible ? "auto" : "none",
    };
  });

  const handleClose = () => {
    translateY.value = withSpring(SCREEN_HEIGHT);
    runOnJS(onClose)();
  };

  const handleTypeSelect = (type: ILocation["type"]) => {
    setSelectedType(type);
  };

  const handleConfirm = async () => {
    console.log(selectedType, selectedLocation);
    if (!selectedType || !selectedLocation) return;

    try {
      // Create the location object to save
      const newLocation: ILocation = {
        address: selectedLocation.address,
        latitude: selectedLocation.latitude,
        longitude: selectedLocation.longitude,
        city: selectedLocation.city,
        state: selectedLocation.state,
        country: selectedLocation.country,
        zipCode: selectedLocation.zipCode,
        type: selectedType,
      };

      // Call the mutation to add the location
      await addLocationMutation.mutateAsync(newLocation);

      // Close the modal
      handleClose();
    } catch (error) {
      console.error("Failed to add location:", error);
      // Error handling is already done in the mutation (shows toast)
    }
  };

  const styles = StyleSheet.create({
    modalContainer: {
      flex: 1,
      justifyContent: "flex-end",
    },
    overlay: {
      position: "absolute",
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: Colors[currentTheme ?? "dark"].overlay,
    },
    bottomsheet_container: {
      width: SCREEN_WIDTH,
      height: SCREEN_HEIGHT * 0.7,
      borderTopEndRadius: scale(25),
      borderTopStartRadius: scale(25),
      backgroundColor: Colors[currentTheme ?? "dark"].background,
    },
    line: {
      width: scale(75),
      height: scale(4),
      backgroundColor: Colors[currentTheme ?? "dark"].separator,
      borderRadius: scale(20),
      alignSelf: "center",
      marginVertical: scale(10),
    },
    container: {
      flex: 1,
      paddingHorizontal: scale(20),
    },
    header: {
      alignItems: "center",
      paddingBottom: scale(20),
    },

    addressTypesContainer: {
      flex: 1,
    },
    addressTypeItem: {
      flexDirection: "row",
      alignItems: "center",
      paddingVertical: scale(18),
      borderRadius: scale(12),
      marginBottom: scale(12),
      borderWidth: 2,
      borderColor: "transparent",
    },
    addressTypeItemSelected: {
      borderColor: Colors[currentTheme ?? "dark"].primary,
      paddingHorizontal: scale(10),
      backgroundColor: Colors[currentTheme ?? "dark"].primary + "10",
    },
    addressTypeIcon: {
      width: scale(50),
      height: scale(50),
      borderRadius: scale(25),
      justifyContent: "center",
      alignItems: "center",
      marginRight: scale(15),
    },
    addressTypeContent: {
      flex: 1,
    },
    addressTypeTitle: {
      marginBottom: scale(4),
    },
    addressTypeDescription: {
      lineHeight: scale(18),
    },
    checkIcon: {
      marginLeft: scale(10),
    },
    buttonContainer: {
      paddingTop: scale(20),
      paddingBottom: scale(20),
    },
    confirmButton: {
      width: "100%",
    },
    closeButton: {
      position: "absolute",
      top: scale(-50),
      right: scale(20),
      height: scale(40),
      width: scale(40),
      backgroundColor: "white",
      borderRadius: scale(20),
      justifyContent: "center",
      alignItems: "center",
      shadowColor: "#000",
      shadowOffset: {
        width: 0,
        height: 2,
      },
      shadowOpacity: 0.25,
      shadowRadius: 3.84,
      elevation: 5,
    },
  });

  const renderAddressType = (addressType: AddressType) => {
    const isSelected = selectedType === addressType.type;

    return (
      <TouchableOpacity
        key={addressType.type}
        style={[
          styles.addressTypeItem,
          {
            backgroundColor: isSelected
              ? Colors[currentTheme ?? "dark"].primary + "10"
              : Colors[currentTheme ?? "dark"].background,
          },
          isSelected && styles.addressTypeItemSelected,
        ]}
        onPress={() => handleTypeSelect(addressType.type)}
        disabled={addLocationMutation.isPending}
      >
        <View
          style={[
            styles.addressTypeIcon,
            {
              backgroundColor: isSelected
                ? Colors[currentTheme ?? "dark"].primary
                : Colors[currentTheme ?? "dark"].background,
            },
          ]}
        >
          <Ionicons
            name={addressType.icon as any}
            size={scaleFont(24)}
            color={
              isSelected
                ? Colors[currentTheme ?? "dark"].background
                : Colors[currentTheme ?? "dark"].primary
            }
          />
        </View>

        <View style={styles.addressTypeContent}>
          <ThemedText
            type="semi-bold"
            size={16}
            style={styles.addressTypeTitle}
          >
            {addressType.title}
          </ThemedText>
          <ThemedText
            size={14}
            style={[
              styles.addressTypeDescription,
              {
                color: Colors[currentTheme ?? "dark"].secondary,
              },
            ]}
          >
            {addressType.description}
          </ThemedText>
        </View>

        {isSelected && (
          <View style={styles.checkIcon}>
            <Ionicons
              name="checkmark-circle"
              size={scaleFont(24)}
              color={Colors[currentTheme ?? "dark"].primary}
            />
          </View>
        )}
      </TouchableOpacity>
    );
  };

  return (
    <Modal
      visible={isVisible}
      transparent={true}
      animationType="none"
      statusBarTranslucent={true}
    >
      <View style={styles.modalContainer}>
        <Animated.View style={[styles.overlay, overlayStyle]}>
          <Pressable
            style={StyleSheet.absoluteFillObject}
            onPress={handleClose}
          />
        </Animated.View>

        <GestureDetector gesture={gesture}>
          <Animated.View
            style={[styles.bottomsheet_container, reanimatedBottomStyle]}
          >
            <View style={styles.line} />

            <View style={styles.container}>
              <View style={styles.header}>
                <ThemedText type="bold" size={20}>
                  {t("addressType.title")}
                </ThemedText>
              </View>

              <View style={styles.addressTypesContainer}>
                {addressTypes.map(renderAddressType)}
              </View>

              {/* Confirm Button */}
              <View style={styles.buttonContainer}>
                <DefaultButton
                  title={t("addressType.saveAddress")}
                  onPress={handleConfirm}
                  disabled={!selectedType || addLocationMutation.isPending}
                  isSubmitting={addLocationMutation.isPending}
                  style={styles.confirmButton}
                />
              </View>
            </View>

            <Pressable onPress={handleClose} style={styles.closeButton}>
              <Ionicons name="close" size={scaleFont(24)} color="black" />
            </Pressable>
          </Animated.View>
        </GestureDetector>
      </View>
    </Modal>
  );
}
