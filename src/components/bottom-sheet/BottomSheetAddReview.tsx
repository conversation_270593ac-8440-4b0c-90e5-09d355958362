import { scale, scaleFont } from "@/src/_helper/Scaler";
import { ReviewSchema } from "@/src/_helper/validator/ClientValidator";
import { Colors } from "@/src/constants/Colors";
import { useLanguage } from "@/src/context/LanguageContext";
import { useTheme } from "@/src/context/ThemeContext";
import useAddRating from "@/src/services/querys/client/useAddRating";
import { BottomSheetAddReviewProps } from "@/src/types";
import { Ionicons } from "@expo/vector-icons";
import { Formik } from "formik";
import React, { useEffect, useMemo, useState } from "react";
import {
  Dimensions,
  Keyboard,
  Modal,
  Pressable,
  StyleSheet,
  View,
} from "react-native";
import { Gesture, GestureDetector } from "react-native-gesture-handler";
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withSpring,
} from "react-native-reanimated";
import DefaultButton from "../buttons/Default";
import { MainContainerForScreensWithInput } from "../containers/MainContainerForScreensWithInput";
import TextInput from "../inputs/TextInput";
import StarRating from "../ui/StarRating";
import { ThemedText } from "../ui/ThemedText";
import { ThemedView } from "../ui/ThemedView";

const { height: SCREEN_HEIGHT } = Dimensions.get("window");

export default function BottomSheetAddReview({
  isVisible,
  onClose,
  id,
  productId,
}: BottomSheetAddReviewProps) {
  const translateY = useSharedValue(SCREEN_HEIGHT);
  const context = useSharedValue({ y: 0 });
  const { t } = useLanguage();
  const [keyboardHeight, setKeyboardHeight] = useState(0);
  const [isKeyboardVisible, setIsKeyboardVisible] = useState(false);
  const mutation = useAddRating();
  const gesture = Gesture.Pan()
    .onStart(() => {
      context.value = { y: translateY.value };
    })
    .onUpdate((e) => {
      // Only allow pan gesture when keyboard is not visible
      if (!isKeyboardVisible) {
        translateY.value = e.translationY + context.value.y;
        translateY.value = Math.max(translateY.value, -SCREEN_HEIGHT / 3);
        translateY.value = Math.min(translateY.value, 0);
      }
    })
    .onEnd(() => {
      // Always snap back to the bottom position when keyboard is not visible
      if (!isKeyboardVisible) {
        translateY.value = withSpring(0);
      }
    });

  const reanimatedBottomStyle = useAnimatedStyle(() => {
    return {
      transform: [{ translateY: translateY.value }],
    };
  });

  const overlayStyle = useAnimatedStyle(() => {
    return {
      opacity: isVisible ? 0.4 : 0,
    };
  });

  const initialValues = useMemo(
    () => ({
      rating: 0, // Changed from string to number
      comment: "",
    }),
    []
  );

  const handleReview = async (
    values: typeof initialValues,
    { setSubmitting }: any
  ) => {
    try {
      console.log("Rating:", values.rating); // You can see the rating value here
      console.log("Comment:", values.comment);
      await mutation.mutateAsync({
        id: id,
        rating: values.rating,
        comment: values.comment,
        productId: productId,
      });
      onClose(); // Close the bottom sheet after successful submission
    } finally {
      setSubmitting(false);
    }
  };

  useEffect(() => {
    const keyboardDidShowListener = Keyboard.addListener(
      "keyboardDidShow",
      (event) => {
        setKeyboardHeight(event.endCoordinates.height);
        setIsKeyboardVisible(true);
      }
    );

    const keyboardDidHideListener = Keyboard.addListener(
      "keyboardDidHide",
      () => {
        setKeyboardHeight(0);
        setIsKeyboardVisible(false);
      }
    );

    return () => {
      keyboardDidShowListener?.remove();
      keyboardDidHideListener?.remove();
    };
  }, []);

  useEffect(() => {
    if (isVisible) {
      translateY.value = withSpring(0);
    } else {
      translateY.value = withSpring(SCREEN_HEIGHT);
      // Dismiss keyboard when bottom sheet closes
      Keyboard.dismiss();
    }
  }, [isVisible, translateY]);

  const { currentTheme } = useTheme();

  const handleClose = () => {
    translateY.value = withSpring(SCREEN_HEIGHT, {
      damping: 50,
    });
    setTimeout(onClose, 300);
  };

  return (
    <Modal
      visible={isVisible}
      transparent={true}
      animationType="none"
      statusBarTranslucent={true}
    >
      <View style={styles.modalContainer}>
        {/* Overlay */}
        <Animated.View style={[styles.overlay, overlayStyle]}>
          <Pressable
            style={StyleSheet.absoluteFillObject}
            onPress={handleClose}
          />
        </Animated.View>

        {/* Bottom Sheet */}
        <GestureDetector gesture={gesture}>
          <Animated.View
            style={[
              styles.bottomsheet_container,
              reanimatedBottomStyle,
              {
                backgroundColor: Colors[currentTheme ?? "dark"].background,
                bottom: keyboardHeight,
              },
            ]}
          >
            <View style={styles.line} />
            <MainContainerForScreensWithInput
              disableScroll={true} // Always disable scroll to prevent interference
            >
              <View style={styles.content}>
                <ThemedText
                  type="bold"
                  size={24}
                  style={{ marginBottom: scale(20) }}
                >
                  {t("bottomSheets.addReview.title")}
                </ThemedText>

                <Formik
                  initialValues={initialValues}
                  onSubmit={handleReview}
                  validationSchema={ReviewSchema}
                >
                  {({
                    handleChange,
                    handleSubmit,
                    values,
                    errors,
                    touched,
                    isValid,
                    dirty,
                    setFieldValue,
                  }) => (
                    <ThemedView style={styles.form}>
                      <View style={styles.inputsSection}>
                        {/* Rating Section */}
                        <View style={styles.ratingSection}>
                          <ThemedText
                            type="bold"
                            size={16}
                            style={{ marginBottom: scale(8) }}
                          >
                            {t("bottomSheets.addReview.rating")}
                          </ThemedText>
                          <StarRating
                            rating={values.rating}
                            onRatingChange={(rating: number) =>
                              setFieldValue("rating", rating)
                            }
                            error={
                              touched.rating && errors.rating
                                ? String(errors.rating)
                                : undefined
                            }
                          />
                        </View>

                        {/* Comment Section */}
                        <TextInput
                          multiline
                          onChangeText={handleChange("comment")}
                          value={values.comment}
                          placeholder={t("bottomSheets.addReview.comment")}
                          error={touched.comment && errors.comment}
                          returnKeyType="default"
                        />
                      </View>
                      <View style={styles.actionsSection}>
                        <DefaultButton
                          title={t("bottomSheets.addReview.submit")}
                          onPress={handleSubmit}
                          isSubmitting={mutation.isPending}
                          disabled={!isValid || !dirty || mutation.isPending}
                        />
                      </View>
                    </ThemedView>
                  )}
                </Formik>
              </View>
            </MainContainerForScreensWithInput>
            <Pressable
              onPress={handleClose}
              style={[
                styles.closeButton,
                {
                  top:
                    keyboardHeight > 0
                      ? scale(-50) - keyboardHeight
                      : scale(-50),
                },
              ]}
            >
              <Ionicons name="close" size={scaleFont(24)} />
            </Pressable>
          </Animated.View>
        </GestureDetector>
      </View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  modalContainer: {
    flex: 1,
  },
  bottomsheet_container: {
    width: "100%",
    height: SCREEN_HEIGHT * 0.5,
    position: "absolute",
    bottom: 0,
    zIndex: 999,
    borderTopEndRadius: scale(25),
    borderTopStartRadius: scale(25),
    paddingHorizontal: scale(5),
  },
  overlay: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: "black",
  },
  line: {
    width: scale(75),
    height: scale(4),
    backgroundColor: "#E9E9E9",
    borderRadius: 20,
    alignSelf: "center",
    marginVertical: scale(10),
  },
  container: {
    width: "100%",
    justifyContent: "center",
    alignItems: "center",
  },
  content: {
    width: "100%",
    justifyContent: "center",
    alignItems: "center",
  },
  closeButton: {
    position: "absolute",
    top: scale(-50),
    right: scale(10),
    height: scale(40),
    width: scale(40),
    backgroundColor: "white",
    borderRadius: scale(20),
    justifyContent: "center",
    alignItems: "center",
  },
  form: {
    width: "100%",
    flexDirection: "column",
    justifyContent: "space-between",
    flexGrow: 1,
  },
  inputsSection: {
    gap: scale(15),
  },
  ratingSection: {
    width: "100%",
  },
  actionsSection: {
    justifyContent: "center",
    alignItems: "center",
    gap: scale(10),
  },
});
