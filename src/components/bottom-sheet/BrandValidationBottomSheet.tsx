import { scale, scaleFont } from "@/src/_helper/Scaler";
import { Colors } from "@/src/constants/Colors";
import { useLanguage } from "@/src/context/LanguageContext";
import { useTheme } from "@/src/context/ThemeContext";
import { Ionicons, MaterialIcons } from "@expo/vector-icons";
import React, { useEffect } from "react";
import {
  Dimensions,
  Modal,
  Pressable,
  ScrollView,
  StyleSheet,
  View,
} from "react-native";
import { Gesture, GestureDetector } from "react-native-gesture-handler";
import Animated, {
  runOnJS,
  useAnimatedStyle,
  useSharedValue,
  withSpring,
} from "react-native-reanimated";
import DefaultButton from "../buttons/Default";
import { ThemedText } from "../ui/ThemedText";

const { height: SCREEN_HEIGHT } = Dimensions.get("window");

interface BrandValidationBottomSheetProps {
  isVisible: boolean;
  onClose: () => void;
  onClearCartAndAdd: () => void;
  currentBrandName?: string;
  newBrandName?: string;
}

export default function BrandValidationBottomSheet({
  isVisible,
  onClose,
  onClearCartAndAdd,
  currentBrandName,
  newBrandName,
}: BrandValidationBottomSheetProps) {
  const { currentTheme } = useTheme();
  const { t } = useLanguage();
  const colors = Colors[currentTheme ?? "dark"];

  const translateY = useSharedValue(SCREEN_HEIGHT);
  const context = useSharedValue({ y: 0 });

  const gesture = Gesture.Pan()
    .onStart(() => {
      context.value = { y: translateY.value };
    })
    .onUpdate((event) => {
      translateY.value = event.translationY + context.value.y;
      translateY.value = Math.max(translateY.value, 0);
    })
    .onEnd((event) => {
      if (event.translationY > 50) {
        translateY.value = withSpring(SCREEN_HEIGHT, {
          damping: 50,
        });
        runOnJS(onClose)();
      } else {
        translateY.value = withSpring(0, {
          damping: 50,
        });
      }
    });

  const reanimatedBottomStyle = useAnimatedStyle(() => {
    return {
      transform: [{ translateY: translateY.value }],
    };
  });

  const overlayStyle = useAnimatedStyle(() => {
    const opacity = isVisible ? 1 - translateY.value / SCREEN_HEIGHT : 0;
    return {
      opacity: opacity * 0.4,
    };
  });

  useEffect(() => {
    if (isVisible) {
      translateY.value = withSpring(0, {
        damping: 50,
      });
    } else {
      translateY.value = withSpring(SCREEN_HEIGHT, {
        damping: 50,
      });
    }
  }, [isVisible]);

  const handleClose = () => {
    translateY.value = withSpring(SCREEN_HEIGHT, {
      damping: 50,
    });
    setTimeout(onClose, 300);
  };

  if (!isVisible) return null;

  return (
    <Modal
      visible={isVisible}
      transparent={true}
      animationType="none"
      statusBarTranslucent={true}
    >
      <View style={styles.modalContainer}>
        {/* Overlay */}
        <Animated.View style={[styles.overlay, overlayStyle]}>
          <Pressable
            style={StyleSheet.absoluteFillObject}
            onPress={handleClose}
          />
        </Animated.View>

        {/* Bottom Sheet */}
        <GestureDetector gesture={gesture}>
          <Animated.View
            style={[
              styles.bottomsheet_container,
              reanimatedBottomStyle,
              {
                backgroundColor: Colors[currentTheme ?? "dark"].background,
              },
            ]}
          >
            <View style={styles.line} />

            {/* Header */}
            <View style={styles.header}>
              <View style={styles.iconContainer}>
                <MaterialIcons
                  name="warning"
                  size={scaleFont(32)}
                  color={Colors[currentTheme ?? "dark"].warning || "#FF9500"}
                />
              </View>
              <ThemedText type="bold" size={22} style={styles.title}>
                {t("cart.brandValidation.title")}
              </ThemedText>
            </View>

            {/* Content */}
            <ScrollView style={styles.content}>
              <ThemedText
                size={16}
                style={[
                  styles.description,
                  { color: Colors[currentTheme ?? "dark"].secondary },
                ]}
              >
                {t("cart.brandValidation.description")}
              </ThemedText>

              {currentBrandName && newBrandName && (
                <View
                  style={[
                    styles.brandInfo,
                    { backgroundColor: colors.brand_info_background },
                  ]}
                >
                  <View style={styles.brandRow}>
                    <ThemedText type="semi-bold" size={14}>
                      {t("cart.brandValidation.currentBrand")}:
                    </ThemedText>
                    <ThemedText size={14} style={styles.brandName}>
                      {currentBrandName}
                    </ThemedText>
                  </View>
                  <View style={styles.brandRow}>
                    <ThemedText type="semi-bold" size={14}>
                      {t("cart.brandValidation.newBrand")}:
                    </ThemedText>
                    <ThemedText size={14} style={styles.brandName}>
                      {newBrandName}
                    </ThemedText>
                  </View>
                </View>
              )}

              <ThemedText
                size={14}
                style={[
                  styles.suggestion,
                  { color: Colors[currentTheme ?? "dark"].secondary },
                ]}
              >
                {t("cart.brandValidation.suggestion")}
              </ThemedText>

              {/* Actions */}
              <View style={styles.actions}>
                <DefaultButton
                  title={t("cart.brandValidation.clearAndAdd")}
                  onPress={onClearCartAndAdd}
                  color={Colors[currentTheme ?? "dark"].warning || "#FF9500"}
                  style={styles.actionButton}
                />

                <DefaultButton
                  title={t("cart.brandValidation.keepShopping")}
                  onPress={handleClose}
                  color={Colors[currentTheme ?? "dark"].secondary}
                  style={styles.actionButton}
                />
              </View>
            </ScrollView>

            <Pressable onPress={handleClose} style={styles.closeButton}>
              <Ionicons name="close" size={scaleFont(24)} color="black" />
            </Pressable>
          </Animated.View>
        </GestureDetector>
      </View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  modalContainer: {
    flex: 1,
  },
  overlay: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: "black",
  },
  bottomsheet_container: {
    width: "100%",
    height: SCREEN_HEIGHT * 0.65,
    position: "absolute",
    bottom: 0,
    zIndex: 999,
    borderTopEndRadius: scale(25),
    borderTopStartRadius: scale(25),
    paddingHorizontal: scale(20),
    paddingBottom: scale(20),
  },
  line: {
    width: scale(75),
    height: scale(4),
    backgroundColor: "#E9E9E9",
    borderRadius: 20,
    alignSelf: "center",
    marginVertical: scale(10),
  },
  header: {
    alignItems: "center",
    marginBottom: scale(5),
  },
  iconContainer: {
    marginBottom: scale(10),
  },
  title: {
    textAlign: "center",
  },
  content: {
    flex: 1,
  },
  description: {
    textAlign: "center",
    lineHeight: scale(22),
    marginBottom: scale(20),
  },
  brandInfo: {
    borderRadius: scale(12),
    padding: scale(15),
    marginBottom: scale(20),
  },
  brandRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: scale(8),
  },
  brandName: {
    flex: 1,
    textAlign: "right",
  },
  suggestion: {
    textAlign: "center",
    lineHeight: scale(20),
    fontStyle: "italic",
  },
  actions: {
    marginTop: scale(20),
  },
  actionButton: {
    borderRadius: scale(12),
  },
  closeButton: {
    position: "absolute",
    top: scale(-50),
    right: scale(20),
    height: scale(40),
    width: scale(40),
    backgroundColor: "white",
    borderRadius: scale(20),
    justifyContent: "center",
    alignItems: "center",
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
});
