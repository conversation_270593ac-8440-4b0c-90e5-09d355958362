import { scale, scaleFont } from "@/src/_helper/Scaler";
import { Colors } from "@/src/constants/Colors";
import { useLanguage } from "@/src/context/LanguageContext";
import { useTheme } from "@/src/context/ThemeContext";
import { useLocation } from "@/src/hooks/useLocation";
import { Ionicons } from "@expo/vector-icons";
import React, { useEffect, useState } from "react";
import {
  ActivityIndicator,
  Dimensions,
  Modal,
  Pressable,
  StyleSheet,
  View,
} from "react-native";
import { Gesture, GestureDetector } from "react-native-gesture-handler";
import MapView, { Marker } from "react-native-maps";
import Animated, {
  runOnJS,
  useAnimatedStyle,
  useSharedValue,
  withSpring,
} from "react-native-reanimated";
import DefaultButton from "../buttons/Default";
import Loader from "../loader/Loader";
import { ThemedText } from "../ui/ThemedText";

import { MapLocationPickerProps } from "@/src/types";

const { height: SCREEN_HEIGHT, width: SCREEN_WIDTH } = Dimensions.get("window");

export default function MapLocationPicker({
  isVisible,
  onClose,
  onLocationSelect,
}: MapLocationPickerProps) {
  const { currentTheme } = useTheme();
  const { t } = useLanguage();
  const [isConfirming, setIsConfirming] = useState(false);

  const {
    selectedLocation,
    mapRegion,
    isLoading,
    isInitializing,
    hasLocationPermission,
    getCurrentLocation,
    selectLocationFromMap,
    clearLocation,
  } = useLocation();

  const translateY = useSharedValue(SCREEN_HEIGHT);

  useEffect(() => {
    if (isVisible) {
      translateY.value = withSpring(0);
    } else {
      translateY.value = withSpring(SCREEN_HEIGHT);
    }
  }, [isVisible, translateY]);

  const gesture = Gesture.Pan()
    .onUpdate((e) => {
      if (e.translationY > 0) {
        translateY.value = e.translationY;
      }
    })
    .onEnd(() => {
      if (translateY.value > SCREEN_HEIGHT * 0.3) {
        translateY.value = withSpring(SCREEN_HEIGHT);
        runOnJS(handleClose)();
      } else {
        translateY.value = withSpring(0);
      }
    });

  const reanimatedStyle = useAnimatedStyle(() => {
    return {
      transform: [{ translateY: translateY.value }],
    };
  });

  const overlayStyle = useAnimatedStyle(() => {
    return {
      opacity: isVisible ? withSpring(0.5) : withSpring(0),
      pointerEvents: isVisible ? "auto" : "none",
    };
  });

  const handleClose = () => {
    clearLocation();
    setIsConfirming(false);
    onClose();
  };

  const handleMapPress = (event: any) => {
    const { latitude, longitude } = event.nativeEvent.coordinate;
    selectLocationFromMap(latitude, longitude);
  };

  const handleCurrentLocation = async () => {
    await getCurrentLocation();
  };

  const handleConfirmLocation = () => {
    if (!selectedLocation) {
      return;
    }

    setIsConfirming(true);

    // Simulate a brief loading state for better UX
    onLocationSelect({
      address: selectedLocation.address,
      latitude: selectedLocation.latitude || 0,
      longitude: selectedLocation.longitude || 0,
      city: selectedLocation.city,
      state: selectedLocation.state,
      country: selectedLocation.country,
      zipCode: selectedLocation.zipCode,
    });
    console.log(selectedLocation);
    setIsConfirming(false);
    handleClose();
  };

  const styles = StyleSheet.create({
    modalContainer: {
      flex: 1,
      justifyContent: "flex-end",
    },
    overlay: {
      position: "absolute",
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: "black",
    },
    container: {
      width: SCREEN_WIDTH,
      height: SCREEN_HEIGHT * 0.7,
      backgroundColor: Colors[currentTheme ?? "dark"].background,
      borderTopLeftRadius: scale(25),
      borderTopRightRadius: scale(25),
    },
    header: {
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "space-between",
      paddingHorizontal: scale(20),
      paddingTop: scale(50),
      paddingBottom: scale(15),
      backgroundColor: Colors[currentTheme ?? "dark"].background,
      borderBottomWidth: 1,
      borderBottomColor: Colors[currentTheme ?? "dark"].border,
    },

    closeButton: {
      position: "absolute",
      top: scale(-50),
      right: scale(20),
      height: scale(40),
      width: scale(40),
      backgroundColor: "white",
      borderRadius: scale(20),
      justifyContent: "center",
      alignItems: "center",
      shadowColor: "#000",
      shadowOffset: {
        width: 0,
        height: 2,
      },
      shadowOpacity: 0.25,
      shadowRadius: 3.84,
      elevation: 5,
      zIndex: 10, // Ensure it's above other elements
    },

    mapContainer: {
      flex: 1,
      position: "relative",
      borderTopLeftRadius: scale(25),
      borderTopRightRadius: scale(25),
      overflow: "hidden", // This ensures the map respects the border radius
    },
    map: {
      flex: 1,
    },
    centerMarker: {
      position: "absolute",
      top: "50%",
      left: "50%",
      marginLeft: scale(-12),
      marginTop: scale(-24),
      zIndex: 1,
    },
    bottomControls: {
      position: "absolute",
      bottom: 0,
      left: 0,
      right: 0,
      backgroundColor: Colors[currentTheme ?? "dark"].background,
      paddingHorizontal: scale(20),
      paddingVertical: scale(20),
      borderTopLeftRadius: scale(20),
      borderTopRightRadius: scale(20),
      shadowColor: Colors[currentTheme ?? "dark"].shadow_color,
      shadowOffset: {
        width: 0,
        height: -2,
      },
      shadowOpacity: 0.1,
      shadowRadius: 3.84,
      elevation: 5,
    },
    locationButton: {
      position: "absolute",
      top: scale(120),
      right: scale(20),
      width: scale(50),
      height: scale(50),
      borderRadius: scale(25),
      backgroundColor: Colors[currentTheme ?? "dark"].background,
      justifyContent: "center",
      alignItems: "center",
      shadowColor: Colors[currentTheme ?? "dark"].shadow_color,
      shadowOffset: {
        width: 0,
        height: 2,
      },
      shadowOpacity: 0.25,
      shadowRadius: 3.84,
      elevation: 5,
      zIndex: 1,
    },
    selectedAddressContainer: {
      marginBottom: scale(15),
    },
    selectedAddressText: {
      marginBottom: scale(5),
    },
    confirmButton: {
      width: "100%",
    },
    loaderOverlay: {
      position: "absolute",
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: Colors[currentTheme ?? "dark"].background,
      justifyContent: "center",
      alignItems: "center",
      zIndex: 10,
    },
  });

  return (
    <Modal
      visible={isVisible}
      transparent={true}
      animationType="none"
      statusBarTranslucent={true}
    >
      <View style={styles.modalContainer}>
        <Animated.View style={[styles.overlay, overlayStyle]}>
          <Pressable
            style={StyleSheet.absoluteFillObject}
            onPress={handleClose}
          />
        </Animated.View>

        <GestureDetector gesture={gesture}>
          <Animated.View style={[styles.container, reanimatedStyle]}>
            {/* Header */}
            <Pressable onPress={handleClose} style={styles.closeButton}>
              <Ionicons
                name="close"
                size={scaleFont(24)}
                color={Colors[currentTheme ?? "dark"].black}
              />
            </Pressable>

            <View style={styles.mapContainer}>
              <MapView
                style={styles.map}
                region={mapRegion}
                onPress={handleMapPress}
                showsUserLocation={hasLocationPermission}
                showsMyLocationButton={false}
                showsCompass={false}
                toolbarEnabled={false}
              >
                {selectedLocation && (
                  <Marker
                    coordinate={{
                      latitude: selectedLocation.latitude || 0,
                      longitude: selectedLocation.longitude || 0,
                    }}
                    title={t("map.selectedLocation")}
                    description={selectedLocation.address}
                  />
                )}
              </MapView>

              {/* Map Loading Overlay */}
              {isInitializing && (
                <View style={styles.loaderOverlay}>
                  <Loader
                    color={Colors[currentTheme ?? "dark"].primary}
                    size={60}
                  />
                </View>
              )}

              {/* Current Location Button */}
              <Pressable
                style={styles.locationButton}
                onPress={handleCurrentLocation}
              >
                {isLoading ? (
                  <ActivityIndicator
                    size="small"
                    color={Colors[currentTheme ?? "dark"].primary}
                  />
                ) : (
                  <Ionicons
                    name="locate"
                    size={scaleFont(24)}
                    color={Colors[currentTheme ?? "dark"].primary}
                  />
                )}
              </Pressable>

              {/* Bottom Controls */}
              <View style={styles.bottomControls}>
                {selectedLocation && (
                  <View style={styles.selectedAddressContainer}>
                    <ThemedText
                      type="semi-bold"
                      size={16}
                      style={styles.selectedAddressText}
                    >
                      {t("map.selectedAddress")}
                    </ThemedText>
                    <ThemedText
                      size={14}
                      style={{
                        color: Colors[currentTheme ?? "dark"].secondary,
                      }}
                    >
                      {selectedLocation.address}, {selectedLocation.city}
                    </ThemedText>
                  </View>
                )}

                <DefaultButton
                  title={t("map.confirmLocation")}
                  onPress={handleConfirmLocation}
                  disabled={!selectedLocation || isConfirming}
                  isSubmitting={isConfirming}
                  style={styles.confirmButton}
                />
              </View>
            </View>
          </Animated.View>
        </GestureDetector>
      </View>
    </Modal>
  );
}
