import { scale, scaleFont } from "@/src/_helper/Scaler";
import { Colors } from "@/src/constants/Colors";
import { useLanguage } from "@/src/context/LanguageContext";
import { useTheme } from "@/src/context/ThemeContext";
import { useLocation } from "@/src/hooks/useLocation";
import useUpdateLocationMutation from "@/src/services/querys/client/useUpdateLocation";
import { ILocation } from "@/src/types/location";
import { Ionicons } from "@expo/vector-icons";
import React, { useEffect, useState } from "react";
import {
  Dimensions,
  Modal,
  Pressable,
  StyleSheet,
  TouchableOpacity,
  View,
} from "react-native";
import { Gesture, GestureDetector } from "react-native-gesture-handler";
import MapView, { Marker } from "react-native-maps";
import Animated, {
  runOnJS,
  useAnimatedStyle,
  useSharedValue,
  withSpring,
} from "react-native-reanimated";
import DefaultButton from "../buttons/Default";
import Loader from "../loader/Loader";
import { ThemedText } from "../ui/ThemedText";

const { height: SCREEN_HEIGHT } = Dimensions.get("window");

import { EditLocationBottomSheetProps } from "@/src/types";

const locationTypes = [
  { key: "home", icon: "home" },
  { key: "workplace", icon: "briefcase" },
  { key: "other", icon: "location" },
] as const;

export default function EditLocationBottomSheet({
  isVisible,
  onClose,
  location,
  onLocationUpdate,
}: EditLocationBottomSheetProps) {
  const { currentTheme } = useTheme();
  const { t } = useLanguage();
  const colors = Colors[currentTheme ?? "dark"];
  const updateLocationMutation = useUpdateLocationMutation();

  const [selectedType, setSelectedType] = useState<
    "workplace" | "home" | "other" | "brand"
  >("home");
  const [currentLocation, setCurrentLocation] = useState<ILocation | null>(
    null
  );

  const {
    selectedLocation,
    setSelectedLocation,
    mapRegion,
    isInitializing,
    hasLocationPermission,
    selectLocationFromMap,
  } = useLocation();

  const translateY = useSharedValue(SCREEN_HEIGHT);
  const context = useSharedValue({ y: 0 });

  useEffect(() => {
    if (isVisible) {
      translateY.value = withSpring(0);
      if (location) {
        setSelectedType(location.type || "home");
        setCurrentLocation(location);
        // Set the location data for the map
        setSelectedLocation({
          address: location.address,
          latitude: location.latitude,
          longitude: location.longitude,
          city: location.city,
          state: location.state,
          country: location.country,
          zipCode: location.zipCode,
        });
      }
    } else {
      translateY.value = withSpring(SCREEN_HEIGHT);
    }
  }, [isVisible, translateY, location, setSelectedLocation]);

  const gesture = Gesture.Pan()
    .onStart(() => {
      context.value = { y: translateY.value };
    })
    .onUpdate((event) => {
      translateY.value = Math.max(0, context.value.y + event.translationY);
    })
    .onEnd((event) => {
      if (event.translationY > 100 || event.velocityY > 500) {
        translateY.value = withSpring(SCREEN_HEIGHT, {}, () => {
          runOnJS(onClose)();
        });
      } else {
        translateY.value = withSpring(0);
      }
    });

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ translateY: translateY.value }],
  }));

  const overlayStyle = useAnimatedStyle(() => ({
    opacity: 1 - translateY.value / SCREEN_HEIGHT,
  }));

  const handleClose = () => {
    translateY.value = withSpring(SCREEN_HEIGHT, {}, () => {
      runOnJS(onClose)();
    });
  };

  const handleMapPress = (event: any) => {
    const { coordinate } = event.nativeEvent;
    selectLocationFromMap(coordinate.latitude, coordinate.longitude);
  };

  const handleSave = async () => {
    if (!location || !location._id) return;

    try {
      // Use the selected location from map if available, otherwise use current location
      const locationToUpdate = selectedLocation || currentLocation;
      if (!locationToUpdate) return;

      const updatedLocation: ILocation = {
        address: locationToUpdate.address,
        latitude: locationToUpdate.latitude || 0,
        longitude: locationToUpdate.longitude || 0,
        city: locationToUpdate.city,
        state: locationToUpdate.state,
        country: locationToUpdate.country,
        zipCode: locationToUpdate.zipCode,
        type: selectedType,
      };

      await updateLocationMutation.mutateAsync({
        id: location._id,
        location: updatedLocation,
      });

      onLocationUpdate(updatedLocation);
      handleClose();
    } catch (error) {
      console.error("Failed to update location:", error);
    }
  };

  if (!location) return null;

  return (
    <Modal
      visible={isVisible}
      transparent={true}
      animationType="none"
      statusBarTranslucent={true}
    >
      <View style={styles.modalContainer}>
        <Animated.View
          style={[
            styles.overlay,
            overlayStyle,
            { backgroundColor: colors.modal_overlay },
          ]}
        >
          <Pressable
            style={StyleSheet.absoluteFillObject}
            onPress={handleClose}
          />
        </Animated.View>

        <GestureDetector gesture={gesture}>
          <Animated.View
            style={[
              styles.bottomsheet_container,
              animatedStyle,
              { backgroundColor: colors.background },
            ]}
          >
            <View style={styles.line} />

            {/* Header */}
            <View style={styles.header}>
              <ThemedText type="bold" size={20}>
                {t("addresses.editLocation")}
              </ThemedText>
            </View>

            {/* Map Section */}
            <View style={styles.mapSection}>
              <ThemedText
                type="semi-bold"
                size={16}
                style={styles.sectionTitle}
              >
                {t("map.updateLocation")}
              </ThemedText>
              <View style={styles.mapContainer}>
                <MapView
                  style={styles.map}
                  region={mapRegion}
                  onPress={handleMapPress}
                  showsUserLocation={hasLocationPermission}
                  showsMyLocationButton={false}
                  showsCompass={false}
                  toolbarEnabled={false}
                >
                  {(selectedLocation || currentLocation) && (
                    <Marker
                      coordinate={{
                        latitude:
                          (selectedLocation || currentLocation)?.latitude || 0,
                        longitude:
                          (selectedLocation || currentLocation)?.longitude || 0,
                      }}
                      title={t("map.selectedLocation")}
                      description={
                        (selectedLocation || currentLocation)!.address
                      }
                    />
                  )}
                </MapView>

                {/* Map Loading Overlay */}
                {isInitializing && (
                  <View style={styles.loaderOverlay}>
                    <Loader
                      color={Colors[currentTheme ?? "dark"].primary}
                      size={60}
                    />
                  </View>
                )}
              </View>

              {/* Current Location Info */}
              <View style={styles.locationInfo}>
                <ThemedText
                  type="semi-bold"
                  size={14}
                  style={styles.addressText}
                >
                  {(selectedLocation || currentLocation)?.address}
                </ThemedText>
                <ThemedText size={12} style={styles.cityText}>
                  {(selectedLocation || currentLocation)?.city},{" "}
                  {(selectedLocation || currentLocation)?.state},{" "}
                  {(selectedLocation || currentLocation)?.country}
                </ThemedText>
              </View>
            </View>

            {/* Type Selection */}
            <View style={styles.typeSection}>
              <ThemedText
                type="semi-bold"
                size={16}
                style={styles.sectionTitle}
              >
                {t("addresses.selectType")}
              </ThemedText>
              <View style={styles.typeOptions}>
                {locationTypes.map((type) => (
                  <TouchableOpacity
                    key={type.key}
                    style={[
                      styles.typeOption,
                      {
                        backgroundColor:
                          selectedType === type.key
                            ? colors.primary + "20"
                            : colors.background,
                        borderColor:
                          selectedType === type.key
                            ? colors.primary
                            : colors.border,
                      },
                    ]}
                    onPress={() => setSelectedType(type.key)}
                    activeOpacity={0.7}
                  >
                    <Ionicons
                      name={type.icon as any}
                      size={scaleFont(24)}
                      color={
                        selectedType === type.key ? colors.primary : colors.text
                      }
                    />
                    <ThemedText
                      type={selectedType === type.key ? "semi-bold" : "regular"}
                      size={14}
                      style={{
                        color:
                          selectedType === type.key
                            ? colors.primary
                            : colors.text,
                      }}
                    >
                      {t(`addresses.types.${type.key}`)}
                    </ThemedText>
                  </TouchableOpacity>
                ))}
              </View>
            </View>

            {/* Save Button */}
            <DefaultButton
              title={
                updateLocationMutation.isPending
                  ? t("common.saving")
                  : t("common.save")
              }
              onPress={handleSave}
              disabled={updateLocationMutation.isPending}
              isSubmitting={updateLocationMutation.isPending}
            />

            {/* Close Button */}
            <Pressable onPress={handleClose} style={styles.closeButton}>
              <Ionicons name="close" size={scaleFont(24)} color="black" />
            </Pressable>
          </Animated.View>
        </GestureDetector>
      </View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  modalContainer: {
    flex: 1,
  },
  overlay: {
    ...StyleSheet.absoluteFillObject,
  },
  bottomsheet_container: {
    position: "absolute",
    bottom: 0,
    left: 0,
    right: 0,
    height: SCREEN_HEIGHT * 0.85,
    borderTopLeftRadius: scale(20),
    borderTopRightRadius: scale(20),
    paddingHorizontal: scale(20),
    paddingBottom: scale(20),
  },
  line: {
    width: scale(40),
    height: scale(4),
    backgroundColor: "#E0E0E0",
    alignSelf: "center",
    marginVertical: scale(12),
    borderRadius: scale(2),
  },
  header: {
    alignItems: "center",
    marginBottom: scale(20),
  },
  closeButton: {
    position: "absolute",
    top: scale(-50),
    right: scale(20),
    height: scale(40),
    width: scale(40),
    backgroundColor: "white",
    borderRadius: scale(20),
    justifyContent: "center",
    alignItems: "center",
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  mapSection: {
    marginBottom: scale(20),
  },
  mapContainer: {
    height: scale(200),
    borderRadius: scale(12),
    overflow: "hidden",
    marginBottom: scale(12),
  },
  map: {
    flex: 1,
  },
  loaderOverlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: "rgba(255, 255, 255, 0.8)",
    justifyContent: "center",
    alignItems: "center",
  },
  locationInfo: {
    padding: scale(12),
    borderRadius: scale(8),
    backgroundColor: "rgba(0, 0, 0, 0.05)",
  },
  addressText: {
    marginBottom: scale(4),
  },
  cityText: {
    opacity: 0.7,
  },
  typeSection: {
    marginBottom: scale(20),
  },
  sectionTitle: {
    marginBottom: scale(12),
  },
  typeOptions: {
    flexDirection: "row",
    justifyContent: "space-between",
    gap: scale(12),
  },
  typeOption: {
    flex: 1,
    alignItems: "center",
    padding: scale(16),
    borderRadius: scale(12),
    borderWidth: 2,
    gap: scale(8),
  },
  saveButton: {
    paddingVertical: scale(16),
    borderRadius: scale(12),
    alignItems: "center",
    marginTop: "auto",
  },
});
