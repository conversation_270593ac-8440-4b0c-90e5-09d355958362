import { scale, scaleFont } from "@/src/_helper/Scaler";
import { ThemedText } from "@/src/components/ui/ThemedText";
import { ThemedView } from "@/src/components/ui/ThemedView";
import { Colors } from "@/src/constants/Colors";
import { useLanguage } from "@/src/context/LanguageContext";
import { useTheme } from "@/src/context/ThemeContext";
import { MaterialIcons } from "@expo/vector-icons";
import React, { useCallback } from "react";
import {
  AccessibilityState,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  View,
} from "react-native";

type Language = {
  id: string;
  name: string;
  code: string;
};

export default function LanguageHelpScreen() {
  const { currentTheme } = useTheme();
  const colors = Colors[currentTheme ?? "dark"];
  const { locale, setLocale, t } = useLanguage();

  const languages: Language[] = [
    { id: "1", name: "English", code: "en" },
    { id: "2", name: "Français", code: "fr" },
  ];

  // useCallback to avoid unnecessary re-renders onPress handlers
  const handleLanguageChange = useCallback(
    async (code: string) => {
      if (code !== locale) {
        await setLocale(code);
      }
    },
    [locale, setLocale]
  );

  return (
    <ScrollView
      style={styles.container}
      showsVerticalScrollIndicator={false}
      accessible
      accessibilityLabel={t("language.screenAccessibilityLabel")}
    >
      <View style={styles.header}>
        <ThemedText style={styles.title} size={24} accessibilityRole="header">
          {t("language.title")}
        </ThemedText>
        <ThemedText
          size={16}
          style={styles.subtitle}
          accessibilityRole="header"
        >
          {t("language.subtitle")}
        </ThemedText>
      </View>

      <View style={styles.languageList}>
        {languages.map(({ id, name, code }) => {
          const isSelected = code === locale;

          // Accessibility state for selected
          const accessibilityState: AccessibilityState = {
            selected: isSelected,
          };

          return (
            <TouchableOpacity
              key={id}
              onPress={() => handleLanguageChange(code)}
              accessibilityRole="button"
              accessibilityState={accessibilityState}
              accessibilityLabel={`${name} ${
                isSelected ? t("language.selected") : ""
              }`}
              activeOpacity={0.7}
              style={{ borderRadius: scale(12) }}
            >
              <ThemedView
                style={[
                  styles.languageItem,
                  isSelected && { backgroundColor: colors.thirdary + "22" }, // light highlight
                ]}
              >
                <ThemedText size={16}>{name}</ThemedText>
                {isSelected && (
                  <MaterialIcons
                    name="check-circle"
                    size={scaleFont(24)}
                    color={colors.secondary}
                    accessibilityElementsHidden
                    importantForAccessibility="no"
                  />
                )}
              </ThemedView>
            </TouchableOpacity>
          );
        })}
      </View>

      <ThemedText
        style={styles.note}
        accessibilityRole="text"
        accessibilityHint={t("language.noteAccessibilityHint")}
      >
        {t("language.note")}
      </ThemedText>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    marginBottom: scale(24),
  },
  title: {
    marginBottom: scale(8),
  },
  subtitle: {
    opacity: 0.7,
  },
  languageList: {
    gap: scale(8),
  },
  languageItem: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    padding: scale(16),
    borderRadius: scale(12),
  },
  note: {
    opacity: 0.6,
    textAlign: "center",
    marginTop: scale(24),
    fontStyle: "italic",
  },
});
