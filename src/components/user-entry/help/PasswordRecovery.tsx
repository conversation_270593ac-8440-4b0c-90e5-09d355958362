import { scale } from "@/src/_helper/Scaler";
import { ThemedText } from "@/src/components/ui/ThemedText";
import { Colors } from "@/src/constants/Colors";
import { useLanguage } from "@/src/context/LanguageContext";
import { useTheme } from "@/src/context/ThemeContext";
import React from "react";
import { View } from "react-native";

type ContentItemProps = {
  title?: string;
  description: string;
  titleBold?: boolean;
  titleSize?: number;
  descriptionColor: string;
  marginTopTitle?: number;
};

const ContentItem: React.FC<ContentItemProps> = ({
  title,
  description,
  titleBold = false,
  titleSize = 16,
  descriptionColor,
  marginTopTitle = scale(25),
}) => (
  <View>
    {title && (
      <ThemedText
        size={titleSize}
        type={titleBold ? "bold" : undefined}
        style={{ marginTop: marginTopTitle }}
      >
        {title}
      </ThemedText>
    )}
    <ThemedText style={{ marginTop: scale(20), color: descriptionColor }}>
      {description}
    </ThemedText>
  </View>
);

const PasswordRecovery = () => {
  const { currentTheme } = useTheme();
  const { t } = useLanguage();

  const themeColor = Colors[currentTheme ?? "dark"].primary;

  const accountCreationPairs = [
    {
      title: t("authentication.accountCreation.title"),
      description: t("authentication.accountCreation.howToCreate"),
      titleBold: true,
      titleSize: 18,
      marginTopTitle: scale(20),
    },
    {
      title: undefined,
      description: t("authentication.accountCreation.createAccountDescription"),
    },
    {
      title: t("authentication.accountCreation.incorrectEmail"),
      description: t(
        "authentication.accountCreation.incorrectEmailDescription"
      ),
    },
    {
      title: t("authentication.accountCreation.troubleCreatingAccount"),
      description: t(
        "authentication.accountCreation.troubleCreatingAccountDescription"
      ),
    },
  ];

  const loginPairs = [
    {
      title: t("authentication.login.title"),
      description: t("authentication.login.howToLogin"),
      titleBold: true,
      titleSize: 18,
      marginTopTitle: scale(25),
    },
    {
      title: undefined,
      description: t("authentication.login.loginDescription"),
    },
    {
      title: t("authentication.login.troubleLoggingIn"),
      description: t("authentication.login.troubleLoggingInDescription"),
    },
    {
      title: t("authentication.login.resetPasswordQuestion"),
      description: t("authentication.login.resetPasswordDescription"),
    },
    {
      title: t("authentication.login.didNotReceiveEmail"),
      description: t("authentication.login.didNotReceiveEmailDescription"),
    },
  ];

  return (
    <>
      <ThemedText type="bold" size={28}>
        {t("authentication.title")}
      </ThemedText>

      {accountCreationPairs.map(
        ({ title, description, titleBold, titleSize, marginTopTitle }, i) => (
          <ContentItem
            key={`accountCreation-${i}`}
            title={title}
            description={description}
            titleBold={titleBold}
            titleSize={titleSize}
            descriptionColor={themeColor}
            marginTopTitle={marginTopTitle}
          />
        )
      )}

      {loginPairs.map(
        ({ title, description, titleBold, titleSize, marginTopTitle }, i) => (
          <ContentItem
            key={`login-${i}`}
            title={title}
            description={description}
            titleBold={titleBold}
            titleSize={titleSize}
            descriptionColor={themeColor}
            marginTopTitle={marginTopTitle}
          />
        )
      )}
    </>
  );
};

export default PasswordRecovery;
