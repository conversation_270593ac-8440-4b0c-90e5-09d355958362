import { scale } from "@/src/_helper/Scaler";
import HelpOption from "@/src/components/ui/HelpOption";
import { ThemedText } from "@/src/components/ui/ThemedText";
import { ThemedView } from "@/src/components/ui/ThemedView";
import { Colors } from "@/src/constants/Colors";
import { useLanguage } from "@/src/context/LanguageContext";
import { useTheme } from "@/src/context/ThemeContext";
import React from "react";
import { StyleSheet } from "react-native";

const Help = () => {
  const { currentTheme } = useTheme();
  const themeColors = Colors[currentTheme ?? "dark"];
  const { t } = useLanguage();

  return (
    <ThemedView style={styles.container}>
      <Section
        title={t("help.sections.authentication")}
        color={themeColors.text}
      >
        <HelpOption
          title={t("help.options.loginIssues")}
          route="/(user-entry)/(help)/login-issue"
        />
        <HelpOption
          title={t("help.options.signupIssues")}
          route="/(user-entry)/(help)/signup-issue"
        />
        <HelpOption
          title={t("help.options.passwordRecovery")}
          route="/(user-entry)/(help)/password-recovery"
        />
      </Section>

      <Section title={t("help.sections.contactUs")} color={themeColors.text}>
        <HelpOption
          title={t("help.options.emailSupport")}
          route="/(user-entry)/(help)/email-support"
        />
        <HelpOption
          title={t("help.options.callSupport")}
          route="/(user-entry)/(help)/call-support"
        />
      </Section>

      <Section title={t("help.sections.settings")} color={themeColors.text}>
        <HelpOption
          title={t("help.options.language")}
          route="/(user-entry)/(help)/language"
        />
        <HelpOption
          title={t("help.options.theme")}
          route="/(user-entry)/(help)/theme"
        />
      </Section>
    </ThemedView>
  );
};

const Section: React.FC<{
  title: string;
  color: string;
  children: React.ReactNode;
}> = ({ title, color, children }) => (
  <ThemedView style={styles.sectionContainer}>
    <ThemedText type="bold" size={28} style={{ color }}>
      {title}
    </ThemedText>
    {children}
  </ThemedView>
);

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: scale(15),
    flexGrow: 1,
  },
  sectionContainer: {
    marginTop: scale(30),
  },
});

export default Help;
