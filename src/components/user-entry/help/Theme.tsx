import { scale } from "@/src/_helper/Scaler";
import { ThemedText } from "@/src/components/ui/ThemedText";
import ThemeOptionItem from "@/src/components/ui/ThemeOption";
import { useLanguage } from "@/src/context/LanguageContext";
import { useTheme } from "@/src/context/ThemeContext";
import React from "react";
import { ScrollView, StyleSheet, View } from "react-native";

export default function Theme() {
  const { theme, setTheme } = useTheme();
  const { t } = useLanguage();

  const themeOptions = [
    {
      id: "1",
      key: "light" as const,
      icon: "light-mode" as const,
      label: t("theme.light"),
      description: t("theme.lightDescription"),
    },
    {
      id: "2",
      key: "dark" as const,
      icon: "dark-mode" as const,
      label: t("theme.dark"),
      description: t("theme.darkDescription"),
    },
    {
      id: "3",
      key: "system" as const,
      icon: "settings-suggest" as const,
      label: t("theme.system"),
      description: t("theme.systemDescription"),
    },
  ];

  return (
    <>
      <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
        <View style={styles.header}>
          <ThemedText style={styles.title} size={24}>
            {t("theme.title")}
          </ThemedText>
          <ThemedText size={16} style={styles.subtitle}>
            {t("theme.subtitle")}
          </ThemedText>
        </View>

        <View style={styles.themeList}>
          {themeOptions.map(({ id, key, icon, label, description }) => (
            <ThemeOptionItem
              key={id}
              themeKey={key}
              icon={icon}
              selected={key === theme}
              onPress={() => setTheme(key)}
              label={label}
              description={description}
            />
          ))}
        </View>

        <ThemedText style={styles.note}>
          {t("theme.note", {
            defaultValue: "Note: Theme changes will apply immediately",
          })}
        </ThemedText>
      </ScrollView>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    marginBottom: scale(24),
  },
  title: {
    marginBottom: scale(8),
  },
  subtitle: {
    opacity: 0.7,
  },
  themeList: {
    gap: scale(16),
  },
  note: {
    opacity: 0.6,
    textAlign: "center",
    marginTop: scale(24),
    fontStyle: "italic",
  },
});
