import { scale } from "@/src/_helper/Scaler";
import { ThemedText } from "@/src/components/ui/ThemedText";
import { Colors } from "@/src/constants/Colors";
import { useLanguage } from "@/src/context/LanguageContext";
import { useTheme } from "@/src/context/ThemeContext";
import React from "react";
import { StyleSheet, View } from "react-native";

type IssueProps = {
  title: string;
  description: string;
  descriptionColor: string;
};

const Issue: React.FC<IssueProps> = ({
  title,
  description,
  descriptionColor,
}) => (
  <View>
    <ThemedText size={16} style={styles.issueTitle}>
      {title}
    </ThemedText>
    <ThemedText style={[styles.issueDescription, { color: descriptionColor }]}>
      {description}
    </ThemedText>
  </View>
);

const LoginIssue = () => {
  const { currentTheme } = useTheme();
  const { t } = useLanguage();
  const themeColors = Colors[currentTheme ?? "dark"];

  // Data arrays for dynamic rendering
  const commonIssues = [
    {
      title: t("loginIssue.issues.incorrectEmail.title"),
      description: t("loginIssue.issues.incorrectEmail.description"),
    },
    {
      title: t("loginIssue.issues.incorrectPassword.title"),
      description: t("loginIssue.issues.incorrectPassword.description"),
    },
    {
      title: t("loginIssue.issues.forgotPassword.title"),
      description: t("loginIssue.issues.forgotPassword.description"),
    },
  ];

  const troubleshootingSteps = [
    {
      title: t("loginIssue.steps.clearCache.title"),
      description: t("loginIssue.steps.clearCache.description"),
    },
    {
      title: t("loginIssue.steps.reinstall.title"),
      description: t("loginIssue.steps.reinstall.description"),
    },
    {
      title: t("loginIssue.steps.updateOS.title"),
      description: t("loginIssue.steps.updateOS.description"),
    },
  ];

  return (
    <>
      <ThemedText type="bold" size={28} style={styles.title}>
        {t("loginIssue.title")}
      </ThemedText>
      <ThemedText size={16} style={styles.subtitle}>
        {t("loginIssue.subtitle")}
      </ThemedText>

      <ThemedText type="bold" size={28} style={styles.sectionTitle}>
        {t("loginIssue.commonIssues")}
      </ThemedText>

      {commonIssues.map(({ title, description }, i) => (
        <Issue
          key={`commonIssue-${i}`}
          title={title}
          description={description}
          descriptionColor={themeColors.primary}
        />
      ))}

      <ThemedText type="bold" size={28} style={styles.sectionTitle}>
        {t("loginIssue.troubleshootingSteps")}
      </ThemedText>

      {troubleshootingSteps.map(({ title, description }, i) => (
        <Issue
          key={`troubleshootingStep-${i}`}
          title={title}
          description={description}
          descriptionColor={themeColors.primary}
        />
      ))}
    </>
  );
};

const styles = StyleSheet.create({
  title: {
    marginTop: 0,
  },
  subtitle: {
    marginTop: scale(25),
  },
  sectionTitle: {
    marginTop: scale(20),
  },
  issueTitle: {
    marginTop: scale(25),
  },
  issueDescription: {
    marginTop: scale(20),
  },
});

export default LoginIssue;
