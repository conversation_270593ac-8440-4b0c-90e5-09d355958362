import { scale } from "@/src/_helper/Scaler";
import ContactOption from "@/src/components/ui/ContactOption";
import { ThemedText } from "@/src/components/ui/ThemedText";
import { useLanguage } from "@/src/context/LanguageContext";
import React from "react";
import { Linking, View } from "react-native";

const EmailSupport = () => {
  const { t } = useLanguage();

  return (
    <>
      <ThemedText type="bold" size={28}>
        {t("emailSupport.title")}
      </ThemedText>

      <ThemedText size={16} style={{ marginTop: scale(20) }}>
        {t("emailSupport.subtitle")}
      </ThemedText>

      <View style={{ marginTop: scale(30), gap: scale(20) }}>
        <ContactOption
          icon="note"
          title={t("emailSupport.contactForm.title")}
          description={t("emailSupport.contactForm.description")}
          onPress={() => Linking.openURL("https://www.locasa.app/contact")}
        />
        <ContactOption
          icon="envelope-letter"
          title={t("emailSupport.emailUs.title")}
          description={t("emailSupport.emailUs.description")}
          onPress={() => Linking.openURL("mailto:<EMAIL>")}
        />
      </View>
    </>
  );
};

export default EmailSupport;
