import { scale } from "@/src/_helper/Scaler";
import { ThemedText } from "@/src/components/ui/ThemedText";
import { Colors } from "@/src/constants/Colors";
import { useLanguage } from "@/src/context/LanguageContext";
import { useTheme } from "@/src/context/ThemeContext";
import React from "react";
import { StyleSheet, View } from "react-native";

type HelpItemProps = {
  title: string;
  description: string;
  descriptionColor: string;
};

const HelpItem: React.FC<HelpItemProps> = ({
  title,
  description,
  descriptionColor,
}) => (
  <View>
    <ThemedText size={16} style={styles.itemTitle}>
      {title}
    </ThemedText>
    <ThemedText style={[styles.itemDescription, { color: descriptionColor }]}>
      {description}
    </ThemedText>
  </View>
);

const SignUpIssue = () => {
  const { t } = useLanguage();
  const { currentTheme } = useTheme();
  const themeColors = Colors[currentTheme ?? "dark"];

  // Array of common issues
  const commonIssues = [
    {
      title: t("signupHelp.invalidEmailTitle"),
      description: t("signupHelp.invalidEmailDesc"),
    },
    {
      title: t("signupHelp.passwordReqTitle"),
      description: t("signupHelp.passwordReqDesc"),
    },
    {
      title: t("signupHelp.incorrectPasswordTitle"),
      description: t("signupHelp.incorrectPasswordDesc"),
    },
    {
      title: t("signupHelp.existingAccountTitle"),
      description: t("signupHelp.existingAccountDesc"),
    },
  ];

  // Array of troubleshooting steps
  const troubleshootingSteps = [
    {
      title: t("signupHelp.verifyCredsTitle"),
      description: t("signupHelp.verifyCredsDesc"),
    },
    {
      title: t("signupHelp.socialMediaTitle"),
      description: t("signupHelp.socialMediaDesc"),
    },
    {
      title: t("signupHelp.differentDeviceTitle"),
      description: t("signupHelp.differentDeviceDesc"),
    },
    {
      title: t("signupHelp.contactSupportTitle"),
      description: t("signupHelp.contactSupportDesc"),
    },
  ];

  return (
    <>
      <ThemedText type="bold" size={28}>
        {t("signupHelp.title")}
      </ThemedText>
      <ThemedText size={16} style={styles.intro}>
        {t("signupHelp.intro")}
      </ThemedText>

      <ThemedText type="bold" size={28} style={styles.sectionTitle}>
        {t("signupHelp.commonIssuesTitle")}
      </ThemedText>

      {commonIssues.map(({ title, description }, idx) => (
        <HelpItem
          key={`commonIssue-${idx}`}
          title={title}
          description={description}
          descriptionColor={themeColors.primary}
        />
      ))}

      <ThemedText type="bold" size={28} style={styles.sectionTitle}>
        {t("signupHelp.troubleshootingTitle")}
      </ThemedText>

      {troubleshootingSteps.map(({ title, description }, idx) => (
        <HelpItem
          key={`troubleshootingStep-${idx}`}
          title={title}
          description={description}
          descriptionColor={themeColors.primary}
        />
      ))}
    </>
  );
};

const styles = StyleSheet.create({
  intro: {
    marginTop: scale(25),
  },
  sectionTitle: {
    marginTop: scale(20),
  },
  itemTitle: {
    marginTop: scale(25),
  },
  itemDescription: {
    marginTop: scale(20),
  },
});

export default SignUpIssue;
