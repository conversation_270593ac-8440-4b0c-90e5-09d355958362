import { scale, scaleFont } from "@/src/_helper/Scaler";
import { ThemedText } from "@/src/components/ui/ThemedText";
import { Colors } from "@/src/constants/Colors";
import { useLanguage } from "@/src/context/LanguageContext";
import { useTheme } from "@/src/context/ThemeContext";
import Entypo from "@expo/vector-icons/Entypo";
import React from "react";
import { Linking, View } from "react-native";

const CallSupport = () => {
  const { currentTheme } = useTheme();
  const { t } = useLanguage();

  return (
    <>
      <ThemedText type="bold" size={28}>
        {t("callSupport.title")}
      </ThemedText>
      <ThemedText size={16} style={{ marginTop: scale(25) }}>
        {t("callSupport.subtitle")}
      </ThemedText>
      <View
        style={{
          flexDirection: "row",
          justifyContent: "space-between",
          alignItems: "center",
          marginTop: scale(25),
        }}
      >
        <ThemedText
          onPress={() => Linking.openURL("tel:+21625711161")}
          size={16}
        >
          +21625711161
        </ThemedText>
        <Entypo
          name="phone"
          size={scaleFont(20)}
          color={Colors[currentTheme ?? "dark"].text}
        />
      </View>
    </>
  );
};

export default CallSupport;
