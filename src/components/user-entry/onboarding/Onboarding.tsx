import React, { useRef, useState } from "react";
import {
  Dimensions,
  FlatList,
  Image,
  NativeScrollEvent,
  NativeSyntheticEvent,
  StyleSheet,
  View,
} from "react-native";

import { scale } from "@/src/_helper/Scaler";
import DefaultButton from "@/src/components/buttons/Default";
import { DotIndicator } from "@/src/components/onboarding/DotIndicator";
import { ThemedText } from "@/src/components/ui/ThemedText";
import { useLanguage } from "@/src/context/LanguageContext";
import { setSeenOnboarding } from "@/src/context/RedirectContext";
import { router } from "expo-router";

const { width } = Dimensions.get("window");

export const Onboarding = () => {
  const { t } = useLanguage();

  // Build onboarding data dynamically with translations
  const onboardingData = [
    {
      key: "1",
      title: t("onboarding.slide.title"),
      description: t("onboarding.slide.description"),
      image: require("../../../assets/onboarding/frame-one.png"),
    },
    {
      key: "2",
      title: t("onboarding.slide-two.title"),
      description: t("onboarding.slide-two.description"),
      image: require("../../../assets/onboarding/frame-two.png"),
    },
    {
      key: "3",
      title: t("onboarding.slide-three.title"),
      description: t("onboarding.slide-three.description"),
      image: require("../../../assets/onboarding/frame-one.png"),
    },
  ];

  const [currentIndex, setCurrentIndex] = useState(0);
  const flatListRef = useRef<FlatList>(null);

  const handleNext = () => {
    if (currentIndex < onboardingData.length - 1) {
      flatListRef.current?.scrollToIndex({ index: currentIndex + 1 });
    } else {
      setSeenOnboarding();
      router.replace("/(user-entry)/home");
    }
  };

  const handleScrollEnd = (event: NativeSyntheticEvent<NativeScrollEvent>) => {
    const offsetX = event.nativeEvent.contentOffset.x;
    const index = Math.round(offsetX / width);
    setCurrentIndex(index);
  };

  return (
    <>
      <FlatList
        ref={flatListRef}
        data={onboardingData}
        keyExtractor={(item) => item.key}
        horizontal
        pagingEnabled
        snapToAlignment="center"
        decelerationRate="fast"
        showsHorizontalScrollIndicator={false}
        onMomentumScrollEnd={handleScrollEnd}
        getItemLayout={(_, index) => ({
          length: width,
          offset: width * index,
          index,
        })}
        renderItem={({ item }) => (
          <View style={[styles.page, { width: width }]}>
            <Image
              source={item.image}
              style={styles.image}
              resizeMode="cover"
            />
            <DotIndicator
              currentIndex={currentIndex}
              length={onboardingData.length}
            />
            <ThemedText type="bold" size={28} style={styles.title}>
              {item.title}
            </ThemedText>
            <ThemedText size={16} style={styles.description}>
              {item.description}
            </ThemedText>
          </View>
        )}
      />

      <DefaultButton
        onPress={handleNext}
        title={
          currentIndex === onboardingData.length - 1
            ? t("onboarding.getStarted")
            : t("onboarding.next")
        }
      />
    </>
  );
};

const styles = StyleSheet.create({
  page: {
    flex: 1,
    justifyContent: "flex-start",
    alignItems: "center",
  },
  image: {
    width: width,
    height: scale(320),
    marginTop: scale(40),
  },
  title: {
    textAlign: "center",
    marginTop: scale(20),
  },
  description: {
    textAlign: "center",
    marginTop: scale(10),
  },
});

export default Onboarding;
