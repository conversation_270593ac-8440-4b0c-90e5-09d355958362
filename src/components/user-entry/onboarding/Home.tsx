import { scale } from "@/src/_helper/Scaler";
import BottomSheetLogin from "@/src/components/bottom-sheet/BottomSheetLogin";
import DefaultButton from "@/src/components/buttons/Default";
import { ThemedText } from "@/src/components/ui/ThemedText";
import { ThemedView } from "@/src/components/ui/ThemedView";
import { Colors } from "@/src/constants/Colors";
import { useLanguage } from "@/src/context/LanguageContext";
import { useTheme } from "@/src/context/ThemeContext";
import React, { useState } from "react";
import { Image, Linking, StyleSheet, View } from "react-native";

const Home = () => {
  const { currentTheme } = useTheme();
  const { t } = useLanguage();
  const themeColors = Colors[currentTheme ?? "dark"];

  const [isBottomSheetVisible, setBottomSheetVisible] = useState(false);
  const [type, setType] = useState<"signin" | "signup">("signin");

  const openBottomSheet = (authType: "signin" | "signup") => {
    setType(authType);
    setBottomSheetVisible(true);
  };

  return (
    <>
      <ThemedView style={styles.container}>
        <View style={styles.page}>
          <Image
            source={require("../../../assets/onboarding/frame-two.png")}
            style={styles.image}
            resizeMode="cover"
          />

          <ThemedText type="bold" size={28} style={styles.title}>
            {t("homepage.discoverLocalTreasures")}
          </ThemedText>

          <ThemedText size={16} style={styles.description}>
            {t("homepage.connectWithLocalArtisans")}
          </ThemedText>

          <View style={styles.buttonWrapper}>
            <DefaultButton
              onPress={() => openBottomSheet("signin")}
              title={t("homepage.logIn")}
            />
            <DefaultButton
              onPress={() => openBottomSheet("signup")}
              color={themeColors.thirdary}
              title={t("homepage.signUp")}
              active
            />
          </View>
        </View>

        <View style={styles.footer}>
          <ThemedText
            style={[styles.footerText, { color: themeColors.secondary }]}
          >
            {t("homepage.byContinuingAgree")}{" "}
            <ThemedText
              onPress={() => Linking.openURL("https://www.google.com")}
              type="semi-bold"
              style={[styles.linkText, { color: themeColors.primary }]}
            >
              {t("homepage.termsOfService")} {t("homepage.privacyPolicy")}
            </ThemedText>
          </ThemedText>
        </View>

        <BottomSheetLogin
          isVisible={isBottomSheetVisible}
          onClose={() => setBottomSheetVisible(false)}
          type={type}
        />
      </ThemedView>
    </>
  );
};

const styles = StyleSheet.create({
  root: {
    flex: 1,
  },
  container: {
    flex: 1,
    justifyContent: "space-between",
  },
  page: {
    alignItems: "center",
  },
  image: {
    width: "100%",
    height: scale(320),
  },
  title: {
    textAlign: "center",
    marginTop: scale(20),
  },
  description: {
    textAlign: "center",
    marginTop: scale(20),
    marginBottom: scale(20),
  },
  buttonWrapper: {
    flexDirection: "column",
    alignItems: "center",
    justifyContent: "space-between",
    width: "100%",
  },
  footer: {
    paddingVertical: scale(10),
    paddingHorizontal: scale(15),
  },
  footerText: {
    textAlign: "center",
  },
  linkText: {
    textAlign: "center",
  },
});

export default Home;
