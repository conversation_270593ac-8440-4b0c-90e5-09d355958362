import { scale } from "@/src/_helper/Scaler";
import { ResetPasswordSchema } from "@/src/_helper/validator/AuthValidation";
import DefaultButton from "@/src/components/buttons/Default";
import OTPInput from "@/src/components/inputs/OTPInput";
import PasswordTextInput from "@/src/components/inputs/PasswordTextInput";
import TextInput from "@/src/components/inputs/TextInput";
import { ThemedText } from "@/src/components/ui/ThemedText";
import { ThemedView } from "@/src/components/ui/ThemedView";
import { Colors } from "@/src/constants/Colors";
import { useLanguage } from "@/src/context/LanguageContext";
import { useTheme } from "@/src/context/ThemeContext";
import useResendEmailOTP from "@/src/services/querys/auth/useResendEmailOTP";
import useResetPassword from "@/src/services/querys/auth/useResetPassword";
import useVerifyEmailOTP from "@/src/services/querys/auth/useVerifyEmailOTP";
import { useLocalSearchParams } from "expo-router";
import { Formik } from "formik";
import { useCallback, useEffect, useRef, useState } from "react";
import { StyleSheet, TouchableOpacity, View } from "react-native";

export default function ResetPasswordForm() {
  const { currentTheme } = useTheme();
  const { t } = useLanguage();
  const { email, type } = useLocalSearchParams() as {
    email: string;
    type: "client" | "vendor";
  };

  const mutation = useVerifyEmailOTP();
  const resendMutation = useResendEmailOTP();
  const resetMutation = useResetPassword();

  const [isOtpVisible, setIsOtpVisible] = useState(true);
  const [timer, setTimer] = useState(30);
  const [isResendDisabled, setIsResendDisabled] = useState(false);
  const [isVerifyingOtp, setIsVerifyingOtp] = useState(false);
  const [verifiedOtp, setVerifiedOtp] = useState(""); // Store verified OTP for password reset

  const timerRef = useRef<NodeJS.Timeout | null | number>(null);

  useEffect(() => {
    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
    };
  }, []);

  const handleResend = useCallback(() => {
    setIsResendDisabled(true);
    setTimer(30);

    resendMutation.mutateAsync({
      email,
      type: "reset-password",
      userType: type,
    });

    if (timerRef.current) {
      clearInterval(timerRef.current);
    }

    timerRef.current = setInterval(() => {
      setTimer((prev) => {
        if (prev <= 1) {
          clearInterval(timerRef.current!);
          timerRef.current = null;
          setIsResendDisabled(false);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);
  }, [email, type, resendMutation]);

  // Memoized OTP verification function
  const verifyOTP = useCallback(
    async (otpValue: string) => {
      if (otpValue.length === 4 && !isVerifyingOtp) {
        setIsVerifyingOtp(true);
        try {
          await mutation.mutateAsync({
            email,
            otp: otpValue,
            setIsOtpVisible,
            type: "reset-password",
            userType: type,
          });
          setVerifiedOtp(otpValue);
        } catch (error) {
          console.error("OTP verification failed:", error);
        } finally {
          setIsVerifyingOtp(false);
        }
      }
    },
    [email, type, mutation, isVerifyingOtp]
  );

  return (
    <View style={styles.container}>
      <ThemedText type="bold" size={28}>
        {t("auth.resetPassword.title")}
      </ThemedText>

      <ThemedText size={16} style={styles.descriptionText}>
        {t("auth.resetPassword.description_parts.before")}{" "}
        <ThemedText
          size={16}
          type="semi-bold"
          style={{ color: Colors[currentTheme ?? "dark"].primary }}
        >
          {email}.
        </ThemedText>
        {"\n"}
        {t("auth.resetPassword.description_parts.after")}
      </ThemedText>

      <Formik initialValues={{ otp: "" }} onSubmit={() => {}}>
        {({ handleChange, values }) => (
          <View>
            <ThemedView
              style={[
                styles.otpContainer,
                (!isOtpVisible || isVerifyingOtp) && styles.disabledOpacity,
              ]}
            >
              <OTPInput
                onOtpChange={(val) => {
                  handleChange("otp")(val);
                  if (val.length === 4) {
                    verifyOTP(val);
                  }
                }}
                editable={isOtpVisible && !isVerifyingOtp}
                otpValue={values.otp}
              />
              <View style={styles.resendContainer}>
                <ThemedText>{t("auth.resetPassword.noOtp")} </ThemedText>
                <TouchableOpacity
                  onPress={handleResend}
                  disabled={
                    isResendDisabled ||
                    !isOtpVisible ||
                    isVerifyingOtp ||
                    resendMutation.isPending
                  }
                >
                  <ThemedText
                    style={[{ color: Colors[currentTheme ?? "dark"].primary }]}
                  >
                    {isResendDisabled
                      ? `${timer}s`
                      : t("auth.resetPassword.resend")}
                  </ThemedText>
                </TouchableOpacity>
              </View>
            </ThemedView>
          </View>
        )}
      </Formik>

      <Formik
        initialValues={{
          password: "",
          confirmPassword: "",
        }}
        validationSchema={ResetPasswordSchema}
        onSubmit={(values, { setSubmitting }) => {
          resetMutation.mutateAsync({
            email,
            password: values.password,
            type,
            otp: verifiedOtp, // Use the stored verified OTP
          });
          setSubmitting(false);
        }}
      >
        {({ handleChange, handleSubmit, values, errors, touched }) => (
          <ThemedView
            style={[
              styles.passwordFormContainer,
              (isOtpVisible || isVerifyingOtp) && styles.disabledOpacity,
            ]}
          >
            <View>
              <PasswordTextInput
                onChangeText={handleChange("password")}
                value={values.password}
                placeholder={t("auth.resetPassword.password.placeholder")}
                isPassword
                error={touched.password && errors.password}
                editable={!isOtpVisible && !isVerifyingOtp}
              />
              <TextInput
                onChangeText={handleChange("confirmPassword")}
                value={values.confirmPassword}
                placeholder={t(
                  "auth.resetPassword.confirmPassword.placeholder"
                )}
                isPassword
                error={touched.confirmPassword && errors.confirmPassword}
                editable={!isOtpVisible && !isVerifyingOtp}
              />
            </View>
            <DefaultButton
              size="large"
              isSubmitting={resetMutation.isPending}
              onPress={handleSubmit}
              title={t("auth.resetPassword.submit")}
              disabled={isOtpVisible || isVerifyingOtp}
              style={styles.submitButton}
            />
          </ThemedView>
        )}
      </Formik>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    width: "100%",
    height: "100%",
    paddingTop: scale(20),
  },
  descriptionText: {
    marginTop: scale(12),
    marginBottom: scale(20),
  },
  otpContainer: {
    marginTop: scale(24),
  },
  disabledOpacity: {
    opacity: 0.4,
  },
  resendContainer: {
    flexDirection: "row",
    alignItems: "center",
    marginTop: scale(16),
  },

  passwordFormContainer: {
    flex: 1,
    justifyContent: "space-between",
    marginTop: scale(32),
  },

  submitButton: {
    marginBottom: scale(20),
  },
});
