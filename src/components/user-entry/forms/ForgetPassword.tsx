import { scale } from "@/src/_helper/Scaler";
import { ForgetPasswordSchema } from "@/src/_helper/validator/AuthValidation";
import DefaultButton from "@/src/components/buttons/Default";
import TextInput from "@/src/components/inputs/TextInput";
import TextWithLink from "@/src/components/ui/TextWithLink";
import { ThemedText } from "@/src/components/ui/ThemedText";
import { ThemedView } from "@/src/components/ui/ThemedView";
import { useLanguage } from "@/src/context/LanguageContext";
import useForgetPassword from "@/src/services/querys/auth/useForgetPassword";
import { useLocalSearchParams } from "expo-router";
import { Formik } from "formik";
import { useMemo } from "react";
import { StyleSheet, View } from "react-native";

export default function ForgetPasswordForm() {
  const { t } = useLanguage();
  const { type } = useLocalSearchParams() as { type: "client" | "vendor" };
  const mutation = useForgetPassword();

  const initialValues = useMemo(() => ({ email: "" }), []);

  const handleForgetPassword = async (
    values: typeof initialValues,
    { setSubmitting }: any
  ) => {
    try {
      await mutation.mutateAsync({
        email: values.email,
        type,
      });
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <View style={styles.container}>
      <ThemedText type="bold" size={28} style={styles.title}>
        {t("auth.forgotPassword.title")}
      </ThemedText>
      <ThemedText size={16} style={styles.description}>
        {t("auth.forgotPassword.description")}
      </ThemedText>

      <Formik
        initialValues={initialValues}
        onSubmit={handleForgetPassword}
        validationSchema={ForgetPasswordSchema}
      >
        {({
          handleChange,
          handleSubmit,
          values,
          errors,
          touched,
          isValid,
          dirty,
        }) => (
          <ThemedView style={styles.form}>
            <View style={styles.inputSection}>
              <TextInput
                onChangeText={handleChange("email")}
                value={values.email}
                placeholder={t("auth.forgotPassword.email.placeholder")}
                error={touched.email && errors.email}
              />
              <TextWithLink
                text={t("auth.register.haveAccount")}
                link={t("auth.register.login")}
                href={"/(user-entry)/signin"}
                type={type}
              />
            </View>

            <DefaultButton
              size="large"
              onPress={handleSubmit}
              isSubmitting={mutation.isPending}
              disabled={!isValid || !dirty}
              title={t("auth.forgotPassword.resetButton")}
            />
          </ThemedView>
        )}
      </Formik>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    width: "100%",
    height: "100%",
  },
  title: {
    marginBottom: scale(10),
  },
  description: {
    marginBottom: scale(20),
  },
  form: {
    flexGrow: 1,
    justifyContent: "space-between",
    marginTop: scale(20),
  },
  inputSection: {
    gap: scale(10),
  },
});
