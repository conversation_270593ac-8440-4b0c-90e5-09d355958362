import { scale } from "@/src/_helper/Scaler";
import OTPInput from "@/src/components/inputs/OTPInput";
import { ThemedText } from "@/src/components/ui/ThemedText";
import { ThemedView } from "@/src/components/ui/ThemedView";
import { Colors } from "@/src/constants/Colors";
import { useLanguage } from "@/src/context/LanguageContext";
import { useTheme } from "@/src/context/ThemeContext";
import useResendEmailOTP from "@/src/services/querys/auth/useResendEmailOTP";
import useVerifyEmailOTP from "@/src/services/querys/auth/useVerifyEmailOTP";
import { useLocalSearchParams } from "expo-router";
import { Formik } from "formik";
import { useCallback, useEffect, useRef, useState } from "react";
import { StyleSheet, TouchableOpacity, View } from "react-native";

export default function ValidateEmailForm() {
  const { currentTheme } = useTheme();
  const { t } = useLanguage();
  const { email, type: userType } = useLocalSearchParams() as {
    email: string;
    type: "client" | "vendor";
  };

  const mutation = useVerifyEmailOTP();
  const resendMutation = useResendEmailOTP();

  const [isOtpVisible, setIsOtpVisible] = useState(true);
  const [timer, setTimer] = useState(30);
  const [isResendDisabled, setIsResendDisabled] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false); // Prevent multiple submissions

  const timerRef = useRef<NodeJS.Timeout | null | number>(null);

  const handleResend = useCallback(() => {
    setIsResendDisabled(true);
    setTimer(30);

    resendMutation.mutateAsync({
      email,
      type: "created-account",
      userType,
    });

    if (timerRef.current) {
      clearInterval(timerRef.current);
    }

    timerRef.current = setInterval(() => {
      setTimer((prev) => {
        if (prev <= 1) {
          clearInterval(timerRef.current!);
          timerRef.current = null;
          setIsResendDisabled(false);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);
  }, [email, userType, resendMutation]);

  // Memoized OTP verification function
  const verifyOTP = useCallback(
    async (otpValue: string) => {
      if (otpValue.length === 4 && !isSubmitting) {
        setIsSubmitting(true);
        try {
          await mutation.mutateAsync({
            email,
            otp: otpValue,
            setIsOtpVisible,
            userType,
            type: "created-account",
          });
        } catch (error) {
          console.error("OTP verification failed:", error);
        } finally {
          setIsSubmitting(false);
        }
      }
    },
    [email, userType, mutation, isSubmitting]
  );

  useEffect(() => {
    return () => {
      if (timerRef.current) clearInterval(timerRef.current);
    };
  }, []);

  return (
    <View style={styles.container}>
      <ThemedText type="bold" size={28}>
        {t("auth.verifyEmail.title")}
      </ThemedText>

      <ThemedText size={16} style={styles.textMarginTop}>
        {t("auth.verifyEmail.description_parts.before")}{" "}
        <ThemedText
          type="semi-bold"
          size={16}
          style={[{ color: Colors[currentTheme ?? "dark"].primary }]}
        >
          {email}.
        </ThemedText>
      </ThemedText>

      <ThemedText size={16} style={styles.textMarginTop}>
        {t("auth.verifyEmail.description_parts.after")}
      </ThemedText>

      <Formik initialValues={{ otp: "" }} onSubmit={() => {}}>
        {({ handleChange, values, setFieldValue }) => (
          <View>
            <ThemedView
              style={[
                styles.otpContainer,
                (!isOtpVisible || isSubmitting) && styles.disabledOpacity,
              ]}
            >
              <OTPInput
                onOtpChange={(value) => {
                  handleChange("otp")(value);
                  // Only verify when OTP is complete and not already submitting
                  if (value.length === 4) {
                    verifyOTP(value);
                  }
                }}
                editable={isOtpVisible && !isSubmitting}
                otpValue={values.otp}
              />
              <View style={styles.resendContainer}>
                <ThemedText>{t("auth.resetPassword.noOtp")} </ThemedText>
                <TouchableOpacity
                  onPress={handleResend}
                  disabled={
                    isResendDisabled ||
                    !isOtpVisible ||
                    isSubmitting ||
                    resendMutation.isPending
                  }
                >
                  <ThemedText
                    style={[{ color: Colors[currentTheme ?? "dark"].primary }]}
                  >
                    {isResendDisabled
                      ? `${timer}s`
                      : t("auth.resetPassword.resend")}
                  </ThemedText>
                </TouchableOpacity>
              </View>
            </ThemedView>
          </View>
        )}
      </Formik>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    width: "100%",
    height: "100%",
    paddingTop: scale(20),
  },
  textMarginTop: {
    marginTop: scale(12),
    marginBottom: scale(20),
  },

  otpContainer: {
    marginTop: scale(24),
  },
  disabledOpacity: {
    opacity: 0.4,
  },
  resendContainer: {
    flexDirection: "row",
    alignItems: "center",
    marginTop: scale(16),
  },
});
