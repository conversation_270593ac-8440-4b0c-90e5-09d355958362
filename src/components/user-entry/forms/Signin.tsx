import { scale } from "@/src/_helper/Scaler";
import { LoginSchema } from "@/src/_helper/validator/AuthValidation";
import DefaultButton from "@/src/components/buttons/Default";
import TextInput from "@/src/components/inputs/TextInput";
import TextWithLink from "@/src/components/ui/TextWithLink";
import { ThemedText } from "@/src/components/ui/ThemedText";
import { ThemedView } from "@/src/components/ui/ThemedView";
import { useLanguage } from "@/src/context/LanguageContext";
import useLoginMutation from "@/src/services/querys/auth/useLoginMutation";
import { useLocalSearchParams } from "expo-router";
import { Formik } from "formik";
import { useMemo } from "react";
import { StyleSheet, View } from "react-native";

export default function SignInForm() {
  const { t } = useLanguage();
  const { type } = useLocalSearchParams() as { type: "client" | "vendor" };
  const mutation = useLoginMutation();

  const initialValues = useMemo(
    () => ({
      email: "",
      password: "",
    }),
    []
  );

  const handleLogin = async (
    values: typeof initialValues,
    { setSubmitting }: any
  ) => {
    try {
      await mutation.mutateAsync({
        email: values.email,
        password: values.password,
        type: type,
      });
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <View style={styles.container}>
      <ThemedText size={28} type="bold" style={styles.title}>
        {t("auth.login.title")}
      </ThemedText>

      <Formik
        initialValues={initialValues}
        onSubmit={handleLogin}
        validationSchema={LoginSchema}
      >
        {({
          handleChange,
          handleSubmit,
          values,
          errors,
          touched,
          isValid,
          dirty,
        }) => (
          <ThemedView style={styles.form}>
            <View style={styles.inputsSection}>
              <TextInput
                onChangeText={handleChange("email")}
                value={values.email}
                placeholder={t("auth.login.email.placeholder")}
                error={touched.email && errors.email}
              />
              <TextInput
                onChangeText={handleChange("password")}
                value={values.password}
                placeholder={t("auth.login.password.placeholder")}
                isPassword
                error={touched.password && errors.password}
              />
              <TextWithLink
                text=""
                link={t("auth.login.forgetPasswordLink")}
                href={"/(user-entry)/forget-password"}
                type={type}
              />
            </View>

            <View style={styles.actionsSection}>
              <DefaultButton
                title={t("auth.login.loginButton")}
                onPress={handleSubmit}
                isSubmitting={mutation.isPending}
                disabled={!isValid || !dirty}
              />
              <TextWithLink
                text={t("auth.login.noAccount")}
                link={t("auth.login.signUp")}
                href={"/(user-entry)/signup"}
                type={type}
              />
            </View>
          </ThemedView>
        )}
      </Formik>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    width: "100%",
    height: "100%",
  },
  title: {
    marginBottom: scale(20),
  },
  form: {
    flexDirection: "column",
    justifyContent: "space-between",
    flexGrow: 1,
  },
  inputsSection: {
    gap: scale(10),
  },
  actionsSection: {
    justifyContent: "center",
    alignItems: "center",
    gap: scale(10),
  },
});
