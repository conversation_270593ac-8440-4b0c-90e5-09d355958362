import { scale } from "@/src/_helper/Scaler";
import { SignupSchema } from "@/src/_helper/validator/AuthValidation";
import DefaultButton from "@/src/components/buttons/Default";
import PasswordTextInput from "@/src/components/inputs/PasswordTextInput";
import TextInput from "@/src/components/inputs/TextInput";
import TextWithLink from "@/src/components/ui/TextWithLink";
import { ThemedText } from "@/src/components/ui/ThemedText";
import { ThemedView } from "@/src/components/ui/ThemedView";
import { useLanguage } from "@/src/context/LanguageContext";
import useSignupMutation from "@/src/services/querys/auth/useSignupMutation";
import { useLocalSearchParams } from "expo-router";
import { Formik } from "formik";
import { useMemo } from "react";
import { StyleSheet, View } from "react-native";

export default function SignUpForm() {
  const { t } = useLanguage();
  const { type } = useLocalSearchParams() as { type: "client" | "vendor" };
  const mutation = useSignupMutation();

  const initialValues = useMemo(
    () => ({
      fullName: "",
      email: "",
      password: "",
      confirmPassword: "",
    }),
    []
  );

  const handleSignup = async (
    values: typeof initialValues,
    { setSubmitting }: any
  ) => {
    try {
      await mutation.mutateAsync({
        fullName: values.fullName,
        email: values.email,
        password: values.password,
        type,
      });
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <View style={styles.container}>
      <ThemedText size={28} type="bold" style={styles.title}>
        {t("auth.register.title")}
      </ThemedText>

      <Formik
        initialValues={initialValues}
        onSubmit={handleSignup}
        validationSchema={SignupSchema}
      >
        {({
          handleChange,
          handleSubmit,
          values,
          errors,
          touched,
          isValid,
          dirty,
        }) => (
          <ThemedView style={styles.form}>
            <View style={styles.inputSection}>
              <TextInput
                onChangeText={handleChange("fullName")}
                value={values.fullName}
                placeholder={t("auth.register.label.placeholder")}
                error={touched.fullName && errors.fullName}
              />
              <TextInput
                onChangeText={handleChange("email")}
                value={values.email}
                placeholder={t("auth.register.email.placeholder")}
                error={touched.email && errors.email}
              />
              <PasswordTextInput
                onChangeText={handleChange("password")}
                value={values.password}
                placeholder={t("auth.register.password.placeholder")}
                isPassword
                error={touched.password && errors.password}
              />
              <TextInput
                onChangeText={handleChange("confirmPassword")}
                value={values.confirmPassword}
                placeholder={t("auth.register.confirmPassword.placeholder")}
                isPassword
                error={touched.confirmPassword && errors.confirmPassword}
              />
              <TextWithLink
                text={t("auth.register.haveAccount")}
                link={t("auth.register.login")}
                href={"/(user-entry)/signin"}
                type={type}
              />
            </View>

            <DefaultButton
              size="large"
              onPress={handleSubmit}
              isSubmitting={mutation.isPending}
              disabled={!isValid || !dirty}
              title={t("auth.register.registerButton")}
            />
            {/* <SocialMediaLinks /> */}
          </ThemedView>
        )}
      </Formik>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    width: "100%",
    height: "100%",
  },
  title: {
    marginBottom: scale(20),
  },
  form: {
    flexGrow: 1,
    justifyContent: "space-between",
  },
  inputSection: {
    gap: scale(10),
  },
});
