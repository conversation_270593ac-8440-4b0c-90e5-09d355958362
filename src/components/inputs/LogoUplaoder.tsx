import { scale } from "@/src/_helper/Scaler";
import { ThemedText } from "@/src/components/ui/ThemedText";
import { Colors } from "@/src/constants/Colors";
import { useTheme } from "@/src/context/ThemeContext";
import { Ionicons } from "@expo/vector-icons";
import React from "react";
import { Image, StyleSheet, TouchableOpacity, View } from "react-native";
import ErrorText from "../shared/ErrorText";

export default function LogoUploader({
  pickImage,
  imageUri,
  existingImage,
  error,
}: {
  pickImage: () => void;
  imageUri?: string | null | undefined;
  existingImage?: string | null | undefined;
  error: string;
}) {
  const { currentTheme } = useTheme();
  const theme = currentTheme ?? "dark";
  const displayImage = imageUri || existingImage;
  const isNewImage = !!imageUri;

  return (
    <View>
      <TouchableOpacity
        style={[
          styles.container,
          {
            borderColor: Colors[theme].border,
            backgroundColor: Colors[theme].input,
          },
        ]}
        onPress={pickImage}
        activeOpacity={0.7}
      >
        {displayImage ? (
          <View style={styles.imageContainer}>
            <Image source={{ uri: displayImage }} style={styles.logoPreview} />

            {isNewImage && (
              <View
                style={[
                  styles.newImageBadge,
                  {
                    backgroundColor: Colors[theme].green,
                  },
                ]}
              >
                <ThemedText size={10} style={styles.badgeText}>
                  NEW
                </ThemedText>
              </View>
            )}
          </View>
        ) : (
          <View style={styles.uploadPrompt}>
            <Ionicons
              name="cloud-upload-outline"
              size={40}
              color={Colors[theme].input_text}
            />
            <ThemedText
              size={14}
              style={[
                styles.text,
                {
                  color: Colors[theme].input_text,
                },
              ]}
            >
              Tap to choose logo
            </ThemedText>
          </View>
        )}

        {displayImage && (
          <View style={styles.changePrompt}>
            <ThemedText
              size={12}
              style={[
                styles.changeText,
                {
                  color: Colors[theme].input_text,
                },
              ]}
            >
              {isNewImage ? "Tap to change new logo" : "Tap to update logo"}
            </ThemedText>
          </View>
        )}
      </TouchableOpacity>
      {error && <ErrorText error={error} />}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    marginTop: scale(20),
    minHeight: scale(120),
    borderWidth: scale(2),
    borderStyle: "dashed",
    borderRadius: scale(12),
    justifyContent: "center",
    alignItems: "center",
    paddingHorizontal: scale(20),
    paddingVertical: scale(15),
  },
  imageContainer: {
    position: "relative",
    alignItems: "center",
    justifyContent: "center",
  },
  logoPreview: {
    width: scale(100),
    height: scale(100),
    resizeMode: "contain",
    borderRadius: scale(10),
  },

  newImageBadge: {
    position: "absolute",
    top: -5,
    right: -5,
    borderRadius: scale(8),
    paddingHorizontal: scale(6),
    paddingVertical: scale(2),
  },
  badgeText: {
    color: "white",
  },
  uploadPrompt: {
    alignItems: "center",
    justifyContent: "center",
  },
  text: {
    marginTop: scale(8),
    textAlign: "center",
  },
  changePrompt: {
    marginTop: scale(8),
  },
  changeText: {
    textAlign: "center",
    opacity: 0.7,
  },
});
