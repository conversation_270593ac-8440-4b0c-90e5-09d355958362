import React, { useState } from "react";
import {
  Pressable,
  TextInput as RNTextInput,
  StyleSheet,
  View,
} from "react-native";

import { scale, scaleFont } from "@/src/_helper/Scaler";
import { Colors } from "@/src/constants/Colors";
import { useLanguage } from "@/src/context/LanguageContext";
import { useTheme } from "@/src/context/ThemeContext";
import { Ionicons } from "@expo/vector-icons";
import ErrorText from "../shared/ErrorText";

interface TextInputProps extends React.ComponentProps<typeof RNTextInput> {
  error?: any;
  isPassword?: boolean;
  translationKey?: string;
  multiline?: boolean;
}

const TextInput: React.FC<TextInputProps> = ({
  isPassword,
  error,
  value,
  placeholder,
  onChangeText,
  editable = true,
  translationKey,
  multiline = false,

  ...props
}) => {
  const { currentTheme } = useTheme();
  const { t } = useLanguage();
  const [hidePassword, setHidePassword] = useState(true);
  const [isFocused, setIsFocused] = useState(false);
  const [isTouched, setIsTouched] = useState(false);

  const handleFocus = () => {
    setIsFocused(true);
    setIsTouched(true);
  };

  const handleBlur = () => {
    setIsFocused(false);
    setIsTouched(false);
  };

  const inputBorderColor = () => {
    if (isFocused || (isTouched && !error)) {
      return [
        styles.borderFocused,
        {
          borderColor: Colors[currentTheme ?? "dark"].primary,
        },
      ];
    }
    return [
      styles.borderDefault,
      {
        borderColor: Colors[currentTheme ?? "dark"].input,
        backgroundColor: Colors[currentTheme ?? "dark"].input,
      },
    ];
  };

  return (
    <View style={styles.container}>
      <RNTextInput
        multiline={multiline}
        numberOfLines={multiline ? 4 : 1} // optional: default height lines for multiline
        textAlignVertical={multiline ? "top" : "auto"} // align text at top when multiline
        style={[
          styles.textInput,
          multiline && styles.textArea, // add new style for multiline
          {
            backgroundColor: Colors[currentTheme ?? "dark"].input,
            color: Colors[currentTheme ?? "dark"].input_text,
            fontFamily: "Confortaa",
          },
          inputBorderColor(),
        ]}
        placeholder={translationKey ? t(translationKey) : placeholder}
        placeholderTextColor={Colors[currentTheme ?? "dark"].input_text}
        secureTextEntry={isPassword && hidePassword}
        value={value}
        onChangeText={(text) => {
          onChangeText && onChangeText(text);
          setIsTouched(true);
        }}
        onFocus={handleFocus}
        onBlur={handleBlur}
        editable={editable}
        {...props}
      />
      {error && <ErrorText error={error} />}
      {isPassword && (
        <Pressable
          onPress={() => setHidePassword(!hidePassword)}
          style={styles.passwordToggle}
        >
          {hidePassword ? (
            <Ionicons name="eye-off" size={scaleFont(16)} />
          ) : (
            <Ionicons name="eye" size={scaleFont(16)} />
          )}
        </Pressable>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: scale(10),
  },
  textInput: {
    paddingVertical: scale(8),
    paddingHorizontal: scale(15),
    borderRadius: scale(10),
    marginTop: scale(8),
    fontSize: scaleFont(12),
    height: scale(58),
  },

  borderDefault: {
    borderWidth: scale(1),
  },
  borderFocused: {
    borderWidth: scale(1),
  },

  passwordToggle: {
    position: "absolute",
    height: scale(58),
    top: scale(8),
    right: scale(16),
    zIndex: 10, // zIndex should be an integer
    justifyContent: "center",
    alignItems: "center",
  },
  label: {
    position: "absolute",
    borderRadius: scale(50),
    top: scale(4),
    left: scale(20),
    justifyContent: "center",
    alignItems: "center",
    zIndex: 999, // zIndex should be an integer
  },
  textArea: {
    height: scale(110), // taller height for description box
    paddingTop: scale(10), // optional: padding at top for nicer typing
  },
});

export default TextInput;
