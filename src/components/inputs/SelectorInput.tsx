import { scale } from "@/src/_helper/Scaler";
import { ThemedText } from "@/src/components/ui/ThemedText";
import { Colors } from "@/src/constants/Colors";
import { useTheme } from "@/src/context/ThemeContext";
import React from "react";
import { Keyboard, StyleSheet, TouchableOpacity, View } from "react-native";
import ErrorText from "../shared/ErrorText";

interface CategoryInputProps {
  selectedCategory: string;
  onPress: () => void;
  error?: string | undefined | false;
  placeholder?: string;
}

export default function CategoryInput({
  selectedCategory,
  onPress,
  error,
  placeholder = "Select a category *",
}: CategoryInputProps) {
  const { currentTheme } = useTheme();

  const handlePress = () => {
    Keyboard.dismiss();
    onPress();
  };

  return (
    <View style={styles.container}>
      <TouchableOpacity
        style={[
          styles.selector,
          {
            backgroundColor: Colors[currentTheme ?? "dark"].input,
            borderColor: error
              ? Colors[currentTheme ?? "dark"].error
              : Colors[currentTheme ?? "dark"].border,
          },
        ]}
        onPress={handlePress}
      >
        <ThemedText
          style={{
            color: Colors[currentTheme ?? "dark"].input_text,
          }}
          size={12}
        >
          {selectedCategory || placeholder}
        </ThemedText>
      </TouchableOpacity>

      {error && <ErrorText error={error} />}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    marginVertical: 8,
  },
  selector: {
    paddingVertical: scale(8),
    paddingHorizontal: scale(15),
    borderRadius: scale(10),
    marginTop: scale(8),
    height: scale(58),
    justifyContent: "center",
  },
  errorText: {
    marginTop: scale(4),
    marginLeft: scale(4),
  },
});
