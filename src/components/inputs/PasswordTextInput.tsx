import { scale, scaleFont } from "@/src/_helper/Scaler";
import { Colors } from "@/src/constants/Colors";
import { useLanguage } from "@/src/context/LanguageContext";
import { useTheme } from "@/src/context/ThemeContext";
import { Ionicons } from "@expo/vector-icons";
import React, { useState } from "react";
import {
  Pressable,
  TextInput as RNTextInput,
  StyleSheet,
  View,
} from "react-native";
import ErrorText from "../shared/ErrorText";
import { ThemedText } from "../ui/ThemedText";
import { ThemedView } from "../ui/ThemedView";

interface TextInputProps {
  error?: any;
  value?: string;
  onChangeText?: (text: string) => void;
  placeholder: string;
  editable?: boolean;
  isPassword?: boolean;
  translationKey?: string;
}

const PasswordTextInput: React.FC<TextInputProps> = ({
  isPassword,
  error,
  value,
  placeholder,
  onChangeText,
  editable = true,
  translationKey,
}) => {
  const { currentTheme } = useTheme();
  const { t } = useLanguage();
  const [hidePassword, setHidePassword] = useState(true);
  const [isFocused, setIsFocused] = useState(false);
  const [isTouched, setIsTouched] = useState(false);
  const [passwordStrength, setPasswordStrength] = useState(0);

  const handleFocus = () => {
    setIsFocused(true);
    setIsTouched(true);
  };

  const handleBlur = () => {
    setIsFocused(false);
    setIsTouched(false);
  };

  const calculatePasswordStrength = (password: string) => {
    let strength = 0;
    if (password.length >= 8) strength++;
    if (/[A-Z]/.test(password)) strength++;
    if (/[0-9]/.test(password)) strength++;
    if (/[^A-Za-z0-9]/.test(password)) strength++;
    return strength;
  };

  const handlePasswordChange = (text: string) => {
    onChangeText && onChangeText(text);
    setIsTouched(true);
    setPasswordStrength(calculatePasswordStrength(text));
  };

  const getPasswordStrengthLabel = () => {
    switch (passwordStrength) {
      case 1:
        return (
          <ThemedText
            size={12}
            type="bold"
            style={{
              color: Colors[currentTheme ?? "dark"].error,
            }}
          >
            {t("validation.password.strength.weak")}
          </ThemedText>
        );
      case 2:
        return (
          <ThemedText
            size={12}
            type="bold"
            style={{
              color: Colors[currentTheme ?? "dark"].orange,
            }}
          >
            {t("validation.password.strength.fair")}
          </ThemedText>
        );
      case 3:
        return (
          <ThemedText
            size={12}
            type="bold"
            style={{
              color: Colors[currentTheme ?? "dark"].yellow,
            }}
          >
            {t("validation.password.strength.good")}
          </ThemedText>
        );
      case 4:
        return (
          <ThemedText
            size={12}
            type="bold"
            style={{
              color: Colors[currentTheme ?? "dark"].green,
            }}
          >
            {t("validation.password.strength.strong")}
          </ThemedText>
        );
      default:
        return "";
    }
  };

  const getStrengthColor = () => {
    switch (passwordStrength) {
      case 1:
        return Colors[currentTheme ?? "dark"].secondary;
      case 2:
        return Colors[currentTheme ?? "dark"].secondary;
      case 3:
        return Colors[currentTheme ?? "dark"].secondary;
      case 4:
        return Colors[currentTheme ?? "dark"].secondary;
      default:
        return Colors[currentTheme ?? "dark"].background;
    }
  };

  const inputBorderColor = () => {
    if (isFocused || (isTouched && !error)) {
      return [
        styles.borderFocused,
        {
          borderColor: Colors[currentTheme ?? "dark"].primary,
        },
      ];
    }
    return [
      styles.borderDefault,
      {
        borderColor: Colors[currentTheme ?? "dark"].input,
        backgroundColor: Colors[currentTheme ?? "dark"].input,
      },
    ];
  };

  return (
    <ThemedView style={styles.container}>
      <RNTextInput
        style={[
          styles.textInput,
          {
            backgroundColor: Colors[currentTheme ?? "dark"].input,
            color: Colors[currentTheme ?? "dark"].input_text,
            fontFamily: "Confortaa",
          },
          inputBorderColor(),
        ]}
        placeholder={translationKey ? t(translationKey) : placeholder}
        placeholderTextColor={Colors[currentTheme ?? "dark"].input_text}
        secureTextEntry={isPassword && hidePassword}
        value={value}
        onChangeText={handlePasswordChange}
        onFocus={handleFocus}
        onBlur={handleBlur}
        editable={editable}
      />
      {error && <ErrorText error={error} />}
      {isPassword && (
        <Pressable
          onPress={() => setHidePassword(!hidePassword)}
          style={styles.passwordToggle}
        >
          {hidePassword ? (
            <Ionicons name="eye-off" size={scaleFont(16)} />
          ) : (
            <Ionicons name="eye" size={scaleFont(16)} />
          )}
        </Pressable>
      )}
      {isPassword && value && (
        <View style={styles.strengthIndicatorContainer}>
          <View
            style={[
              styles.strengthBar,
              {
                backgroundColor:
                  passwordStrength >= 1
                    ? getStrengthColor()
                    : Colors[currentTheme ?? "dark"].input,
              },
            ]}
          />
          <View
            style={[
              styles.strengthBar,
              {
                backgroundColor:
                  passwordStrength >= 2
                    ? getStrengthColor()
                    : Colors[currentTheme ?? "dark"].input,
              },
            ]}
          />
          <View
            style={[
              styles.strengthBar,
              {
                backgroundColor:
                  passwordStrength >= 3
                    ? getStrengthColor()
                    : Colors[currentTheme ?? "dark"].input,
              },
            ]}
          />
          <View
            style={[
              styles.strengthBar,
              {
                backgroundColor:
                  passwordStrength >= 4
                    ? getStrengthColor()
                    : Colors[currentTheme ?? "dark"].input,
              },
            ]}
          />
        </View>
      )}
      {isPassword && value && (
        <ThemedText style={styles.strengthText} size={12}>
          {t("validation.password.strength.label")}
          {getPasswordStrengthLabel()}
        </ThemedText>
      )}
    </ThemedView>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: scale(10),
  },
  textInput: {
    paddingVertical: scale(8),
    paddingHorizontal: scale(15),
    borderRadius: scale(10),
    marginTop: scale(8),
    fontSize: scaleFont(12),
    height: scale(58),
  },
  borderDefault: {
    borderWidth: scale(1),
  },
  borderFocused: {
    borderWidth: scale(1),
  },

  errorContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingHorizontal: scale(3),
  },

  passwordToggle: {
    position: "absolute",
    height: scale(58),
    top: scale(8),
    right: scale(16),
    zIndex: 10, // zIndex should be an integer
    justifyContent: "center",
    alignItems: "center",
  },
  label: {
    position: "absolute",
    borderRadius: scale(50),
    top: scale(4),
    left: scale(20),
    justifyContent: "center",
    alignItems: "center",
    zIndex: 999, // zIndex should be an integer
  },
  strengthIndicatorContainer: {
    flexDirection: "row",
    marginTop: scale(20),
  },
  strengthBar: {
    flex: 1,
    height: scale(5),
    marginHorizontal: scale(2),
    borderRadius: scale(4),
  },
  strengthText: {
    marginTop: scale(20),
  },
});

export default PasswordTextInput;
