import React, { useEffect, useRef, useState } from "react";
import {
  Animated,
  Pressable,
  TextInput as RNTextInput,
  StyleSheet,
  View,
} from "react-native";

import { scale, scaleFont } from "@/src/_helper/Scaler";
import { Colors } from "@/src/constants/Colors";
import { useLanguage } from "@/src/context/LanguageContext";
import { useTheme } from "@/src/context/ThemeContext";
import { Ionicons } from "@expo/vector-icons";
import ErrorText from "../shared/ErrorText";

interface AnimatedTextInputProps
  extends React.ComponentProps<typeof RNTextInput> {
  error?: any;
  isPassword?: boolean;
  translationKey?: string;
  multiline?: boolean;
  animatedWords?: string[];
  staticPrefix?: string;
}

const AnimatedTextInput: React.FC<AnimatedTextInputProps> = ({
  isPassword,
  error,
  value,
  onChangeText,
  editable = true,
  translationKey,
  multiline = false,
  animatedWords,
  staticPrefix,
  ...props
}) => {
  const { currentTheme } = useTheme();
  const { locale } = useLanguage();
  const [hidePassword, setHidePassword] = useState(true);
  const [isFocused, setIsFocused] = useState(false);
  const [isTouched, setIsTouched] = useState(false);

  // Animation states
  const [currentWordIndex, setCurrentWordIndex] = useState(0);
  const fadeAnim = useRef(new Animated.Value(1)).current;
  const translateYAnim = useRef(new Animated.Value(0)).current;

  // Default words and prefix based on language
  const defaultWords =
    locale === "en" ? ["product", "brand"] : ["produit", "marque"];
  const defaultPrefix = locale === "en" ? "Search for " : "Rechercher ";

  // Use props or defaults
  const words = animatedWords || defaultWords;
  const prefix = staticPrefix || defaultPrefix;

  const handleFocus = () => {
    setIsFocused(true);
    setIsTouched(true);
  };

  const handleBlur = () => {
    setIsFocused(false);
    setIsTouched(false);
  };

  // Animation effect for cycling placeholders
  useEffect(() => {
    if (!value || value.length === 0) {
      const interval = setInterval(() => {
        // Fade out and slide up
        Animated.parallel([
          Animated.timing(fadeAnim, {
            toValue: 0,
            duration: 300,
            useNativeDriver: true,
          }),
          Animated.timing(translateYAnim, {
            toValue: -10,
            duration: 300,
            useNativeDriver: true,
          }),
        ]).start(() => {
          // Change word index
          setCurrentWordIndex((prevIndex) => (prevIndex + 1) % words.length);

          // Reset position and fade in
          translateYAnim.setValue(10);
          Animated.parallel([
            Animated.timing(fadeAnim, {
              toValue: 1,
              duration: 300,
              useNativeDriver: true,
            }),
            Animated.timing(translateYAnim, {
              toValue: 0,
              duration: 300,
              useNativeDriver: true,
            }),
          ]).start();
        });
      }, 2500); // Change every 2.5 seconds

      return () => clearInterval(interval);
    }
  }, [value, fadeAnim, translateYAnim, words.length]);

  const inputBorderColor = () => {
    if (isFocused || (isTouched && !error)) {
      return [
        styles.borderFocused,
        {
          borderColor: Colors[currentTheme ?? "dark"].primary,
        },
      ];
    }
    return [
      styles.borderDefault,
      {
        borderColor: Colors[currentTheme ?? "dark"].input,
        backgroundColor: Colors[currentTheme ?? "dark"].input,
      },
    ];
  };

  return (
    <View style={styles.container}>
      <View style={styles.inputContainer}>
        <RNTextInput
          multiline={multiline}
          numberOfLines={multiline ? 4 : 1}
          textAlignVertical={multiline ? "top" : "auto"}
          style={[
            styles.textInput,
            multiline && styles.textArea,
            {
              backgroundColor: Colors[currentTheme ?? "dark"].input,
              color: Colors[currentTheme ?? "dark"].input_text,
              fontFamily: "Confortaa",
            },
            inputBorderColor(),
          ]}
          placeholder="" // We'll handle placeholder with animated text
          placeholderTextColor={Colors[currentTheme ?? "dark"].input_text}
          secureTextEntry={isPassword && hidePassword}
          value={value}
          onChangeText={(text) => {
            onChangeText && onChangeText(text);
            setIsTouched(true);
          }}
          onFocus={handleFocus}
          onBlur={handleBlur}
          editable={editable}
          {...props}
        />

        {/* Animated Placeholder */}
        {(!value || value.length === 0) && !isFocused && (
          <View style={styles.placeholderContainer} pointerEvents="none">
            <Animated.Text
              style={[
                styles.staticText,
                {
                  color: Colors[currentTheme ?? "dark"].input_text,
                },
              ]}
            >
              {prefix}
            </Animated.Text>
            <Animated.Text
              style={[
                styles.animatedWord,
                {
                  color: Colors[currentTheme ?? "dark"].input_text,
                  opacity: fadeAnim,
                  transform: [{ translateY: translateYAnim }],
                },
              ]}
            >
              {words[currentWordIndex]}
            </Animated.Text>
          </View>
        )}
      </View>

      {error && <ErrorText error={error} />}
      {isPassword && (
        <Pressable
          onPress={() => setHidePassword(!hidePassword)}
          style={styles.passwordToggle}
        >
          {hidePassword ? (
            <Ionicons name="eye-off" size={scaleFont(16)} />
          ) : (
            <Ionicons name="eye" size={scaleFont(16)} />
          )}
        </Pressable>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: scale(10),
  },
  inputContainer: {
    position: "relative",
  },
  textInput: {
    paddingVertical: scale(8),
    paddingHorizontal: scale(15),
    borderRadius: scale(10),
    marginTop: scale(8),
    fontSize: scaleFont(12),
    height: scale(58),
  },
  placeholderContainer: {
    position: "absolute",
    left: scale(15),
    top: scale(8) + scale(58) / 2 - scaleFont(12) / 2,
    flexDirection: "row",
    alignItems: "center",
    pointerEvents: "none",
  },
  borderDefault: {
    borderWidth: scale(1),
  },
  borderFocused: {
    borderWidth: scale(1),
  },
  passwordToggle: {
    position: "absolute",
    height: scale(58),
    top: scale(8),
    right: scale(16),
    zIndex: 10, // zIndex should be an integer
    justifyContent: "center",
    alignItems: "center",
  },
  label: {
    position: "absolute",
    borderRadius: scale(50),
    top: scale(4),
    left: scale(20),
    justifyContent: "center",
    alignItems: "center",
    zIndex: 999, // zIndex should be an integer
  },
  textArea: {
    height: scale(110),
    paddingTop: scale(10),
  },
  staticText: {
    fontSize: scaleFont(12),
    fontFamily: "Confortaa",
  },
  animatedWord: {
    fontSize: scaleFont(12),
    fontFamily: "Confortaa",
  },
});

export default AnimatedTextInput;
