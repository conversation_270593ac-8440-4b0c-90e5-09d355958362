import { scale } from "@/src/_helper/Scaler";
import { Colors } from "@/src/constants/Colors";
import { useTheme } from "@/src/context/ThemeContext";
import React, { useEffect, useRef } from "react";
import {
  NativeSyntheticEvent,
  StyleSheet,
  TextInput,
  TextInputKeyPressEventData,
  View,
} from "react-native";

interface OTPInputProps {
  otpLength?: number;
  onOtpChange?: (otp: string) => void;
  editable: boolean;
  otpValue: string;
}

const OTPInput: React.FC<OTPInputProps> = ({
  otpLength = 4,
  onOtpChange,
  editable,
  otpValue,
}) => {
  const { currentTheme } = useTheme();
  const inputRefs = useRef<TextInput[]>([]);

  useEffect(() => {
    // Autofocus first input on mount
    if (editable) {
      inputRefs.current[0]?.focus();
    }
  }, [editable]);

  const handleChange = (index: number, value: string) => {
    if (!onOtpChange || !editable) return;

    if (value.length > 1) {
      // Handle paste
      const newValues = value.slice(0, otpLength).split("");
      const filled = Array(otpLength)
        .fill("")
        .map((_, i) => newValues[i] || "");
      onOtpChange(filled.join(""));
      inputRefs.current[Math.min(newValues.length, otpLength - 1)]?.focus();
    } else {
      const otpArray = otpValue.split("");
      otpArray[index] = value;
      const updated = otpArray.join("");
      onOtpChange(updated);
      if (value && index < otpLength - 1) {
        inputRefs.current[index + 1]?.focus();
      }
    }
  };

  const handleKeyPress = (
    index: number,
    e: NativeSyntheticEvent<TextInputKeyPressEventData>
  ) => {
    if (!onOtpChange || !editable) return;
    const key = e.nativeEvent.key;

    if (key === "Backspace") {
      if (!otpValue[index] && index > 0) {
        const otpArray = otpValue.split("");
        otpArray[index - 1] = "";
        onOtpChange(otpArray.join(""));
        inputRefs.current[index - 1]?.focus();
      } else {
        const otpArray = otpValue.split("");
        otpArray[index] = "";
        onOtpChange(otpArray.join(""));
      }
    }
  };

  return (
    <View style={styles.otpContainer}>
      {Array.from({ length: otpLength }).map((_, index) => (
        <TextInput
          key={index}
          ref={(ref) => {
            if (ref) inputRefs.current[index] = ref;
          }}
          style={[
            styles.otpInput,
            {
              borderColor: Colors[currentTheme ?? "dark"].input,
              backgroundColor: Colors[currentTheme ?? "dark"].input,
              color: Colors[currentTheme ?? "dark"].input_text,
            },
          ]}
          maxLength={1}
          keyboardType="number-pad"
          onChangeText={(value) => handleChange(index, value)}
          onKeyPress={(e) => handleKeyPress(index, e)}
          value={otpValue[index] || ""}
          editable={editable}
          autoFocus={index === 0}
        />
      ))}
    </View>
  );
};

const styles = StyleSheet.create({
  otpContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginVertical: scale(20),
    paddingHorizontal: scale(20),
  },
  otpInput: {
    width: scale(55),
    height: scale(55),
    borderWidth: 1.5,
    fontSize: scale(12),
    borderRadius: scale(12),
    textAlign: "center",
  },
});

export default OTPInput;
