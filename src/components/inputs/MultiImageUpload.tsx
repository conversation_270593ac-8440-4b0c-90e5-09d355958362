import { scale } from "@/src/_helper/Scaler";
import { ThemedText } from "@/src/components/ui/ThemedText";
import { Colors } from "@/src/constants/Colors";
import { useTheme } from "@/src/context/ThemeContext";
import { Ionicons } from "@expo/vector-icons";
import React from "react";
import {
  Image,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  View,
} from "react-native";
import ErrorText from "../shared/ErrorText";

interface MultiImageUploaderProps {
  images: string[]; // URIs of selected images
  onPickImages: () => void;
  onRemoveImage: (uri: string) => void;
  error: string | false | undefined;
}
export default function MultiImageUploader({
  images,
  onPickImages,
  onRemoveImage,
  error,
}: MultiImageUploaderProps) {
  const { currentTheme } = useTheme();
  const theme = currentTheme ?? "dark";

  return (
    <View style={styles.wrapper}>
      <TouchableOpacity
        style={[
          styles.uploadBox,
          {
            borderColor: Colors[theme].border,
            backgroundColor: Colors[theme].input,
          },
        ]}
        onPress={onPickImages}
        activeOpacity={0.7}
      >
        <Ionicons
          name="cloud-upload-outline"
          size={40}
          color={Colors[theme].input_text}
        />
        <ThemedText
          size={14}
          style={[
            styles.uploadText,
            {
              color: Colors[theme].input_text,
            },
          ]}
        >
          Tap to add images
        </ThemedText>
      </TouchableOpacity>

      {images.length > 0 && (
        <ScrollView
          horizontal
          style={styles.scrollContainer}
          showsHorizontalScrollIndicator={false}
        >
          {images.map((uri) => (
            <View key={uri} style={styles.imageWrapper}>
              <Image source={{ uri }} style={styles.image} />
              <TouchableOpacity
                style={styles.removeBtn}
                onPress={() => onRemoveImage(uri)}
              >
                <Ionicons name="close-circle" size={20} color="white" />
              </TouchableOpacity>
            </View>
          ))}
        </ScrollView>
      )}

      {error ? <ErrorText error={error} /> : null}
    </View>
  );
}

const styles = StyleSheet.create({
  wrapper: {
    marginVertical: scale(20),
  },
  uploadBox: {
    minHeight: scale(120),
    borderWidth: scale(2),
    borderStyle: "dashed",
    borderRadius: scale(12),
    justifyContent: "center",
    alignItems: "center",
    paddingVertical: scale(20),
    marginBottom: scale(15),
  },
  uploadText: {
    marginTop: scale(8),
    textAlign: "center",
  },
  scrollContainer: {
    flexGrow: 0,
  },
  imageWrapper: {
    position: "relative",
    marginRight: scale(10),
  },
  image: {
    width: scale(100),
    height: scale(100),
    borderRadius: scale(12),
    resizeMode: "cover",
  },
  removeBtn: {
    position: "absolute",
    top: -5,
    right: -5,
    borderRadius: scale(10),
    padding: scale(2),
  },
});
