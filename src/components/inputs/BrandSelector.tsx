import { scale } from "@/src/_helper/Scaler";
import { ThemedText } from "@/src/components/ui/ThemedText";
import { Colors } from "@/src/constants/Colors";
import { useTheme } from "@/src/context/ThemeContext";
import React from "react";
import { Keyboard, StyleSheet, TouchableOpacity, View } from "react-native";
import ErrorText from "../shared/ErrorText";

interface BrandInputProps {
  selectedBrandName: string;
  onPress: () => void;
  error?: string | undefined | false;
  placeholder?: string;
}

export default function BrandInput({
  selectedBrandName,
  onPress,
  error,
  placeholder = "Select a brand *",
}: BrandInputProps) {
  const { currentTheme } = useTheme();

  const handlePress = () => {
    Keyboard.dismiss();
    onPress();
  };

  return (
    <View style={styles.container}>
      <TouchableOpacity
        style={[
          styles.selector,
          {
            backgroundColor: Colors[currentTheme ?? "dark"].input,
            borderColor: Colors[currentTheme ?? "dark"].border,
          },
        ]}
        onPress={handlePress}
      >
        <ThemedText
          style={{
            color: Colors[currentTheme ?? "dark"].input_text,
          }}
          size={12}
        >
          {selectedBrandName || placeholder}
        </ThemedText>
      </TouchableOpacity>

      {error && <ErrorText error={error} />}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    marginVertical: scale(8),
  },
  selector: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingVertical: scale(8),
    paddingHorizontal: scale(15),
    borderRadius: scale(10),
    marginTop: scale(8),
    height: scale(58),
  },
});
