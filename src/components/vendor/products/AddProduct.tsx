import { scale } from "@/src/_helper/Scaler";
import { addProductSchema } from "@/src/_helper/validator/VendorValidator";
import BrandBottomSheet from "@/src/components/bottom-sheet/BrandsBottomSheet";
import CategoryBottomSheet from "@/src/components/bottom-sheet/CategorySelectorBottomSheet";
import GalleryPermission from "@/src/components/bottom-sheet/GalleryPermission";
import DefaultButton from "@/src/components/buttons/Default";
import { MainContainerForScreensWithInput } from "@/src/components/containers/MainContainerForScreensWithInput";
import BrandInput from "@/src/components/inputs/BrandSelector";
import MultiImageUploader from "@/src/components/inputs/MultiImageUpload";
import CategoryInput from "@/src/components/inputs/SelectorInput";
import TextInput from "@/src/components/inputs/TextInput";
import { useLanguage } from "@/src/context/LanguageContext";
import { useImagePicker } from "@/src/hooks/useImagePicker";
import useAddProductMutation from "@/src/services/querys/vendor/useAddProductMutation";
import { Formik } from "formik";
import React, { useEffect, useRef, useState } from "react";
import { StyleSheet, View } from "react-native";
import { GestureHandlerRootView } from "react-native-gesture-handler";

import { ProductFormValues } from "@/src/types";

export default function AddProduct() {
  const { t } = useLanguage();
  const { imageUris, pickImages, removeImage, showPermissionUI, closeUI } =
    useImagePicker();
  const mutation = useAddProductMutation();
  const [showCategorySheet, setShowCategorySheet] = useState(false);
  const [showBrandSheet, setShowBrandSheet] = useState(false);
  const [selectedBrandName, setSelectedBrandName] = useState("");
  // Add state for category display name
  const [selectedCategoryName, setSelectedCategoryName] = useState("");

  const formikSetFieldValueRef = useRef<
    null | ((field: string, value: any) => void)
  >(null);

  // ✅ Sync imageUris with Formik only when imageUris changes
  useEffect(() => {
    if (formikSetFieldValueRef.current) {
      formikSetFieldValueRef.current("images", imageUris);
    }
  }, [imageUris]);

  const handleSubmit = async (values: ProductFormValues) => {
    await mutation.mutateAsync(values);
  };

  // Updated category select handler to manage both ID and display name
  const handleCategorySelect = (categoryId: string, categoryName: string) => {
    formikSetFieldValueRef.current?.("category", categoryId);
    setSelectedCategoryName(categoryName);
  };

  const handleBrandSelect = (brandId: string, brandName: string) => {
    formikSetFieldValueRef.current?.("brand", brandId);
    setSelectedBrandName(brandName);
  };

  const handlePickImages = async () => {
    try {
      await pickImages(); // useEffect will sync imageUris
    } catch (error) {
      console.error("Error picking images:", error);
    }
  };

  const handleRemoveImage = (uri: string) => {
    removeImage(uri); // useEffect will sync imageUris after removal
  };

  const handleNumericInput = (
    field: string,
    text: string,
    setFieldValue: any
  ) => {
    if (text === "" || /^\d*\.?\d*$/.test(text)) {
      setFieldValue(field, text);
    }
  };

  return (
    <GestureHandlerRootView>
      <MainContainerForScreensWithInput>
        <Formik<ProductFormValues>
          initialValues={{
            name: "",
            description: "",
            category: "",
            brand: "",
            price: "",
            images: [],
          }}
          validationSchema={addProductSchema}
          onSubmit={handleSubmit}
        >
          {(formikProps) => {
            const {
              values,
              errors,
              touched,
              handleChange,
              handleSubmit,
              setFieldValue,
            } = formikProps;

            // ✅ Only set once
            if (!formikSetFieldValueRef.current) {
              formikSetFieldValueRef.current = setFieldValue;
            }

            return (
              <View style={styles.form}>
                <TextInput
                  value={values.name}
                  onChangeText={handleChange("name")}
                  placeholder={t("vendor.productForm.placeholders.productName")}
                  error={touched.name && errors.name}
                />
                <TextInput
                  value={values.description}
                  onChangeText={handleChange("description")}
                  placeholder={t(
                    "vendor.productForm.placeholders.productDescription"
                  )}
                  multiline
                  error={touched.description && errors.description}
                />

                {/* Updated CategoryInput to use display name instead of ID */}
                <CategoryInput
                  selectedCategory={selectedCategoryName}
                  onPress={() => setShowCategorySheet(true)}
                  error={touched.category && errors.category}
                  placeholder={t(
                    "vendor.productForm.placeholders.selectCategory"
                  )}
                />

                <BrandInput
                  selectedBrandName={selectedBrandName}
                  onPress={() => setShowBrandSheet(true)}
                  error={touched.brand && errors.brand}
                />

                <TextInput
                  value={values.price}
                  onChangeText={(text) =>
                    handleNumericInput("price", text, setFieldValue)
                  }
                  placeholder={t("vendor.productForm.placeholders.price")}
                  keyboardType="numeric"
                  error={touched.price && errors.price}
                />

                <MultiImageUploader
                  images={imageUris}
                  onPickImages={handlePickImages}
                  onRemoveImage={handleRemoveImage}
                  error={
                    touched.images && typeof errors.images === "string"
                      ? errors.images
                      : undefined
                  }
                />

                <DefaultButton
                  title={t("buttons.saveProduct")}
                  onPress={handleSubmit}
                  style={{ marginTop: 20 }}
                  isSubmitting={mutation.isPending}
                  disabled={mutation.isPending}
                />
              </View>
            );
          }}
        </Formik>
      </MainContainerForScreensWithInput>

      {/* Bottom Sheets */}
      <GalleryPermission isVisible={showPermissionUI} onClose={closeUI} />
      <CategoryBottomSheet
        isVisible={showCategorySheet}
        onClose={() => setShowCategorySheet(false)}
        onCategorySelect={handleCategorySelect} // Updated to pass both ID and name
      />
      <BrandBottomSheet
        isVisible={showBrandSheet}
        onClose={() => setShowBrandSheet(false)}
        onBrandSelect={handleBrandSelect}
      />
    </GestureHandlerRootView>
  );
}

const styles = StyleSheet.create({
  form: {
    flex: 1,
    gap: scale(12),
  },
});
