import { scale } from "@/src/_helper/Scaler";
import { ThemedText } from "@/src/components/ui/ThemedText";
import { Colors } from "@/src/constants/Colors";
import { useTheme } from "@/src/context/ThemeContext";
import { MaterialIcons } from "@expo/vector-icons";
import React from "react";
import { Image, StyleSheet, TouchableOpacity, View } from "react-native";

interface ProductItemProps {
  item: {
    id: string;
    name: string;
    brand: string;
    image: { uri: string };
    backgroundColor?: string;
  };
  onPress: (id: string) => void;
}

const ProductItem = ({ item, onPress }: ProductItemProps) => {
  const { currentTheme } = useTheme();

  return (
    <TouchableOpacity
      style={styles.productCard}
      onPress={() => onPress(item.id)}
      activeOpacity={0.7}
    >
      <View style={styles.productContent}>
        <View
          style={[
            styles.productImageContainer,
            { backgroundColor: item.backgroundColor },
          ]}
        >
          <Image source={item.image} style={styles.productImage} />
        </View>
        <View style={styles.productInfo}>
          <ThemedText type="semi-bold" size={16}>
            {item.name}
          </ThemedText>
          <ThemedText
            size={14}
            style={{
              color: Colors[currentTheme ?? "dark"].secondary,
              marginTop: 2,
            }}
          >
            {item.brand}
          </ThemedText>
        </View>
      </View>
      <MaterialIcons
        name="chevron-right"
        size={24}
        color={Colors[currentTheme ?? "dark"].secondary}
      />
    </TouchableOpacity>
  );
};

export default ProductItem;

const styles = StyleSheet.create({
  productCard: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    borderRadius: scale(16),
    paddingVertical: scale(16),
  },
  productContent: {
    flexDirection: "row",
    alignItems: "center",
    flex: 1,
  },
  productImageContainer: {
    width: scale(50),
    height: scale(50),
    borderRadius: scale(12),
    marginRight: scale(16),
    justifyContent: "center",
    alignItems: "center",
  },
  productImage: {
    width: scale(48),
    height: scale(48),
    borderRadius: scale(8),
  },
  productInfo: {
    flex: 1,
  },
});
