import { scale } from "@/src/_helper/Scaler";
import { addBrandSchema } from "@/src/_helper/validator/VendorValidator";
import CategoryBottomSheet from "@/src/components/bottom-sheet/CategorySelectorBottomSheet";
import GalleryPermission from "@/src/components/bottom-sheet/GalleryPermission";
import LocationPermission from "@/src/components/bottom-sheet/LocationPermission";
import DefaultButton from "@/src/components/buttons/Default";
import { MainContainerForScreensWithInput } from "@/src/components/containers/MainContainerForScreensWithInput";
import LogoUploader from "@/src/components/inputs/LogoUplaoder";
import CategoryInput from "@/src/components/inputs/SelectorInput"; // Renamed from CategorySelector for clarity
import TextInput from "@/src/components/inputs/TextInput";
import ErrorText from "@/src/components/shared/ErrorText";
import { ThemedText } from "@/src/components/ui/ThemedText";
import { Colors } from "@/src/constants/Colors";
import { useLanguage } from "@/src/context/LanguageContext";
import { useTheme } from "@/src/context/ThemeContext";
import { useImagePicker } from "@/src/hooks/useImagePicker";
import { useLocation } from "@/src/hooks/useLocation";
import useAddBrandMutation from "@/src/services/querys/vendor/useAddBrandMutation";
import { Formik } from "formik";
import React, { useEffect, useRef, useState } from "react";
import { StyleSheet, View } from "react-native";
import { GestureHandlerRootView } from "react-native-gesture-handler";
import MapView, { Marker } from "react-native-maps";

import { BrandFormValues } from "@/src/types";

export default function AddBrand() {
  const [showMap, setShowMap] = useState(false);
  const [showLocationForm, setShowLocationForm] = useState(false);
  const [showCategorySheet, setShowCategorySheet] = useState(false);
  const [selectedCategoryName, setSelectedCategoryName] = useState(""); // Add category name state
  const mutation = useAddBrandMutation();

  // Use useRef to store the setFieldValue function
  const formikSetFieldValueRef = useRef<
    null | ((field: string, value: any) => void)
  >(null);

  const handleSubmit = (values: BrandFormValues) => {
    const formData = {
      ...values,
      logo: imageUri,
    };

    mutation.mutateAsync(formData);
  };

  // Fixed category select handler
  const handleCategorySelect = (categoryId: string, categoryName: string) => {
    formikSetFieldValueRef.current?.("category", categoryId);
    setSelectedCategoryName(categoryName);
  };

  const { imageUri, pickImage, showPermissionUI, closeUI } = useImagePicker();
  const {
    selectedLocation,
    mapRegion,
    isLoading: locationLoading,
    showPermissionUI: showLocationPermissionUI,
    hasLocationPermission,
    getCurrentLocation,
    selectLocationFromMap,
    clearLocation,
    closePermissionUI: closeLocationPermissionUI,
    setSelectedLocation,
  } = useLocation();

  const handleMapPress = (event: any) => {
    const { latitude, longitude } = event.nativeEvent.coordinate;
    selectLocationFromMap(latitude, longitude);
    setShowLocationForm(true);
  };

  return (
    <GestureHandlerRootView>
      <MainContainerForScreensWithInput>
        <Formik<BrandFormValues>
          initialValues={{
            name: "",
            description: "",
            logo: "",
            email: "",
            phone: "",
            location: {
              address: "",
              latitude: undefined,
              longitude: undefined,
              city: "",
              state: "",
              country: "",
              zipCode: "",
            },
            category: "",
          }}
          validationSchema={addBrandSchema}
          onSubmit={handleSubmit}
          enableReinitialize
        >
          {({
            handleChange,
            handleSubmit,
            values,
            errors,
            touched,
            isSubmitting,
            setFieldValue,
          }) => {
            // Set the ref for setFieldValue
            if (!formikSetFieldValueRef.current) {
              formikSetFieldValueRef.current = setFieldValue;
            }

            return (
              <FormContent
                values={values}
                errors={errors}
                touched={touched}
                isSubmitting={isSubmitting}
                locationLoading={locationLoading}
                handleChange={handleChange}
                handleSubmit={handleSubmit}
                setFieldValue={setFieldValue}
                showMap={showMap}
                setShowMap={setShowMap}
                showLocationForm={showLocationForm}
                setShowLocationForm={setShowLocationForm}
                showLocationPermissionUI={showLocationPermissionUI}
                hasLocationPermission={hasLocationPermission}
                mapRegion={mapRegion}
                handleMapPress={handleMapPress}
                getCurrentLocation={getCurrentLocation}
                selectedLocation={selectedLocation}
                setSelectedLocation={setSelectedLocation}
                clearLocation={clearLocation}
                imageUri={imageUri}
                pickImage={pickImage}
                mutation={mutation}
                setShowCategorySheet={setShowCategorySheet}
                selectedCategoryName={selectedCategoryName} // Pass category name
              />
            );
          }}
        </Formik>
      </MainContainerForScreensWithInput>

      {/* Gallery Permission Bottom Sheet */}
      <GalleryPermission isVisible={showPermissionUI} onClose={closeUI} />

      {/* Location Permission Bottom Sheet */}
      <LocationPermission
        isVisible={showLocationPermissionUI}
        onClose={closeLocationPermissionUI}
      />

      {/* Category Bottom Sheet */}
      <CategoryBottomSheet
        isVisible={showCategorySheet}
        onClose={() => setShowCategorySheet(false)}
        onCategorySelect={handleCategorySelect}
      />
    </GestureHandlerRootView>
  );
}

function FormContent({
  values,
  errors,
  touched,
  locationLoading,
  handleChange,
  handleSubmit,
  setFieldValue,
  showMap,
  setShowMap,
  showLocationPermissionUI,
  hasLocationPermission,
  mapRegion,
  handleMapPress,
  getCurrentLocation,
  selectedLocation,
  imageUri,
  pickImage,
  mutation,
  setShowCategorySheet,
  selectedCategoryName, // Add this prop
}: any) {
  const { currentTheme } = useTheme();
  const { t } = useLanguage();
  // Auto-fill Formik values when location is selected
  useEffect(() => {
    if (selectedLocation && selectedLocation.address) {
      setFieldValue("location.address", selectedLocation.address);
      setFieldValue("location.city", selectedLocation.city);
      setFieldValue("location.state", selectedLocation.state);
      setFieldValue("location.country", selectedLocation.country);
      setFieldValue("location.zipCode", selectedLocation.zipCode);
      setFieldValue("location.latitude", selectedLocation.latitude);
      setFieldValue("location.longitude", selectedLocation.longitude);
    }
  }, [selectedLocation, setFieldValue]);

  // Update logo field when image is picked
  useEffect(() => {
    if (imageUri) {
      setFieldValue("logo", imageUri);
    }
  }, [imageUri, setFieldValue]);

  const handleCurrentLocation = () => {
    getCurrentLocation();
  };

  return (
    <View style={styles.formContainer}>
      <TextInput
        onChangeText={handleChange("name")}
        value={values.name}
        placeholder="Enter brand name *"
        error={touched.name && errors.name}
      />

      <TextInput
        onChangeText={handleChange("description")}
        value={values.description}
        placeholder="Enter brand description"
        error={touched.description && errors.description}
        multiline
      />

      <TextInput
        onChangeText={handleChange("phone")}
        value={values.phone}
        placeholder="Enter phone number"
        keyboardType="phone-pad"
        error={touched.phone && errors.phone}
      />

      <TextInput
        onChangeText={handleChange("email")}
        value={values.email}
        placeholder="Enter email address"
        keyboardType="email-address"
        autoCapitalize="none"
        error={touched.email && errors.email}
      />

      {/* Fixed CategoryInput to show category name instead of ID */}
      <CategoryInput
        selectedCategory={selectedCategoryName}
        onPress={() => setShowCategorySheet(true)}
        error={touched.category && errors.category}
        placeholder="Select a category *"
      />

      <View style={styles.locationSection}>
        <ThemedText type="bold" size={18}>
          Location Information *
        </ThemedText>

        {hasLocationPermission && !showLocationPermissionUI && (
          <>
            <DefaultButton
              title={showMap ? "Hide Map" : "Select Location on Map"}
              onPress={() => setShowMap(!showMap)}
              style={{
                backgroundColor: Colors[currentTheme ?? "dark"].thirdary,
              }}
              active
            />
            {showMap && (
              <View>
                <View style={styles.mapWrapper}>
                  <MapView
                    style={styles.map}
                    region={mapRegion}
                    onPress={handleMapPress}
                    showsUserLocation
                    showsMyLocationButton
                  >
                    {selectedLocation?.latitude &&
                      selectedLocation?.longitude && (
                        <Marker
                          coordinate={{
                            latitude: selectedLocation.latitude,
                            longitude: selectedLocation.longitude,
                          }}
                          title="Selected Location"
                        />
                      )}
                  </MapView>
                </View>

                <DefaultButton
                  isSubmitting={locationLoading}
                  disabled={locationLoading}
                  onPress={handleCurrentLocation}
                  title="Use Current Location"
                  style={{
                    backgroundColor: Colors[currentTheme ?? "dark"].thirdary,
                  }}
                  active
                />
              </View>
            )}
          </>
        )}

        {!hasLocationPermission && !showLocationPermissionUI && (
          <DefaultButton
            title={t("buttons.enableLocationAccess")}
            onPress={handleCurrentLocation}
          />
        )}

        {touched.location &&
          errors.location &&
          typeof errors.location === "string" && (
            <ErrorText error={errors.location} />
          )}
        {touched.location &&
          errors.location &&
          typeof errors.location === "object" && (
            <ErrorText error={t("validation.location.required")} />
          )}
      </View>

      <LogoUploader
        imageUri={imageUri}
        pickImage={pickImage}
        error={touched.logo && errors.logo}
      />

      <DefaultButton
        title={t("buttons.saveBrand")}
        onPress={handleSubmit}
        isSubmitting={mutation.isPending}
        disabled={mutation.isPending}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  formContainer: {
    flex: 1,
  },
  locationSection: {
    marginVertical: scale(10),
  },
  mapWrapper: {
    height: scale(200),
    borderRadius: scale(12),
    overflow: "hidden",
  },
  map: {
    flex: 1,
  },
});
