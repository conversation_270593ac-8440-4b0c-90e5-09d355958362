import { scale } from "@/src/_helper/Scaler";
import SkeletonBrandList from "@/src/components/loader/SkeletonBrandList";
import { InfiniteScrollList } from "@/src/components/shared/InfiniteScrollList";
import EmptyBrandListUI from "@/src/components/ui/EmptyBrandListUI";
import { ThemedText } from "@/src/components/ui/ThemedText";
import { Colors } from "@/src/constants/Colors";
import { useLanguage } from "@/src/context/LanguageContext";
import { useTheme } from "@/src/context/ThemeContext";
import useGetVendorBrands from "@/src/services/querys/vendor/useGetBrands";
import { IVendorBrand } from "@/src/types";
import { MaterialIcons } from "@expo/vector-icons";
import { router } from "expo-router";
import React, { useMemo, useState } from "react";
import { Image, StyleSheet, Text, TouchableOpacity, View } from "react-native";
import { ErrorComponent } from "../../ui/ErrorComponent";

export default function BrandListSection() {
  const { currentTheme } = useTheme();
  const { t } = useLanguage();
  const {
    data,
    isLoading,
    hasNextPage,
    fetchNextPage,
    isFetchingNextPage,
    refetch,
    error,
  } = useGetVendorBrands();

  const [refreshing, setRefreshing] = useState(false);

  const brands = useMemo(() => {
    return (
      data?.pages.flatMap((page) =>
        page.data.items.map((brand: IVendorBrand) => ({
          id: brand._id,
          name: brand.name,
          productCount: brand.numberOfProducts,
          logo: { uri: brand.logo },
        }))
      ) || []
    );
  }, [data]);
  const handleRefetch = async () => await refetch();
  const handleRefresh = async () => {
    setRefreshing(true);
    try {
      await refetch();
    } finally {
      setRefreshing(false);
    }
  };

  const handleBrandPress = (id: string) => {
    router.push(`/(vendor)/(screens)/brand/${id}/brand-details`);
  };

  const handleEditBrand = (id: string) => {
    router.push(`/(vendor)/(screens)/brand/${id}/edit-brand`);
  };

  const renderBrandItem = ({ item }: any) => (
    <TouchableOpacity
      style={styles.brandCard}
      onPress={() => handleBrandPress(item.id)}
      activeOpacity={0.7}
    >
      <View style={styles.brandContent}>
        <View style={styles.brandIcon}>
          <Image source={item.logo} style={styles.logoImage} />
        </View>
        <View style={styles.brandInfo}>
          <ThemedText type="semi-bold" size={16}>
            {item.name}
          </ThemedText>
          <Text style={{ color: Colors[currentTheme ?? "dark"].secondary }}>
            {item.productCount} {t("vendor.labels.products")}
          </Text>
        </View>
      </View>
      <TouchableOpacity
        style={styles.editButton}
        onPress={() => handleEditBrand(item.id)}
        activeOpacity={0.6}
      >
        <MaterialIcons
          name="mode-edit"
          size={24}
          color={Colors[currentTheme ?? "dark"].secondary}
        />
      </TouchableOpacity>
    </TouchableOpacity>
  );

  if (error) {
    return (
      <ErrorComponent
        error={t("vendor.errors.somethingWentWrong")}
        onRetry={handleRefetch}
      />
    );
  }

  return (
    <View style={styles.section}>
      <ThemedText type="bold" size={18}>
        {t("vendor.labels.yourBrands")}
      </ThemedText>
      <InfiniteScrollList
        data={brands}
        isLoading={isLoading}
        isFetchingNextPage={isFetchingNextPage}
        hasNextPage={hasNextPage}
        fetchNextPage={fetchNextPage}
        refreshing={refreshing}
        onRefresh={handleRefresh}
        estimatedItemSize={300}
        renderItem={renderBrandItem}
        skeletonComponent={<SkeletonBrandList />}
        emptyComponent={<EmptyBrandListUI />}
        count={4}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  section: { flex: 1 },
  brandCard: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    borderRadius: scale(16),
    padding: scale(16),
    marginBottom: scale(16),
  },
  brandContent: {
    flexDirection: "row",
    alignItems: "center",
    flex: 1,
  },
  brandIcon: {
    width: scale(48),
    height: scale(48),
    borderRadius: scale(12),
    justifyContent: "center",
    alignItems: "center",
    marginRight: scale(16),
  },
  logoImage: {
    width: scale(32),
    height: scale(32),
    borderRadius: scale(8),
  },
  brandInfo: {
    flex: 1,
  },
  editButton: {
    padding: scale(8),
  },
});
