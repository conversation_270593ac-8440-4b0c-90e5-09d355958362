import { scale } from "@/src/_helper/Scaler";
import { addBrandSchema } from "@/src/_helper/validator/VendorValidator";
import CategoryBottomSheet from "@/src/components/bottom-sheet/CategorySelectorBottomSheet";
import GalleryPermission from "@/src/components/bottom-sheet/GalleryPermission";
import LocationPermission from "@/src/components/bottom-sheet/LocationPermission";
import DefaultButton from "@/src/components/buttons/Default";
import { MainContainerForScreensWithInput } from "@/src/components/containers/MainContainerForScreensWithInput";
import LogoUploader from "@/src/components/inputs/LogoUplaoder";
import CategoryInput from "@/src/components/inputs/SelectorInput";
import TextInput from "@/src/components/inputs/TextInput";
import Loader from "@/src/components/loader/Loader";
import ErrorText from "@/src/components/shared/ErrorText";
import { ErrorComponent } from "@/src/components/ui/ErrorComponent";
import { ThemedText } from "@/src/components/ui/ThemedText";
import { Colors } from "@/src/constants/Colors";
import { useTheme } from "@/src/context/ThemeContext";
import { useImagePicker } from "@/src/hooks/useImagePicker";
import { useLocation } from "@/src/hooks/useLocation";
import useEditBrandMutation from "@/src/services/querys/vendor/useEditBrandMutation";
import useGetBrandDetails from "@/src/services/querys/vendor/useGetBrandDetails";
import { useLocalSearchParams } from "expo-router";
import { Formik } from "formik";
import React, { useEffect, useMemo, useRef, useState } from "react";
import { StyleSheet, View } from "react-native";
import { GestureHandlerRootView } from "react-native-gesture-handler";
import MapView, { Marker } from "react-native-maps";

import { BrandFormValues } from "@/src/types";

export default function EditBrand() {
  const { id: brandId } = useLocalSearchParams() as { id: string };
  const {
    data: brandResponse,
    isLoading,
    isError,
    error,
    refetch,
  } = useGetBrandDetails(brandId);

  const [showMap, setShowMap] = useState(false);
  const [showLocationForm, setShowLocationForm] = useState(false);
  const [showCategorySheet, setShowCategorySheet] = useState(false);
  const [selectedCategoryName, setSelectedCategoryName] = useState("");
  const [isFormInitialized, setIsFormInitialized] = useState(false);

  const formikSetFieldValueRef = useRef<any>(null);

  const mutation = useEditBrandMutation();
  const { imageUri, pickImage, showPermissionUI, closeUI } = useImagePicker();

  const {
    selectedLocation,
    mapRegion,
    isLoading: locationLoading,
    showPermissionUI: showLocationPermissionUI,
    hasLocationPermission,
    getCurrentLocation,
    selectLocationFromMap,
    clearLocation,
    closePermissionUI: closeLocationPermissionUI,
    setSelectedLocation,
  } = useLocation();

  // Initialize form data when brand data is loaded
  useEffect(() => {
    if (brandResponse?.data?.brand && !isFormInitialized) {
      const brand = brandResponse?.data?.brand;
      // Handle category name - support both object and string formats
      const categoryName =
        typeof brand?.category === "object" && brand?.category?.name
          ? brand?.category?.name
          : typeof brand?.category === "string"
          ? brand?.category
          : "";
      setSelectedCategoryName(categoryName);

      setIsFormInitialized(true);
    }
  }, [brandResponse, isFormInitialized]);

  // Helper function to safely get ID from object or string
  const getCategoryId = () => {
    if (brandResponse?.data?.brand) {
      const category = brandResponse?.data?.brand?.category;
      if (typeof category === "object" && category?._id) {
        return category._id;
      }
      if (typeof category === "string") {
        return category;
      }
    }
    return "";
  };

  const initialValues = useMemo(() => {
    if (brandResponse?.data?.brand) {
      const brand = brandResponse?.data?.brand;
      return {
        name: brand?.name || "",
        description: brand?.description || "",
        logo: brand?.logo || "",
        email: brand?.email || "",
        phone: brand?.phone || "",
        location: {
          address: brand?.location?.address || "",
          latitude: brand?.location?.latitude,
          longitude: brand?.location?.longitude,
          city: brand?.location?.city || "",
          state: brand?.location?.state || "",
          country: brand?.location?.country || "",
          zipCode: brand?.location?.zipCode || "",
        },
        category: getCategoryId(),
      };
    }

    return {
      name: "",
      description: "",
      logo: "",
      email: "",
      phone: "",
      location: {
        address: "",
        latitude: undefined,
        longitude: undefined,
        city: "",
        state: "",
        country: "",
        zipCode: "",
      },
      category: "",
    };
  }, [brandResponse]);
  const handleRefetch = async () => await refetch(); // Wait for the refetch to complete before proceeding
  const handleSubmit = (values: BrandFormValues) => {
    const formData = {
      ...values,
      logo: imageUri || values.logo,
      id: brandId,
    };
    mutation.mutateAsync({ brandData: formData, id: brandId });
  };

  const handleCategorySelect = (categoryId: string, categoryName: string) => {
    formikSetFieldValueRef.current?.("category", categoryId);
    setSelectedCategoryName(categoryName);
  };

  const handleMapPress = (event: any) => {
    const { latitude, longitude } = event.nativeEvent.coordinate;
    selectLocationFromMap(latitude, longitude);
    setShowLocationForm(true);
  };

  if (isLoading) {
    return <Loader />;
  }

  if (isError) {
    return <ErrorComponent error={error.message} onRetry={handleRefetch} />;
  }

  return (
    <GestureHandlerRootView>
      <MainContainerForScreensWithInput>
        <Formik<BrandFormValues>
          initialValues={initialValues}
          validationSchema={addBrandSchema}
          onSubmit={handleSubmit}
          enableReinitialize
        >
          {({
            handleChange,
            handleSubmit,
            values,
            errors,
            touched,
            isSubmitting,
            setFieldValue,
          }) => {
            formikSetFieldValueRef.current = setFieldValue;

            return (
              <FormContent
                values={values}
                errors={errors}
                touched={touched}
                isSubmitting={isSubmitting}
                locationLoading={locationLoading}
                handleChange={handleChange}
                handleSubmit={handleSubmit}
                setFieldValue={setFieldValue}
                showMap={showMap}
                setShowMap={setShowMap}
                showLocationForm={showLocationForm}
                setShowLocationForm={setShowLocationForm}
                showLocationPermissionUI={showLocationPermissionUI}
                hasLocationPermission={hasLocationPermission}
                mapRegion={mapRegion}
                handleMapPress={handleMapPress}
                getCurrentLocation={getCurrentLocation}
                selectedLocation={selectedLocation}
                setSelectedLocation={setSelectedLocation}
                clearLocation={clearLocation}
                imageUri={imageUri}
                pickImage={pickImage}
                mutation={mutation}
                existingLogo={values.logo}
                setShowCategorySheet={setShowCategorySheet}
                selectedCategoryName={selectedCategoryName}
              />
            );
          }}
        </Formik>
      </MainContainerForScreensWithInput>

      <GalleryPermission isVisible={showPermissionUI} onClose={closeUI} />
      <LocationPermission
        isVisible={showLocationPermissionUI}
        onClose={closeLocationPermissionUI}
      />
      <CategoryBottomSheet
        isVisible={showCategorySheet}
        onClose={() => setShowCategorySheet(false)}
        onCategorySelect={handleCategorySelect}
      />
    </GestureHandlerRootView>
  );
}

function FormContent({
  values,
  errors,
  touched,
  locationLoading,
  handleChange,
  handleSubmit,
  setFieldValue,
  showMap,
  setShowMap,
  showLocationPermissionUI,
  hasLocationPermission,
  mapRegion,
  handleMapPress,
  getCurrentLocation,
  selectedLocation,
  setSelectedLocation,
  imageUri,
  pickImage,
  mutation,
  existingLogo,
  setShowCategorySheet,
  selectedCategoryName,
}: any) {
  const { currentTheme } = useTheme();

  // Auto-fill Formik values when location is selected
  useEffect(() => {
    if (selectedLocation && selectedLocation?.address) {
      setFieldValue("location.address", selectedLocation?.address);
      setFieldValue("location.city", selectedLocation?.city);
      setFieldValue("location.state", selectedLocation?.state);
      setFieldValue("location.country", selectedLocation?.country);
      setFieldValue("location.zipCode", selectedLocation?.zipCode);
      setFieldValue("location.latitude", selectedLocation?.latitude);
      setFieldValue("location.longitude", selectedLocation?.longitude);
    }
  }, [selectedLocation, setFieldValue]);

  // Initialize selected location from form values
  useEffect(() => {
    if (
      values.location?.latitude &&
      values.location?.longitude &&
      !selectedLocation
    ) {
      setSelectedLocation({
        address: values.location.address,
        latitude: values.location.latitude,
        longitude: values.location.longitude,
        city: values.location.city,
        state: values.location.state,
        country: values.location.country,
        zipCode: values.location.zipCode,
      });
    }
  }, [values.location, selectedLocation, setSelectedLocation]);

  // Update logo field when image is picked
  useEffect(() => {
    if (imageUri) {
      setFieldValue("logo", imageUri);
    }
  }, [imageUri, setFieldValue]);

  const handleCurrentLocation = () => {
    getCurrentLocation();
  };

  return (
    <View style={styles.formContainer}>
      <TextInput
        onChangeText={handleChange("name")}
        value={values.name}
        placeholder="Enter brand name *"
        error={touched.name && errors.name}
      />

      <TextInput
        onChangeText={handleChange("description")}
        value={values.description}
        placeholder="Enter brand description"
        error={touched.description && errors.description}
        multiline
      />

      <TextInput
        onChangeText={handleChange("phone")}
        value={values.phone}
        placeholder="Enter phone number"
        keyboardType="phone-pad"
        error={touched.phone && errors.phone}
      />

      <TextInput
        onChangeText={handleChange("email")}
        value={values.email}
        placeholder="Enter email address"
        keyboardType="email-address"
        autoCapitalize="none"
        error={touched.email && errors.email}
      />

      <CategoryInput
        selectedCategory={selectedCategoryName}
        onPress={() => setShowCategorySheet(true)}
        error={touched.category && errors.category}
        placeholder="Select a category *"
      />

      <View style={styles.locationSection}>
        <ThemedText type="bold" size={18}>
          Location Information *
        </ThemedText>

        {hasLocationPermission && !showLocationPermissionUI && (
          <>
            <DefaultButton
              title={showMap ? "Hide Map" : "Select Location on Map"}
              onPress={() => setShowMap(!showMap)}
              style={{
                backgroundColor: Colors[currentTheme ?? "dark"].thirdary,
              }}
              active
            />
            {showMap && (
              <View>
                <View style={styles.mapWrapper}>
                  <MapView
                    style={styles.map}
                    region={mapRegion}
                    onPress={handleMapPress}
                    showsUserLocation
                    showsMyLocationButton
                  >
                    {selectedLocation?.latitude &&
                      selectedLocation?.longitude && (
                        <Marker
                          coordinate={{
                            latitude: selectedLocation?.latitude,
                            longitude: selectedLocation?.longitude,
                          }}
                          title="Selected Location"
                        />
                      )}
                  </MapView>
                </View>

                <DefaultButton
                  isSubmitting={locationLoading}
                  disabled={locationLoading}
                  onPress={handleCurrentLocation}
                  title="Use Current Location"
                  style={{
                    backgroundColor: Colors[currentTheme ?? "dark"].thirdary,
                  }}
                  active
                />
              </View>
            )}
          </>
        )}

        {!hasLocationPermission && !showLocationPermissionUI && (
          <DefaultButton
            title="Enable Location Access"
            onPress={handleCurrentLocation}
          />
        )}

        {touched.location &&
          errors.location &&
          typeof errors.location === "string" && (
            <ErrorText error={errors.location} />
          )}
        {touched.location &&
          errors.location &&
          typeof errors.location === "object" && (
            <ErrorText error={"Location is required"} />
          )}
      </View>

      <LogoUploader
        imageUri={imageUri}
        pickImage={pickImage}
        existingImage={existingLogo}
        error={touched.logo && errors.logo}
      />

      <DefaultButton
        title="Update Brand"
        onPress={handleSubmit}
        isSubmitting={mutation.isPending}
        disabled={mutation.isPending}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  formContainer: {
    flex: 1,
  },
  locationSection: {
    marginVertical: scale(10),
  },
  mapWrapper: {
    height: scale(200),
    borderRadius: scale(12),
  },
  map: {
    flex: 1,
  },
});
