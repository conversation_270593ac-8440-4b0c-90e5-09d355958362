import { scale } from "@/src/_helper/Scaler";
import { ThemedText } from "@/src/components/ui/ThemedText";
import { useLanguage } from "@/src/context/LanguageContext";
import { StyleSheet, View } from "react-native";

export default function OrdersHeader() {
  const { t } = useLanguage();

  return (
    <View style={styles.header}>
      <ThemedText type="bold" size={22}>
        {t("vendor.headers.orders")}
      </ThemedText>
      <ThemedText size={16}>{t("vendor.headers.ordersSubtitle")}</ThemedText>
    </View>
  );
}

const styles = StyleSheet.create({
  header: {
    marginTop: scale(20),
    paddingBottom: scale(16),
  },
});
