import { scale } from "@/src/_helper/Scaler";
import { ThemedText } from "@/src/components/ui/ThemedText";
import { Colors } from "@/src/constants/Colors";
import { useLanguage } from "@/src/context/LanguageContext";
import { useTheme } from "@/src/context/ThemeContext";
import { MaterialIcons } from "@expo/vector-icons";
import { router } from "expo-router";
import React from "react";
import { StyleSheet, TouchableOpacity, View } from "react-native";

import { OrderCardProps } from "@/src/types";

export default function OrderCard({ item }: OrderCardProps) {
  const { currentTheme } = useTheme();
  const { t } = useLanguage();

  // Helper function to get status color
  const getStatusColor = (status: string) => {
    switch (status?.toLowerCase()) {
      case "delivered":
        return Colors[currentTheme ?? "dark"].green;
      case "accepted":
        return Colors[currentTheme ?? "dark"].blue;
      case "pending":
        return Colors[currentTheme ?? "dark"].orange;
      case "cancelled":
        return Colors[currentTheme ?? "dark"].error;
      default:
        return Colors[currentTheme ?? "dark"].secondary;
    }
  };

  return (
    <TouchableOpacity
      style={[
        styles.card,
        {
          shadowColor: Colors[currentTheme ?? "dark"].text,
        },
      ]}
      onPress={() =>
        router.push(`/(vendor)/(screens)/order/${item.id}/order-details`)
      }
      activeOpacity={0.7}
    >
      <View style={styles.content}>
        <View
          style={[
            styles.avatarContainer,
            { backgroundColor: Colors[currentTheme ?? "dark"].primary + "20" },
          ]}
        >
          <MaterialIcons
            name="person"
            size={28}
            color={Colors[currentTheme ?? "dark"].text}
          />
        </View>

        <View style={styles.info}>
          <View style={styles.headerSection}>
            <ThemedText type="semi-bold" size={16} style={styles.userName}>
              {item.name}
            </ThemedText>
            <View
              style={[
                styles.badge,
                { backgroundColor: getStatusColor(item.status) },
              ]}
            >
              <ThemedText
                size={10}
                type="semi-bold"
                style={{
                  color: Colors[currentTheme ?? "dark"].background,
                }}
              >
                {t(`orderStatus.${item.status.toLowerCase()}`).toUpperCase()}
              </ThemedText>
            </View>
          </View>

          <View style={styles.detailsSection}>
            <View style={styles.detailRow}>
              <MaterialIcons
                name="inventory"
                size={14}
                color={Colors[currentTheme ?? "dark"].secondary}
                style={styles.icon}
              />
              <ThemedText
                size={13}
                style={{
                  color: Colors[currentTheme ?? "dark"].secondary,
                  flex: 1,
                }}
              >
                {item.product}
              </ThemedText>
            </View>

            <View style={styles.detailRow}>
              <MaterialIcons
                name="local-offer"
                size={14}
                color={Colors[currentTheme ?? "dark"].secondary}
                style={styles.icon}
              />
              <ThemedText
                size={13}
                style={{
                  color: Colors[currentTheme ?? "dark"].secondary,
                  flex: 1,
                }}
              >
                {item.brand}
              </ThemedText>
            </View>

            <View style={styles.detailRow}>
              <MaterialIcons
                name="phone"
                size={14}
                color={Colors[currentTheme ?? "dark"].secondary}
                style={styles.icon}
              />
              <ThemedText
                size={13}
                style={{
                  color: Colors[currentTheme ?? "dark"].secondary,
                  flex: 1,
                }}
              >
                {item.phone}
              </ThemedText>
            </View>
          </View>
        </View>
      </View>

      <View style={styles.chevronContainer}>
        <MaterialIcons
          name="chevron-right"
          size={24}
          color={Colors[currentTheme ?? "dark"].secondary}
        />
      </View>
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  card: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    borderRadius: scale(16),
    paddingVertical: scale(16),
    marginBottom: scale(12),
  },
  content: {
    flexDirection: "row",
    alignItems: "flex-start",
    flex: 1,
  },
  avatarContainer: {
    width: scale(50),
    height: scale(50),
    borderRadius: scale(25),
    marginRight: scale(16),
    alignItems: "center",
    justifyContent: "center",
  },
  info: {
    flex: 1,
  },
  headerSection: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    marginBottom: scale(8),
  },
  badge: {
    paddingHorizontal: scale(8),
    paddingVertical: scale(3),
    borderRadius: scale(10),
  },
  detailsSection: {
    gap: scale(6),
  },
  userName: {
    flex: 1,
  },
  detailRow: {
    flexDirection: "row",
    alignItems: "center",
  },
  icon: {
    marginRight: scale(8),
    width: scale(16),
  },
  chevronContainer: {
    paddingLeft: scale(8),
    alignItems: "center",
    justifyContent: "center",
  },
});
