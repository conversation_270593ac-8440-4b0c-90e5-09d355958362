import { scale, scaleFont } from "@/src/_helper/Scaler";
import SkeletonDashboard from "@/src/components/loader/SkeletonDashboard";
import { ErrorComponent } from "@/src/components/ui/ErrorComponent";
import { NetworkErrorComponent } from "@/src/components/ui/NetworkErrorComponent";
import { ThemedText } from "@/src/components/ui/ThemedText";
import { Colors } from "@/src/constants/Colors";
import { useLanguage } from "@/src/context/LanguageContext";
import { useTheme } from "@/src/context/ThemeContext";
import useGetDashboardAnalytics from "@/src/services/querys/vendor/useGetDashboardAnalytics";
import { router } from "expo-router";
import React from "react";
import {
  Dimensions,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from "react-native";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, PieChart } from "react-native-gifted-charts";

import { MetricCardProps, ProductCardProps } from "@/src/types";

const { width } = Dimensions.get("window");

const Dashboard = () => {
  const { data, isLoading, error, refetch } = useGetDashboardAnalytics();
  const { currentTheme } = useTheme();
  const { t } = useLanguage();
  const theme = currentTheme ?? "dark";

  // Helper function to translate order status labels
  const translateOrderStatus = (status: string): string => {
    const statusKey = status.toLowerCase();
    return t(`orderStatus.${statusKey}`);
  };

  // Navigation handlers
  const handleOrdersNavigation = () => {
    router.push("/(vendor)/(tabs)/orders");
  };

  const handleProductsNavigation = () => {
    router.push("/(vendor)/(tabs)/products");
  };

  // Handle loading state
  if (isLoading) {
    return <SkeletonDashboard />;
  }

  // Handle error state
  if (error) {
    const isNetworkError =
      error.message.includes("Network") || error.message.includes("timeout");

    if (isNetworkError) {
      return <NetworkErrorComponent onRetry={refetch} />;
    }

    return <ErrorComponent error={error.message} onRetry={refetch} />;
  }

  // Extract data from API response
  const dashboardData = data?.data;

  if (!dashboardData) {
    return <ErrorComponent error="No data available" onRetry={refetch} />;
  }

  const {
    overviewMetrics,
    monthlyOrdersData,
    orderStatusData,
    deliveredOrdersData,
    mostOrderedProductsData,
    orderStatistics,
    productPerformance,
    customerEngagement,
  } = dashboardData;
  const MetricCard = ({
    title,
    value,
    change,
    isPositive = true,
    onPress,
  }: MetricCardProps & { onPress?: () => void }) => {
    const { currentTheme } = useTheme();
    const theme = currentTheme ?? "dark";

    if (onPress) {
      return (
        <TouchableOpacity
          style={[
            styles.metricCard,
            {
              borderColor: Colors[theme].border,
            },
          ]}
          onPress={onPress}
          activeOpacity={0.7}
        >
          <ThemedText
            style={[
              styles.metricTitle,
              {
                color: Colors[theme].gray,
              },
            ]}
          >
            {title}
          </ThemedText>
          <ThemedText type="bold" size={20} style={[styles.metricTitle]}>
            {value}
          </ThemedText>
          {change && (
            <ThemedText
              size={12}
              style={[
                isPositive ? styles.positiveChange : styles.negativeChange,
              ]}
            >
              {change}
            </ThemedText>
          )}
        </TouchableOpacity>
      );
    }

    return (
      <View
        style={[
          styles.metricCard,
          {
            borderColor: Colors[theme].border,
          },
        ]}
      >
        <ThemedText
          style={[
            styles.metricTitle,
            {
              color: Colors[theme].gray,
            },
          ]}
        >
          {title}
        </ThemedText>
        <ThemedText type="bold" size={20} style={[styles.metricTitle]}>
          {value}
        </ThemedText>
        {change && (
          <ThemedText
            size={12}
            style={[isPositive ? styles.positiveChange : styles.negativeChange]}
          >
            {change}
          </ThemedText>
        )}
      </View>
    );
  };

  const ProductCard = ({
    name,
    sales,
    color,
    onPress,
  }: ProductCardProps & { onPress?: () => void }) => (
    <TouchableOpacity
      style={[
        styles.productCard,
        {
          borderColor: Colors[theme].border,
        },
      ]}
      onPress={onPress}
      activeOpacity={0.7}
      disabled={!onPress}
    >
      <View style={[styles.productIndicator, { backgroundColor: color }]} />
      <View style={styles.productInfo}>
        <ThemedText type="semi-bold" size={16} style={styles.productName}>
          {name}
        </ThemedText>
        <ThemedText style={{ color: Colors[theme].gray }}>{sales}</ThemedText>
      </View>
    </TouchableOpacity>
  );

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      {/* Header */}
      <ThemedText size={24} type="bold" style={styles.header}>
        {t("dashboard.overview")}
      </ThemedText>

      {/* Overview Metrics */}
      <View style={styles.overviewGrid}>
        <MetricCard
          title={t("dashboard.totalOrders")}
          value={overviewMetrics.totalOrders.toString()}
          change=""
          onPress={handleOrdersNavigation}
        />
        <MetricCard
          title={t("dashboard.delivered")}
          value={overviewMetrics.delivered.toString()}
          change=""
          onPress={handleOrdersNavigation}
        />
        <MetricCard
          title={t("dashboard.pending")}
          value={overviewMetrics.pending.toString()}
          change=""
          isPositive={false}
          onPress={handleOrdersNavigation}
        />
      </View>

      {/* Monthly Orders Trend */}
      <View style={styles.section}>
        <ThemedText size={24} type="bold" style={styles.sectionTitle}>
          {t("dashboard.monthlyOrders")}
        </ThemedText>
        <View style={styles.chartHeader}>
          <ThemedText size={24}>{overviewMetrics.totalOrders}</ThemedText>
          <ThemedText
            style={[
              styles.chartSubtext,
              {
                color: Colors[theme].gray,
              },
            ]}
          >
            {t("dashboard.totalOrdersOverTime")}
          </ThemedText>
        </View>
        <TouchableOpacity onPress={handleOrdersNavigation} activeOpacity={0.8}>
          <LineChart
            data={monthlyOrdersData}
            width={width - scale(64)}
            height={scale(180)}
            curved
            color={Colors[theme].green}
            thickness={scale(2)}
            dataPointsColor={Colors[theme].green}
            dataPointsRadius={scale(4)}
            hideRules
            hideYAxisText
            xAxisColor={Colors[theme].border}
            yAxisColor={Colors[theme].border}
            xAxisLabelTextStyle={{
              color: Colors[theme].gray,
              fontSize: scaleFont(12),
            }}
            initialSpacing={scale(20)}
            endSpacing={scale(20)}
            spacing={scale(40)}
            rulesColor={Colors[theme].border}
            showVerticalLines={false}
            verticalLinesColor={Colors[theme].border}
            xAxisThickness={scale(1)}
            yAxisThickness={scale(1)}
          />
        </TouchableOpacity>
      </View>

      {/* Order Status Distribution */}
      <View style={styles.section}>
        <ThemedText size={24} type="bold" style={styles.sectionTitle}>
          {t("dashboard.orderStatusDistribution")}
        </ThemedText>
        <TouchableOpacity onPress={handleOrdersNavigation} activeOpacity={0.8}>
          <View style={styles.pieChartContainer}>
            <PieChart
              data={orderStatusData}
              radius={scale(80)}
              innerRadius={scale(40)}
              showText
              textColor={Colors[theme].text}
              textSize={scaleFont(12)}
            />
            <View style={styles.categoryLegend}>
              {orderStatusData.map((item, index) => (
                <View key={index} style={styles.categoryItem}>
                  <View
                    style={[
                      styles.categoryColor,
                      { backgroundColor: item.color },
                    ]}
                  />
                  <ThemedText>
                    {translateOrderStatus(item.label)} ({item.value})
                  </ThemedText>
                </View>
              ))}
            </View>
          </View>
        </TouchableOpacity>
      </View>

      {/* Delivered Orders Monthly */}
      <View style={styles.section}>
        <ThemedText type="bold" size={24} style={styles.sectionTitle}>
          {t("dashboard.deliveredOrders")}
        </ThemedText>
        <View style={styles.chartHeader}>
          <ThemedText size={24}>{overviewMetrics.delivered}</ThemedText>
          <ThemedText
            style={[
              styles.chartSubtext,
              {
                color: Colors[theme].gray,
              },
            ]}
          >
            {t("dashboard.successfullyDeliveredThisPeriod")}
          </ThemedText>
        </View>
        <TouchableOpacity onPress={handleOrdersNavigation} activeOpacity={0.8}>
          <BarChart
            data={deliveredOrdersData}
            width={width - scale(64)}
            height={scale(180)}
            barWidth={scale(30)}
            barBorderRadius={scale(4)}
            hideRules
            hideYAxisText
            xAxisColor={Colors[theme].border}
            yAxisColor={Colors[theme].border}
            xAxisLabelTextStyle={{
              color: Colors[theme].gray,
              fontSize: scaleFont(12),
            }}
            initialSpacing={scale(20)}
            endSpacing={scale(20)}
            spacing={scale(40)}
            xAxisThickness={scale(1)}
            yAxisThickness={scale(1)}
          />
        </TouchableOpacity>
      </View>

      {/* Most Ordered Products */}
      <View style={styles.section}>
        <ThemedText type="bold" size={24} style={styles.sectionTitle}>
          {t("dashboard.mostOrderedProducts")}
        </ThemedText>
        <View style={styles.chartHeader}>
          <ThemedText size={24}>{t("dashboard.topProducts")}</ThemedText>
          <Text
            style={[
              styles.chartSubtext,
              {
                color: Colors[theme].gray,
              },
            ]}
          >
            {t("dashboard.bestPerformingProducts")}
          </Text>
        </View>
        <TouchableOpacity
          onPress={handleProductsNavigation}
          activeOpacity={0.8}
        >
          <BarChart
            data={mostOrderedProductsData}
            width={width - scale(64)}
            height={scale(200)}
            barWidth={scale(35)}
            barBorderRadius={scale(4)}
            hideRules
            hideYAxisText
            xAxisColor={Colors[theme].border}
            yAxisColor={Colors[theme].border}
            xAxisLabelTextStyle={{
              color: Colors[theme].gray,
              fontSize: scaleFont(12),
            }}
            initialSpacing={scale(15)}
            endSpacing={scale(15)}
            spacing={scale(35)}
            xAxisThickness={scale(1)}
            yAxisThickness={scale(1)}
          />
        </TouchableOpacity>
      </View>

      {/* Order Statistics */}
      <View style={styles.section}>
        <ThemedText type="bold" size={24} style={styles.sectionTitle}>
          {t("dashboard.orderStatistics")}
        </ThemedText>
        <View style={styles.orderStats}>
          <TouchableOpacity
            style={[
              styles.orderStatCard,
              {
                borderColor: Colors[theme].border,
              },
            ]}
            onPress={handleOrdersNavigation}
            activeOpacity={0.7}
          >
            <ThemedText
              style={[
                styles.customerStatTitle,
                {
                  color: Colors[theme].gray,
                },
              ]}
            >
              {t("dashboard.completedOrders")}
            </ThemedText>
            <ThemedText type="bold" size={20}>
              {orderStatistics.completedOrders}
            </ThemedText>
          </TouchableOpacity>
          <TouchableOpacity
            style={[
              styles.orderStatCard,
              {
                borderColor: Colors[theme].border,
              },
            ]}
            onPress={handleOrdersNavigation}
            activeOpacity={0.7}
          >
            <ThemedText
              style={[
                styles.customerStatTitle,
                {
                  color: Colors[theme].gray,
                },
              ]}
            >
              {t("dashboard.cancelledOrders")}
            </ThemedText>
            <ThemedText type="bold" size={20}>
              {orderStatistics.cancelledOrders}
            </ThemedText>
          </TouchableOpacity>
        </View>
      </View>

      {/* Product Performance */}
      <View style={styles.section}>
        <ThemedText type="bold" size={24} style={styles.sectionTitle}>
          {t("dashboard.productPerformance")}
        </ThemedText>
        {productPerformance.map((product, index) => (
          <ProductCard
            key={index}
            name={product.name}
            sales={product.sales}
            color={product.color}
            onPress={handleProductsNavigation}
          />
        ))}
      </View>

      <View style={styles.section}>
        <ThemedText type="bold" size={24} style={styles.sectionTitle}>
          {t("dashboard.customerEngagement")}
        </ThemedText>
        <View style={styles.customerStats}>
          <View
            style={[
              styles.customerStatCard,
              {
                borderColor: Colors[theme].border,
              },
            ]}
          >
            <ThemedText
              style={[
                styles.customerStatTitle,
                {
                  color: Colors[theme].gray,
                },
              ]}
            >
              {t("dashboard.activeCustomers")}
            </ThemedText>
            <ThemedText type="bold" size={20}>
              {customerEngagement.activeCustomers}
            </ThemedText>
          </View>
          <View
            style={[
              styles.customerStatCard,
              {
                borderColor: Colors[theme].border,
              },
            ]}
          >
            <ThemedText
              style={[
                styles.customerStatTitle,
                {
                  color: Colors[theme].gray,
                },
              ]}
            >
              {t("dashboard.newCustomers")}
            </ThemedText>
            <ThemedText type="bold" size={20}>
              {customerEngagement.newCustomers}
            </ThemedText>
          </View>
        </View>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    marginBottom: scale(20),
  },
  overviewGrid: {
    flexDirection: "row",
    flexWrap: "wrap",
    justifyContent: "space-between",
    marginBottom: scale(24),
  },
  metricCard: {
    borderRadius: scale(12),
    padding: scale(16),
    width: "48%",
    marginBottom: scale(12),
    borderWidth: scale(1),
  },
  metricTitle: {
    marginBottom: scale(8),
  },

  positiveChange: {
    color: Colors.light.green,
  },
  negativeChange: {
    color: Colors.light.error,
  },
  section: {
    marginBottom: scale(24),
  },
  sectionTitle: {
    marginBottom: scale(16),
  },
  chartHeader: {
    marginBottom: scale(16),
  },

  chartSubtext: {
    marginTop: scale(10),
  },
  chart: {
    borderRadius: scale(12),
    marginVertical: scale(8),
  },
  chartLegend: {
    flexDirection: "row",
    justifyContent: "center",
    marginBottom: scale(16),
    gap: scale(20),
  },
  legendItem: {
    flexDirection: "row",
    alignItems: "center",
  },
  legendColor: {
    width: scale(12),
    height: scale(12),
    borderRadius: scale(6),
    marginRight: scale(8),
  },

  pieChartContainer: {
    alignItems: "center",
  },
  categoryLegend: {
    marginTop: scale(20),
    alignSelf: "stretch",
  },
  categoryItem: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: scale(8),
  },
  categoryColor: {
    width: scale(16),
    height: scale(16),
    borderRadius: scale(8),
    marginRight: scale(12),
  },

  orderStats: {
    flexDirection: "row",
    justifyContent: "space-between",
  },
  orderStatCard: {
    borderRadius: scale(12),
    padding: scale(16),
    width: "48%",
    borderWidth: scale(1),
  },

  productCard: {
    borderRadius: scale(12),
    padding: scale(16),
    marginBottom: scale(12),
    flexDirection: "row",
    alignItems: "center",
    borderWidth: scale(1),
  },
  productIndicator: {
    width: scale(12),
    height: scale(12),
    borderRadius: scale(8),
    marginRight: scale(12),
  },
  productInfo: {
    flex: 1,
  },
  productName: {
    marginBottom: scale(4),
  },

  customerStats: {
    flexDirection: "row",
    justifyContent: "space-between",
  },
  customerStatCard: {
    borderRadius: scale(12),
    padding: scale(16),
    width: "48%",
    borderWidth: scale(1),
  },
  customerStatTitle: {
    marginBottom: scale(8),
  },
});

export default Dashboard;
