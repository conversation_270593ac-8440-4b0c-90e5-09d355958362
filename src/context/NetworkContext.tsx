import NetInfo from "@react-native-community/netinfo";
import React, { useEffect, useState } from "react";
import Loader from "../components/loader/Loader";
import { NetworkErrorComponent } from "../components/ui/NetworkErrorComponent";

interface NetworkContextProps {
  children: React.ReactNode;
}

export default function NetworkContext({ children }: NetworkContextProps) {
  const [isConnected, setIsConnected] = useState<boolean | null>(null);

  useEffect(() => {
    const unsubscribe = NetInfo.addEventListener((state) => {
      setIsConnected(state.isConnected);
    });

    return () => unsubscribe();
  }, [isConnected]);

  if (isConnected === false) {
    return <NetworkErrorComponent onRetry={() => setIsConnected(null)} />;
  }

  if (isConnected === null) {
    return <Loader />;
  }

  return <>{children}</>;
}
