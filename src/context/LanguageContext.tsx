import { I18n } from "i18n-js";
import React, { createContext, useContext, useEffect, useState } from "react";
import { MMKV } from "react-native-mmkv";

import en from "../translations/en.json";
import fr from "../translations/fr.json";

const i18n = new I18n({
  en,
  fr,
});

i18n.defaultLocale = "en";
i18n.enableFallback = true;
i18n.translations = { en, fr };

const storage = new MMKV();

const LOCALE_KEY = "userLocale";

import { LanguageContextType } from "@/src/types";

const LanguageContext = createContext<LanguageContextType | undefined>(
  undefined
);

export const LanguageProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  // Read locale synchronously from MMKV or fallback to 'en'
  const [locale, setLocaleState] = useState(() => {
    const savedLocale = storage.getString(LOCALE_KEY);
    return savedLocale ?? "en";
  });

  const [, forceUpdate] = useState({});

  // Update i18n locale whenever locale state changes
  useEffect(() => {
    i18n.locale = locale;
    forceUpdate({}); // force re-render to update translations if needed
  }, [locale]);

  // Save locale to MMKV whenever it changes
  useEffect(() => {
    storage.set(LOCALE_KEY, locale);
  }, [locale]);

  const setLocale = (newLocale: string) => {
    setLocaleState(newLocale);
  };

  const t = (scope: string, options?: object) => {
    return i18n.t(scope, options);
  };

  return (
    <LanguageContext.Provider value={{ locale, setLocale, t }}>
      {children}
    </LanguageContext.Provider>
  );
};

export const useLanguage = () => {
  const context = useContext(LanguageContext);
  if (context === undefined) {
    throw new Error("useLanguage must be used within a LanguageProvider");
  }
  return context;
};
