import { Href, Redirect } from "expo-router";
import React, { useEffect, useState } from "react";
import { MMKV } from "react-native-mmkv";
import Loader from "../components/loader/Loader";
import { ThemedView } from "../components/ui/ThemedView";

interface NetworkContextProps {
  children: React.ReactNode;
}

const HOME = "/(user-entry)/home" as Href;

const storage = new MMKV();
export const hasSeenOnboarding = (): boolean => {
  return storage.getBoolean("hasSeenOnboarding") || false;
};

export const setSeenOnboarding = () => {
  storage.set("hasSeenOnboarding", true);
};

export default function RedirectContext({ children }: NetworkContextProps) {
  const [hasSeenOnboardingState, setHasSeenOnboardingState] = useState<
    boolean | null
  >(null);
  const [isCheckingOnboarding, setIsCheckingOnboarding] = useState(true);
  useEffect(() => {
    try {
      const seen = hasSeenOnboarding();
      setHasSeenOnboardingState(!!seen);
    } catch (error) {
      console.error("MMKV error reading onboarding status:", error);
      setHasSeenOnboardingState(false);
    } finally {
      setIsCheckingOnboarding(false);
    }
  }, []);

  if (isCheckingOnboarding || hasSeenOnboardingState === null) {
    return <Loader />;
  }
  if (hasSeenOnboardingState) {
    return <Redirect href={HOME} />;
  }

  return <ThemedView style={{ flex: 1 }}>{children}</ThemedView>;
}
