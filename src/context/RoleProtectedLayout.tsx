import { decodeJWT } from "@/src/_helper/decode/decodeJWT";
import { addInterceptors } from "@/src/services/api";
import { Href, Redirect } from "expo-router";
import React, { useEffect } from "react";
import Loader from "../components/loader/Loader";
import { useSession } from "./AuthContext";
export type UserRole = "vendor" | "client" | null;

interface RoleProtectedLayoutProps {
  children: React.ReactNode;
  allowedRoles: UserRole[];
}

const CLIENT_PATH = "/(client)/(tabs)" as Href;
const VENDOR_PATH = "/(vendor)/" as Href;

const DEFAULT_REDIRECT = "/(user-entry)/" as Href;

export default function RoleProtectedLayout({
  children,
  allowedRoles,
}: RoleProtectedLayoutProps) {
  const { session, isLoading, signOut } = useSession();
  useEffect(() => {
    if (session?.token) {
      addInterceptors(session.token);
    }
  }, [session?.token, signOut]);

  if (isLoading) {
    return <Loader />;
  }

  if (!session || !session.role) {
    return <Redirect href={DEFAULT_REDIRECT} />;
  }

  if (!allowedRoles.includes(session.role)) {
    try {
      const decoded = decodeJWT(session.token);
      const currentTime = Math.floor(Date.now() / 1000);
      if (decoded?.exp < currentTime) {
        signOut();
        return <Redirect href={DEFAULT_REDIRECT} />;
      }
    } catch (error) {
      console.error("Token decoding error:", error);
      signOut();
      return <Redirect href={DEFAULT_REDIRECT} />;
    }
    switch (session.role) {
      case "client":
        return <Redirect href={CLIENT_PATH} />;
      case "vendor":
        return <Redirect href={VENDOR_PATH} />;

      default:
        signOut();
        return <Redirect href={DEFAULT_REDIRECT} />;
    }
  }

  return <>{children}</>;
}
