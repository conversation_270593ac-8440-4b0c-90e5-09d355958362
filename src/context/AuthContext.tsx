// src/contexts/ctx.tsx
import { useQueryClient } from "@tanstack/react-query";
import { createContext, type PropsWithChildren, useContext } from "react";
import { useStorageState } from "../hooks/useStorageState";
import { addInterceptors, removeInterceptors } from "../services/api";

import { AuthContextType, SessionData } from "@/src/types";
const AuthContext = createContext<AuthContextType>({
  signIn: () => null,
  signOut: () => null,
  session: null,
  isLoading: false,
});

export function useSession() {
  const value = useContext(AuthContext);
  if (process.env.NODE_ENV !== "production") {
    if (!value) {
      throw new Error("useSession must be wrapped in a <SessionProvider />");
    }
  }
  return value;
}

export function SessionProvider({ children }: PropsWithChildren) {
  const [[isLoading, session], setSession] = useStorageState("session");
  const queryClient = useQueryClient();

  const handleSignOut = () => {
    console.log("🔐 Handling sign out...");
    setSession(null);
    queryClient.cancelQueries();
    queryClient.getQueryCache().clear();
    queryClient.invalidateQueries();
    queryClient.removeQueries();
    queryClient.clear();
    removeInterceptors();
    console.log("🔐 Finished handling sign out...");
  };
  return (
    <AuthContext.Provider
      value={{
        signIn: (sessionData: SessionData) => {
          setSession(JSON.stringify(sessionData));
          addInterceptors(sessionData.token);
        },
        signOut: handleSignOut,
        session: session ? JSON.parse(session) : null,
        isLoading,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
}
