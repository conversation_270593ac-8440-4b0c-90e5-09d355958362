import {
  DarkTheme as NavigationDarkTheme,
  DefaultTheme as NavigationDefaultTheme,
  ThemeProvider,
} from "@react-navigation/native";
import { Colors } from "../constants/Colors";
import { useTheme } from "./ThemeContext";

// Extend and override only what you need
export const DarkTheme = {
  ...NavigationDarkTheme,
  colors: {
    ...NavigationDarkTheme.colors,
    background: Colors.dark.background,
    primary: Colors.dark.primary,
  },
};

export const DefaultTheme = {
  ...NavigationDefaultTheme,
  colors: {
    ...NavigationDefaultTheme.colors,
    background: Colors.light.background,
    primary: Colors.light.primary,
  },
};

export function NavigationThemeWrapper({
  children,
}: {
  children: React.ReactNode;
}) {
  const { currentTheme } = useTheme();

  return (
    <ThemeProvider value={currentTheme === "dark" ? DarkTheme : DefaultTheme}>
      {children}
    </ThemeProvider>
  );
}
