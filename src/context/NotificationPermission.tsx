import { client } from "@/src/services/api";
import React, { useEffect, useState } from "react";
import { GestureHandlerRootView } from "react-native-gesture-handler";
import { MMKV } from "react-native-mmkv";
import NotificationPermissionBottomTab from "../components/bottom-sheet/NotificationPermission";
import Loader from "../components/loader/Loader";
import { useNotification } from "../hooks/useNotification";
import { useSession } from "./AuthContext";
export type UserRole = "vendor" | "client" | null;

const storage = new MMKV();
const EXPO_PUSH_TOKEN_KEY = "expo_push_token";
const NOTIFICATION_PERMISSION_STATUS_KEY = "notification_permission_status";
const NOTIFICATION_SETUP_COMPLETED_KEY = "notification_setup_completed";
const USER_ROLE_KEY = "user_role";
// Utility function to reset notification setup (useful for testing)
export const resetNotificationSetup = () => {
  storage.delete(EXPO_PUSH_TOKEN_KEY);
  storage.delete(NOTIFICATION_PERMISSION_STATUS_KEY);
  storage.delete(NOTIFICATION_SETUP_COMPLETED_KEY);
  storage.delete(USER_ROLE_KEY);
  console.log(
    "📱 Notification setup reset - app will treat next launch as first time"
  );
};

interface NotificationPermissionProps {
  children: React.ReactNode;
}

export default function NotificationPermission({
  children,
}: NotificationPermissionProps) {
  const { session, isLoading } = useSession();
  const [isVisible, setIsVisible] = useState(false);
  const [isProcessingRequest, setIsProcessingRequest] = useState(false);
  const { expoPushToken, deviceInfo, hasNotificationPermission, canAskAgain } =
    useNotification();
  console.log(expoPushToken);

  useEffect(() => {
    if (!hasNotificationPermission && !canAskAgain) {
      setIsVisible(true);
    }
  }, [hasNotificationPermission, canAskAgain]);

  useEffect(() => {
    const sendTokenToBackend = async () => {
      // Don't proceed if session/device info not ready or already processing
      if (!session?.role || !deviceInfo || isProcessingRequest) return;

      try {
        setIsProcessingRequest(true);

        // Get stored values from MMKV
        const storedToken = storage.getString(EXPO_PUSH_TOKEN_KEY);
        const storedPermissionStatus = storage.getString(
          NOTIFICATION_PERMISSION_STATUS_KEY
        );
        const newRole = storage.getString(USER_ROLE_KEY) !== session.role;
        const setupCompleted =
          storage.getBoolean(NOTIFICATION_SETUP_COMPLETED_KEY) || false;

        const currentPermissionStatus = hasNotificationPermission.toString();

        // Check if this is first time setup (no setup completed flag)
        const isFirstTimeSetup = !setupCompleted;

        // Check for changes
        const tokenChanged = expoPushToken && storedToken !== expoPushToken;
        const permissionStatusChanged =
          storedPermissionStatus !== currentPermissionStatus;

        console.log("📱 Notification setup check:");
        console.log("📱 First time setup:", isFirstTimeSetup);
        console.log("📱 Token changed:", tokenChanged);
        console.log("📱 Permission status changed:", permissionStatusChanged);
        console.log("📱 Current permission:", hasNotificationPermission);
        console.log("📱 Current token:", expoPushToken ? "exists" : "null");

        // Scenario 1: First time app install - user accepts notifications
        if (isFirstTimeSetup && hasNotificationPermission && expoPushToken) {
          console.log(
            "📱 Scenario 1: First time setup - permission granted with token"
          );

          const payload = {
            expoPushToken: expoPushToken,
            type: session.role,
            device_id: deviceInfo.deviceId,
            device_type: deviceInfo.deviceType,
            status: true,
          };

          console.log("📱 Sending first-time acceptance payload:", payload);
          await client.post("/profile/add-token", payload);

          // Store values after successful API call
          storage.set(EXPO_PUSH_TOKEN_KEY, expoPushToken);
          storage.set(NOTIFICATION_PERMISSION_STATUS_KEY, "true");
          storage.set(NOTIFICATION_SETUP_COMPLETED_KEY, true);

          console.log("📱 First-time setup completed successfully");
          return;
        }

        // Scenario 2: First time app install - user refuses notifications
        if (isFirstTimeSetup && !hasNotificationPermission) {
          console.log("📱 Scenario 2: First time setup - permission denied");

          const payload = {
            expoPushToken: null,
            type: session.role,
            device_id: deviceInfo.deviceId,
            device_type: deviceInfo.deviceType,
            status: false,
          };

          console.log("📱 Sending first-time refusal payload:", payload);
          await client.post("/profile/add-token", payload);

          // Store values after successful API call
          storage.delete(EXPO_PUSH_TOKEN_KEY); // Clear any token
          storage.set(NOTIFICATION_PERMISSION_STATUS_KEY, "false");
          storage.set(NOTIFICATION_SETUP_COMPLETED_KEY, true);

          console.log("📱 First-time refusal setup completed");
          return;
        }

        // Scenario 3: Permission status changed (user changed settings) or user changed account
        if (
          (!isFirstTimeSetup && permissionStatusChanged) ||
          (!isFirstTimeSetup && newRole)
        ) {
          console.log("📱 Scenario 3: Permission status changed");

          let tokenToSend;
          if (hasNotificationPermission) {
            // User enabled notifications - wait for token if not available yet
            if (!expoPushToken) {
              console.log(
                "📱 Permission granted but token not ready yet, waiting..."
              );
              return;
            }
            tokenToSend = expoPushToken;
          } else {
            // User disabled notifications
            tokenToSend = null;
          }

          const payload = {
            expoPushToken: tokenToSend,
            type: session.role,
            device_id: deviceInfo.deviceId,
            device_type: deviceInfo.deviceType,
            status: hasNotificationPermission,
          };

          console.log("📱 Sending permission change payload:", payload);
          await client.post("/profile/add-token", payload);

          // Store updated values after successful API call
          if (hasNotificationPermission && expoPushToken) {
            storage.set(EXPO_PUSH_TOKEN_KEY, expoPushToken);
          } else {
            storage.delete(EXPO_PUSH_TOKEN_KEY);
          }
          storage.set(
            NOTIFICATION_PERMISSION_STATUS_KEY,
            currentPermissionStatus
          );
          storage.set(USER_ROLE_KEY, session.role);

          console.log("📱 Permission change updated successfully");
          return;
        }

        // Scenario 4: Token changed but permission status same (token refresh)
        if (!isFirstTimeSetup && tokenChanged && hasNotificationPermission) {
          console.log("📱 Scenario 4: Token refresh");

          const payload = {
            expoPushToken: expoPushToken,
            type: session.role,
            device_id: deviceInfo.deviceId,
            device_type: deviceInfo.deviceType,
            status: true,
          };

          console.log("📱 Sending token refresh payload:", payload);
          await client.post("/profile/add-token", payload);

          // Store new token after successful API call
          storage.set(EXPO_PUSH_TOKEN_KEY, expoPushToken);

          console.log("📱 Token refresh completed successfully");
          return;
        }

        // No action needed
        console.log(
          "📱 No notification update needed - all values are current"
        );
      } catch (error) {
        console.error("📱 Error sending notification update:", error);
      } finally {
        setIsProcessingRequest(false);
      }
    };

    // Add a small delay to prevent rapid successive calls
    const timeoutId = setTimeout(sendTokenToBackend, 200);
    return () => clearTimeout(timeoutId);
  }, [
    expoPushToken,
    session?.role,
    hasNotificationPermission,
    deviceInfo,
    isProcessingRequest,
  ]);
  if (isLoading) {
    return <Loader />;
  }

  return (
    <GestureHandlerRootView>
      {children}

      <NotificationPermissionBottomTab
        isVisible={isVisible}
        onClose={() => {
          setIsVisible(false);
        }}
      />
    </GestureHandlerRootView>
  );
}
