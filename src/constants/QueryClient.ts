// This file configures the React Query client with default options for queries.

import { QueryClient } from "@tanstack/react-query";

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 1000 * 60 * 5, // 5 minutes cache before considered stale
      refetchOnWindowFocus: false, // no refetch on app focus (mobile apps don’t have "window focus")
      refetchOnReconnect: true, // refetch when back online
      retry: 2, // retry failed queries up to 2 times
      retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000), // exponential backoff for retries
      refetchInterval: false, // disable polling by default
    },
  },
});

export default queryClient;
