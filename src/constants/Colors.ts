/**
 * Below are the colors that are used in the app. The colors are defined in the light and dark mode.
 */

type ColorScheme = {
  disabled: string;
  text: string;
  background: string;
  primary: string;
  secondary: string;
  thirdary: string;
  error: string;
  success: string;
  warning: string;
  info: string;
  green: string;
  orange: string;
  yellow: string;
  blue: string;
  gray: string;
  white: string;
  black: string;
  input: string;
  input_text: string;
  border: string;
  star: string;
  no_star: string;
  tab: string;
  inactive_tab: string;
  // Location type colors
  location_home: string;
  location_work: string;
  location_brand: string;
  // Notification type colors
  notification_review: string;
  notification_favorite: string;
  notification_recommendation: string;
  // Error/warning background
  error_background: string;
  // Overlay colors
  overlay: string;
  separator: string;
  // Product placeholder
  product_placeholder: string;
  // Shadow and modal colors
  shadow_color: string;
  modal_overlay: string;
  brand_info_background: string;
  // Border colors for bottom sheets
  bottom_sheet_border: string;
};

export type AppTheme = "light" | "dark";
export type AppThemeColors = Record<AppTheme, ColorScheme>;

// Helper function to safely get colors with fallback
export const getThemeColors = (theme?: AppTheme | null): ColorScheme => {
  const safeTheme = theme || "dark";
  return Colors[safeTheme];
};

export const Colors: AppThemeColors = {
  light: {
    disabled: "rgba(0, 0, 0, 0.15)",
    text: "#1C1C1C",
    background: "#F7F5FA",
    primary: "#2C3930",
    secondary: "#555",
    thirdary: "#DCD7C9",
    error: "#E64848",
    success: "#2AA876",
    warning: "#F4A100",
    info: "#2680EB",
    green: "#198754",
    orange: "#EC6C42",
    yellow: "#F5B700",
    blue: "#2196F3",
    gray: "#888",
    white: "#F7F5FA",
    black: "#1C1C1C",
    input: "#DCD7C9",
    input_text: "#3F4F44",
    border: "#555",
    star: "#3F4F44", // Added star color for light theme
    no_star: "#DCD7C9", // Added no_star color for light theme
    tab: "#555", // Added activeTab color for light theme
    inactive_tab: "#728370", // Added inActiveTab color for light theme
    // Location type colors
    location_home: "#4CAF50",
    location_work: "#2196F3",
    location_brand: "#FF9800",
    // Notification type colors
    notification_review: "#FFD700",
    notification_favorite: "#FF6B6B",
    notification_recommendation: "#4ECDC4",
    // Error/warning background
    error_background: "rgba(255, 107, 107, 0.05)",
    // Overlay colors
    overlay: "rgba(0, 0, 0, 0.5)",
    separator: "#E9E9E9",
    // Product placeholder
    product_placeholder: "#F5F5DC",
    // Shadow and modal colors
    shadow_color: "#000000",
    modal_overlay: "rgba(0, 0, 0, 0.4)",
    brand_info_background: "rgba(255, 149, 0, 0.1)",
    // Border colors for bottom sheets
    bottom_sheet_border: "rgba(255, 255, 255, 0.1)",
  },

  dark: {
    disabled: "rgba(255, 255, 255, 0.2)",
    text: "#F1F1F1",
    background: "#0E0E0E",
    primary: "#D0E7D2",
    secondary: "#CCCCCC",
    thirdary: "#DCD7C9",
    error: "#F28B82",
    success: "#81C995",
    warning: "#FFC107",
    info: "#82B1FF",
    green: "#4CAF50",
    orange: "#FF8A65",
    yellow: "#FFD54F",
    blue: "#64B5F6",
    gray: "#AAAAAA",
    white: "#F1F1F1",
    black: "#1C1C1C",
    input: "#2E2E2E",
    input_text: "#E0E0E0",
    border: "#3A3A3A",
    star: "#F5C518", // Yellowish for filled stars
    no_star: "#555555", // Dimmed for empty stars
    tab: "#555", // Active tab color
    inactive_tab: "#728370", // Muted green for inactive tab
    // Location type colors
    location_home: "#81C995",
    location_work: "#64B5F6",
    location_brand: "#FF8A65",
    // Notification type colors
    notification_review: "#FFD54F",
    notification_favorite: "#F28B82",
    notification_recommendation: "#82B1FF",
    // Error/warning background
    error_background: "rgba(242, 139, 130, 0.1)",
    // Overlay colors
    overlay: "rgba(0, 0, 0, 0.7)",
    separator: "#3A3A3A",
    // Product placeholder
    product_placeholder: "#2E2E2E",
    // Shadow and modal colors
    shadow_color: "#000000",
    modal_overlay: "rgba(0, 0, 0, 0.7)",
    brand_info_background: "rgba(255, 138, 101, 0.15)",
    // Border colors for bottom sheets
    bottom_sheet_border: "rgba(255, 255, 255, 0.05)",
  },
};
