import { CartContextType, CartItem } from "@/src/types";
import React, {
  createContext,
  useCallback,
  useContext,
  useEffect,
  useState,
} from "react";
import { MMKV } from "react-native-mmkv";

const storage = new MMKV();
const CART_KEY = "cart_items";

const CartContext = createContext<CartContextType | undefined>(undefined);

export function CartProvider({ children }: { children: React.ReactNode }) {
  const [cartItems, setCartItems] = useState<CartItem[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // Load cart from MMKV on mount
  useEffect(() => {
    try {
      const savedCart = storage.getString(CART_KEY);
      if (savedCart) {
        const parsedCart = JSON.parse(savedCart) as CartItem[];
        setCartItems(parsedCart);
      }
    } catch (error) {
      console.error("Error loading cart from MMKV:", error);
      setCartItems([]);
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Save cart to MMKV whenever cartItems changes
  useEffect(() => {
    if (!isLoading) {
      try {
        storage.set(CART_KEY, JSON.stringify(cartItems));
      } catch (error) {
        console.error("Error saving cart to MMKV:", error);
      }
    }
  }, [cartItems, isLoading]);

  // Check if product can be added to cart (brand validation)
  const canAddToCart = useCallback(
    (productBrandId: string): { canAdd: boolean; reason?: string } => {
      if (cartItems.length === 0) {
        return { canAdd: true };
      }

      const existingBrandId = cartItems[0].brandId;
      if (existingBrandId !== productBrandId) {
        return {
          canAdd: false,
          reason: "different_brand",
        };
      }

      return { canAdd: true };
    },
    [cartItems]
  );

  // Add item to cart with brand validation
  const addToCart = useCallback(
    (
      product: {
        _id: string;
        name: string;
        description?: string;
        price: number;
        images: string[];
        brandId: string;
        brandName?: string;
      },
      skipValidation: boolean = false
    ) => {
      console.log("🛒 Adding to cart:", product.name, "ID:", product._id);

      // Check brand validation unless skipped
      if (!skipValidation) {
        const validation = canAddToCart(product.brandId);
        if (!validation.canAdd) {
          console.log("🛒 Cannot add to cart:", validation.reason);
          return { success: false, reason: validation.reason };
        }
      }

      setCartItems((prevItems) => {
        console.log("🛒 Current cart items:", prevItems.length);
        const existingItemIndex = prevItems.findIndex(
          (item) => item.id === product._id
        );

        if (existingItemIndex >= 0) {
          // Item already exists, increase quantity
          console.log(
            "🛒 Item exists, increasing quantity from",
            prevItems[existingItemIndex].quantity,
            "to",
            prevItems[existingItemIndex].quantity + 1
          );
          const updatedItems = [...prevItems];
          updatedItems[existingItemIndex] = {
            ...updatedItems[existingItemIndex],
            quantity: updatedItems[existingItemIndex].quantity + 1,
          };
          return updatedItems;
        } else {
          // New item, add to cart
          console.log("🛒 New item, adding to cart");
          const newCartItem: CartItem = {
            id: product._id,
            name: product.name,
            description: product.description,
            price: product.price,
            image: product.images[0] || "",
            quantity: 1,
            brandId: product.brandId,
            brandName: product.brandName,
          };
          return [...prevItems, newCartItem];
        }
      });

      return { success: true };
    },
    [canAddToCart]
  );

  // Remove item completely from cart
  const removeFromCart = useCallback((productId: string) => {
    console.log("🛒 Removing item from cart:", productId);
    setCartItems((prevItems) => {
      const filteredItems = prevItems.filter((item) => item.id !== productId);
      console.log("🛒 Items after removal:", filteredItems.length);
      return filteredItems;
    });
  }, []);

  // Update item quantity
  const updateQuantity = useCallback(
    (productId: string, quantity: number) => {
      if (quantity <= 0) {
        removeFromCart(productId);
        return;
      }

      setCartItems((prevItems) =>
        prevItems.map((item) =>
          item.id === productId ? { ...item, quantity } : item
        )
      );
    },
    [removeFromCart]
  );

  // Increase quantity by 1
  const increaseQuantity = useCallback((productId: string) => {
    setCartItems((prevItems) =>
      prevItems.map((item) =>
        item.id === productId ? { ...item, quantity: item.quantity + 1 } : item
      )
    );
  }, []);

  // Decrease quantity by 1
  const decreaseQuantity = useCallback((productId: string) => {
    console.log("🛒 Decreasing quantity for:", productId);
    setCartItems((prevItems) => {
      const updatedItems = prevItems
        .map((item) => {
          if (item.id === productId) {
            const newQuantity = item.quantity - 1;
            console.log(`🛒 New quantity for ${productId}:`, newQuantity);
            return newQuantity > 0 ? { ...item, quantity: newQuantity } : item;
          }
          return item;
        })
        .filter((item) => item.quantity > 0);
      console.log("🛒 Items after decrease:", updatedItems.length);
      return updatedItems;
    });
  }, []);

  // Clear entire cart
  const clearCart = useCallback(() => {
    console.log("🛒 Clearing cart...");
    setCartItems([]);
    // Also clear from MMKV storage
    try {
      storage.delete(CART_KEY);
      console.log("🛒 Cart cleared from MMKV");
    } catch (error) {
      console.error("Error clearing cart from MMKV:", error);
    }
  }, []);

  // Refresh cart from MMKV
  const refreshCart = useCallback(() => {
    console.log("🛒 Refreshing cart from MMKV...");
    try {
      const savedCart = storage.getString(CART_KEY);
      if (savedCart) {
        const parsedCart = JSON.parse(savedCart) as CartItem[];
        console.log("🛒 Loaded from MMKV:", parsedCart.length, "items");
        setCartItems(parsedCart);
      } else {
        console.log("🛒 No cart data in MMKV");
        setCartItems([]);
      }
    } catch (error) {
      console.error("Error refreshing cart from MMKV:", error);
      setCartItems([]);
    }
  }, []);

  // Get item quantity
  const getItemQuantity = useCallback(
    (productId: string): number => {
      const item = cartItems.find((item) => item.id === productId);
      return item?.quantity || 0;
    },
    [cartItems]
  );

  // Calculate totals
  const subtotal = cartItems.reduce(
    (total, item) => total + item.price * item.quantity,
    0
  );

  const totalItems = cartItems.reduce(
    (total, item) => total + item.quantity,
    0
  );

  const shipping = 0; // Free shipping
  const total = subtotal + shipping;

  const value: CartContextType = {
    cartItems,
    isLoading,
    addToCart,
    canAddToCart,
    removeFromCart,
    updateQuantity,
    increaseQuantity,
    decreaseQuantity,
    clearCart,
    refreshCart,
    getItemQuantity,
    subtotal,
    total,
    totalItems,
    shipping,
    isEmpty: cartItems.length === 0,
  };

  return <CartContext.Provider value={value}>{children}</CartContext.Provider>;
}

// Hook to use cart context
export function useCartContext() {
  const context = useContext(CartContext);
  if (context === undefined) {
    throw new Error("useCartContext must be used within a CartProvider");
  }
  return context;
}

// Keep the original useCart hook for backward compatibility
export function useCart() {
  const context = useContext(CartContext);
  if (context) {
    return context;
  }

  // Fallback to original implementation if not in context
  throw new Error("useCart must be used within a CartProvider");
}
