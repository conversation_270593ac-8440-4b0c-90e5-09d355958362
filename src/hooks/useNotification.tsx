import { DeviceInfo } from "@/src/types";
import * as Application from "expo-application";
import Constants from "expo-constants";
import * as Device from "expo-device";
import * as Notifications from "expo-notifications";
import { useEffect, useState } from "react";
import { Platform } from "react-native";
import { Colors } from "../constants/Colors";

Notifications.setNotificationHandler({
  handleNotification: async (notification) => ({
    shouldPlaySound: true,
    shouldSetBadge: true,
    shouldShowBanner: true,
    shouldShowList: true,
    sound: notification.request.content.sound || "alert.wav",
  }),
});
export function useNotification() {
  const [expoPushToken, setExpoPushToken] = useState<string | null>(null);
  const [hasNotificationPermission, setHasNotificationPermission] =
    useState<boolean>(true);
  const [canAskAgain, setCanAskAgain] = useState<boolean>(true);
  const [deviceInfo, setDeviceInfo] = useState<DeviceInfo | null>(null);

  useEffect(() => {
    const getDeviceId = async () => {
      if (Platform.OS === "android") {
        return Application.getAndroidId() || "Unknown Android ID";
      } else if (Platform.OS === "ios") {
        return (await Application.getIosIdForVendorAsync()) || "Unknown iOS ID";
      }
      return "Unknown Device ID";
    };

    const initializeDeviceInfo = async () => {
      const deviceId = await getDeviceId();
      const deviceType = Device.osName || "Unknown OS";
      setDeviceInfo({ deviceId, deviceType });
    };

    const setup = async () => {
      if (Platform.OS === "android") {
        await Notifications.setNotificationChannelAsync("default", {
          name: "Default Channel",
          importance: Notifications.AndroidImportance.MAX,
          vibrationPattern: [0, 250, 250, 250],
          lightColor: Colors.light.input_text,
          sound: "alert.wav",
        });
      }

      if (Device.isDevice) {
        const { status: existingStatus, canAskAgain } =
          await Notifications.getPermissionsAsync();

        setCanAskAgain(canAskAgain);

        let finalStatus = existingStatus;
        if (existingStatus !== "granted") {
          const { status, canAskAgain: newCanAskAgain } =
            await Notifications.requestPermissionsAsync();
          finalStatus = status;
          setCanAskAgain(newCanAskAgain);
        }

        setHasNotificationPermission(finalStatus === "granted");

        if (finalStatus === "granted") {
          try {
            const projectId =
              Constants?.expoConfig?.extra?.eas?.projectId ??
              Constants?.easConfig?.projectId;
            if (!projectId) throw new Error("Project ID not found");

            const tokenResponse = await Notifications.getExpoPushTokenAsync({
              projectId,
            });

            setExpoPushToken(tokenResponse.data);
          } catch (e: any) {
            console.warn("Error getting push token:", e);
            setExpoPushToken(null);
          }
        }
      } else {
        console.log("Must use physical device for Push Notifications");
      }
    };

    initializeDeviceInfo();
    setup();
  }, []);

  return {
    hasNotificationPermission,
    expoPushToken,
    deviceInfo,
    canAskAgain,
  };
}
