import * as ImagePicker from "expo-image-picker";
import { useState } from "react";

export function useImagePicker() {
  const [imageUri, setImageUri] = useState<string>("");
  const [imageUris, setImageUris] = useState<string[]>([]);
  const [showPermissionUI, setShowPermissionUI] = useState(false);

  const pickImage = async () => {
    // Check current permission status
    const { status, canAskAgain } =
      await ImagePicker.getMediaLibraryPermissionsAsync();
    if (status === "granted") {
      // Permission granted, launch picker
      await openPicker();
    } else if (status === "undetermined" && canAskAgain) {
      // Request permission if possible
      const { status: newStatus } =
        await ImagePicker.requestMediaLibraryPermissionsAsync();
      if (newStatus === "granted") {
        await openPicker();
      } else {
        // User denied permission, show UI
        setShowPermissionUI(true);
      }
    } else {
      // Permission denied permanently, show UI
      setShowPermissionUI(true);
    }
  };

  const pickImages = async () => {
    // Check current permission status
    const { status, canAskAgain } =
      await ImagePicker.getMediaLibraryPermissionsAsync();
    if (status === "granted") {
      // Permission granted, launch picker for multiple images
      await openMultiplePicker();
    } else if (status === "undetermined" && canAskAgain) {
      // Request permission if possible
      const { status: newStatus } =
        await ImagePicker.requestMediaLibraryPermissionsAsync();
      if (newStatus === "granted") {
        await openMultiplePicker();
      } else {
        // User denied permission, show UI
        setShowPermissionUI(true);
      }
    } else {
      // Permission denied permanently, show UI
      setShowPermissionUI(true);
    }
  };

  const openPicker = async () => {
    const result = await ImagePicker.launchImageLibraryAsync({
      allowsEditing: true,
      aspect: [1, 1],
      quality: 0.8,
    });

    if (!result.canceled) {
      setImageUri(result.assets[0].uri);
    }
  };

  const openMultiplePicker = async () => {
    const result = await ImagePicker.launchImageLibraryAsync({
      allowsMultipleSelection: true,
      quality: 0.8,
      aspect: [1, 1],
      allowsEditing: false, // Disable editing for multiple selection
    });

    if (!result.canceled) {
      const newUris = result.assets.map((asset) => asset.uri);
      setImageUris((prevUris) => [...prevUris, ...newUris]);
    }
  };

  const removeImage = (uri: string) => {
    setImageUris((prevUris) => prevUris.filter((imageUri) => imageUri !== uri));
  };

  const clearImages = () => {
    setImageUris([]);
  };

  const closeUI = () => setShowPermissionUI(false);

  return {
    imageUri,
    imageUris,
    pickImage,
    pickImages,
    removeImage,
    clearImages,
    showPermissionUI,
    closeUI,
    setImageUris,
  };
}
