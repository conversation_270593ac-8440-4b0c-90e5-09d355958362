import * as Location from "expo-location";
import { useEffect, useRef, useState } from "react";

import { LocationData, MapRegion } from "@/src/types";

export function useLocation() {
  const [selectedLocation, setSelectedLocation] = useState<LocationData | null>(
    null
  );
  const [showPermissionUI, setShowPermissionUI] = useState(false);
  const [hasLocationPermission, setHasLocationPermission] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [isInitializing, setIsInitializing] = useState(true);
  const [mapRegion, setMapRegion] = useState<MapRegion>({
    latitude: 33.8869, // Bangalore coordinates as default fallback
    longitude: 9.5375,
    latitudeDelta: 0.0922,
    longitudeDelta: 0.0421,
  });

  // Debouncing ref to prevent multiple rapid calls
  const lastReverseGeocodeCall = useRef<number>(0);

  // Check permission status and get current location on mount
  useEffect(() => {
    initializeLocation();
  }, []);

  const initializeLocation = async () => {
    try {
      setIsInitializing(true);
      // Check current permission status
      const { status } = await Location.getForegroundPermissionsAsync();
      const hasPermission = status === "granted";
      setHasLocationPermission(hasPermission);

      if (hasPermission) {
        // If we have permission, silently get current location for map region
        await fetchCurrentLocationSilently();
      }
      // If no permission, keep the default Bangalore coordinates
    } catch (error) {
      console.error("Error initializing location:", error);
      setHasLocationPermission(false);
    } finally {
      setIsInitializing(false);
    }
  };

  const checkLocationPermission = async () => {
    try {
      const { status } = await Location.getForegroundPermissionsAsync();
      const hasPermission = status === "granted";
      setHasLocationPermission(hasPermission);
    } catch (error) {
      console.error("Error checking location permission:", error);
      setHasLocationPermission(false);
    }
  };

  // Silent location fetch for initial map positioning (doesn't set selectedLocation)
  const fetchCurrentLocationSilently = async () => {
    try {
      const location = await Location.getCurrentPositionAsync({
        accuracy: Location.Accuracy.Balanced,
      });

      const { latitude, longitude } = location.coords;

      // Update map region to user's current location
      setMapRegion({
        latitude,
        longitude,
        latitudeDelta: 0.0922,
        longitudeDelta: 0.0421,
      });
    } catch (error) {
      console.error("Error getting current location silently:", error);
      // Keep default region if location fetch fails
    }
  };

  const getCurrentLocation = async () => {
    setIsLoading(true);

    // Check current permission status
    const { status, canAskAgain } =
      await Location.getForegroundPermissionsAsync();

    if (status === "granted") {
      // Permission granted, get location
      setHasLocationPermission(true);
      await fetchCurrentLocation();
    } else if (status === "undetermined" && canAskAgain) {
      // Request permission if possible
      const { status: newStatus } =
        await Location.requestForegroundPermissionsAsync();
      if (newStatus === "granted") {
        setHasLocationPermission(true);
        setShowPermissionUI(false);
        await fetchCurrentLocation();
      } else {
        // User denied permission, show UI
        setHasLocationPermission(false);
        setShowPermissionUI(true);
        setIsLoading(false);
      }
    } else {
      // Permission denied permanently, show UI
      setHasLocationPermission(false);
      setShowPermissionUI(true);
      setIsLoading(false);
    }
  };

  const fetchCurrentLocation = async () => {
    try {
      const location = await Location.getCurrentPositionAsync({
        accuracy: Location.Accuracy.Balanced,
      });

      const { latitude, longitude } = location.coords;

      // Update map region
      setMapRegion({
        latitude,
        longitude,
        latitudeDelta: 0.0922,
        longitudeDelta: 0.0421,
      });

      // Get address details via reverse geocoding
      await reverseGeocode(latitude, longitude);
    } catch (error) {
      console.error("Error getting current location:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const selectLocationFromMap = async (latitude: number, longitude: number) => {
    // Only allow map selection if we have permission
    if (!hasLocationPermission) {
      setShowPermissionUI(true);
      return;
    }

    setIsLoading(true);

    // Update map region
    setMapRegion({
      ...mapRegion,
      latitude,
      longitude,
    });

    // Get address details via reverse geocoding
    await reverseGeocode(latitude, longitude);
  };

  const reverseGeocode = async (latitude: number, longitude: number) => {
    // Debouncing: prevent multiple calls within 1 second
    const now = Date.now();
    if (now - lastReverseGeocodeCall.current < 1000) {
      console.log("Reverse geocoding call debounced");
      setIsLoading(false);
      return;
    }
    lastReverseGeocodeCall.current = now;

    try {
      const reverseGeocode = await Location.reverseGeocodeAsync({
        latitude,
        longitude,
      });

      if (reverseGeocode.length > 0) {
        const location = reverseGeocode[0];
        const locationData: LocationData = {
          address:
            `${location.street || ""} ${location.streetNumber || ""}`.trim() ||
            "Address not found",
          latitude,
          longitude,
          city: location.city || "City not found",
          state: location.region || "State not found",
          country: location.country || "Country not found",
          zipCode: location.postalCode || "Zip Code not found",
        };

        setSelectedLocation(locationData);
      }
    } catch (error) {
      console.error("Error reverse geocoding:", error);
      // Set basic location data even if reverse geocoding fails
      setSelectedLocation({
        address: "Address not found",
        latitude,
        longitude,
        city: "",
        state: "",
        country: "",
        zipCode: "",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const clearLocation = () => {
    setSelectedLocation(null);
  };

  const closePermissionUI = () => {
    setShowPermissionUI(false);
    // Don't automatically recheck permission here to avoid reopening the UI
  };

  const updateMapRegion = (region: MapRegion) => {
    setMapRegion(region);
  };

  // Function to manually request permission (can be called from permission UI)
  const requestLocationPermission = async () => {
    try {
      const { status } = await Location.requestForegroundPermissionsAsync();
      const hasPermission = status === "granted";
      setHasLocationPermission(hasPermission);

      if (hasPermission) {
        setShowPermissionUI(false);
        // After granting permission, get current location for map
        await fetchCurrentLocationSilently();
      }

      return hasPermission;
    } catch (error) {
      console.error("Error requesting location permission:", error);
      return false;
    }
  };

  return {
    selectedLocation,
    setSelectedLocation,
    mapRegion,
    isLoading,
    isInitializing,
    showPermissionUI,
    hasLocationPermission,
    getCurrentLocation,
    selectLocationFromMap,
    clearLocation,
    closePermissionUI,
    updateMapRegion,
    requestLocationPermission,
    checkLocationPermission,
  };
}
