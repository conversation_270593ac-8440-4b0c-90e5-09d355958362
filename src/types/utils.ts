import { AppTheme } from "@/src/constants/Colors";

// Utility types

// Hook state type
export type UseStateHook<T> = [[boolean, T | null], (value: T | null) => void];

// Theme types
export type ThemeType = AppTheme | "system";

export interface ThemeContextType {
  theme: ThemeType;
  setTheme: (theme: ThemeType) => void;
  currentTheme: AppTheme;
}

// Language context type
export interface LanguageContextType {
  locale: string;
  setLocale: (locale: string) => void;
  t: (scope: string, options?: object) => string;
}

// Map region interface
export interface MapRegion {
  latitude: number;
  longitude: number;
  latitudeDelta: number;
  longitudeDelta: number;
}

// Device info interface for notifications
export interface DeviceInfo {
  deviceId: string;
  deviceType: string;
}
