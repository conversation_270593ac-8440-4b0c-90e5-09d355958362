import { ILocation } from "./location";
import { IOrderProduct } from "./product";

// Order related types

export type OrderStatus = 
  | "Pending"
  | "Confirmed" 
  | "Accepted"
  | "Delivered"
  | "Cancelled";

// Main Order interface
export interface IOrder {
  _id: string;
  products?: IOrderProduct[];
  client?: IClient;
  brand?: IOrderBrand;
  location?: ILocation;
  orderStatus?: OrderStatus;
  totalPrice?: number;
  orderDate?: string; // ISO string
  createdAt?: string;
  updatedAt?: string;
  __v?: number;
}

// Client interface in order context
export interface IClient {
  _id: string;
  name?: string;
  email?: string;
}

// Brand interface in order context
export interface IOrderBrand {
  _id: string;
  name?: string;
  logo?: string;
}

// Order card props interface
export interface OrderCardProps {
  item: {
    id: string;
    brand: string;
    name: string;
    phone: string;
    product: string;
    status: OrderStatus;
  };
}
