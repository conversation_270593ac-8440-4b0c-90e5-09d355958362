import { ILocation } from "./location";

// Component props types

// Bottom sheet base props
export interface BottomSheetProps {
  isVisible: boolean;
  onClose: () => void;
}

// Login bottom sheet props
export interface BottomSheetLoginProps extends BottomSheetProps {
  type: "signup" | "signin";
}

// Add review bottom sheet props
export interface BottomSheetAddReviewProps extends BottomSheetProps {
  id: string;
  productId?: string;
}

// Address type selector props
export interface AddressType {
  type: ILocation["type"];
  icon: string;
  title: string;
  description: string;
}

export interface AddressTypeSelectorProps extends BottomSheetProps {
  selectedLocation: SelectedLocationData | null;
}

// Map location picker props
export interface SelectedLocationData {
  address: string;
  latitude: number;
  longitude: number;
  city: string;
  state: string;
  country: string;
  zipCode: string;
}

export interface MapLocationPickerProps extends BottomSheetProps {
  onLocationSelect: (location: SelectedLocationData) => void;
}

// Address bottom sheet props
export interface AddressBottomSheetProps extends BottomSheetProps {
  onAddressSelect: (address: ILocation) => void;
  onAddNewAddress?: () => void;
}

// Edit location bottom sheet props
export interface EditLocationBottomSheetProps extends BottomSheetProps {
  location: ILocation | null;
  onLocationUpdate: (updatedLocation: ILocation) => void;
}

// Brand bottom sheet props
export interface BrandBottomSheetProps extends BottomSheetProps {
  onBrandSelect: (brandId: string, brandName: string) => void;
}

// Dashboard metric card props
export interface MetricCardProps {
  title: string;
  value: string;
  change: string;
  isPositive?: boolean;
}

// Dashboard product card props
export interface ProductCardProps {
  name: string;
  sales: string;
  color: string;
}
