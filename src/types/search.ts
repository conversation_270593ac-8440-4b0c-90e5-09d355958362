// Search related types

// Search brand interface
export interface SearchBrand {
  id: string;
  type: "brand";
  image: string;
  title: string;
  description: string;
  productCount: number;
}

// Search product interface
export interface SearchProduct {
  id: string;
  type: "product";
  image: string;
  title: string;
  price: string;
  brand: string;
}

// Union type for search results
export type SearchResultItem = SearchBrand | SearchProduct;

// Search result interface from API
export interface ISearchResult {
  products: ISearchProduct[];
  brands: ISearchBrand[];
}

// API search brand interface
export interface ISearchBrand {
  id: string;
  type: string;
  image: string;
  title: string;
  description: string;
  productCount: number;
}

// API search product interface
export interface ISearchProduct {
  id: string;
  type: string;
  image: string;
  title: string;
  price: string;
  brand: string;
}

// Search results component props
export interface SearchResultsProps {
  searchQuery: string;
  visible: boolean;
  isFullScreen?: boolean;
}
