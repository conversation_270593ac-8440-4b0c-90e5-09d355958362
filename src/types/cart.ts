// Cart related types

// Cart item interface
export interface CartItem {
  id: string;
  name: string;
  description?: string;
  price: number;
  image: string;
  quantity: number;
  brandId: string;
  brandName?: string;
}

// Product interface for cart operations
export interface CartProduct {
  _id: string;
  name: string;
  description?: string;
  price: number;
  images: string[];
  brandId: string;
  brandName?: string;
}

// Cart context type interface
export interface CartContextType {
  cartItems: CartItem[];
  isLoading: boolean;
  addToCart: (
    product: CartProduct,
    skipValidation?: boolean
  ) => {
    success: boolean;
    reason?: string;
  };
  canAddToCart: (productBrandId: string) => {
    canAdd: boolean;
    reason?: string;
  };
  removeFromCart: (productId: string) => void;
  updateQuantity: (productId: string, quantity: number) => void;
  increaseQuantity: (productId: string) => void;
  decreaseQuantity: (productId: string) => void;
  clearCart: () => void;
  refreshCart: () => void;
  getItemQuantity: (productId: string) => number;
  subtotal: number;
  total: number;
  totalItems: number;
  shipping: number;
  isEmpty: boolean;
}

// Cart details bottom sheet props
export interface CartDetailsBottomSheetProps {
  isVisible: boolean;
  onClose: () => void;
}
