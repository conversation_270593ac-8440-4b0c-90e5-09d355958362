export interface DashboardAnalytics {
  overviewMetrics: {
    totalOrders: number;
    delivered: number;
    pending: number;
  };
  monthlyOrdersData: Array<{
    value: number;
    label: string; // Month name (Jan, Feb, etc.)
  }>;
  orderStatusData: Array<{
    value: number;
    color: string; // Hex color code
    text: string;
    label: string; // Status name
  }>;
  deliveredOrdersData: Array<{
    value: number;
    label: string; // Month name
    frontColor: string; // Hex color code
  }>;
  mostOrderedProductsData: Array<{
    value: number; // Total quantity ordered
    label: string; // Product name
    frontColor: string; // Hex color code
  }>;
  orderStatistics: {
    completedOrders: number;
    cancelledOrders: number;
  };
  productPerformance: Array<{
    name: string;
    sales: string; // e.g., "50 Units sold"
    color: string; // Hex color code
  }>;
  customerEngagement: {
    activeCustomers: number;
    newCustomers: number;
  };
}

export interface DashboardAnalyticsResponse {
  ok: boolean;
  status: string;
  message: string;
  data: DashboardAnalytics;
}
