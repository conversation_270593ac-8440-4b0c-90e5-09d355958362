// Product related types

// Base Product interface for client queries
export interface IProduct {
  _id: string;
  name: string;
  images: string[];
  price?: number;
  description?: string;
  brand?: {
    _id: string;
    name?: string;
  };
}

// Client specific product interface
export interface IClientProduct extends IProduct {
  price: number;
}

// Vendor specific product interface
export interface IVendorProduct extends IProduct {
  brand: {
    _id: string;
    name: string;
  };
}

// Product in order context
export interface IOrderProduct {
  product: {
    _id: string;
    name?: string;
    price?: number;
    images?: string[];
    brand?: {
      _id: string;
      name?: string;
    };
  };
  quantity: number;
}

// Product form values for Add form
export interface ProductFormValues {
  name: string;
  description: string;
  category: string;
  brand: string;
  price: string;
  images: string[];
}

// Product form values for Edit form
export interface EditProductFormValues extends ProductFormValues {
  id: string;
}

// Product interface used in cart context
export interface Product {
  _id: string;
  name: string;
  description?: string;
  price: number;
  images: string[];
}
