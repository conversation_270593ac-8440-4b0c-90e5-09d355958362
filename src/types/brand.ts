// Brand related types

// Base Brand interface for client queries
export interface IBrand {
  _id: string;
  name: string;
  logo: string;
  description?: string;
  numberOfProducts?: number; // For vendor brands
}

// Vendor specific brand interface
export interface IVendorBrand extends IBrand {
  numberOfProducts: number;
}

// Client specific brand interface
export interface IClientBrand extends IBrand {
  description: string;
}

// Brand form values for Add/Edit forms
export interface BrandFormValues {
  name: string;
  description: string;
  logo: string;
  email: string;
  phone: string;
  location: LocationData;
  category: string;
}

// Location data interface used in brand forms
export interface LocationData {
  address: string;
  latitude?: number;
  longitude?: number;
  city: string;
  state: string;
  country: string;
  zipCode: string;
}
