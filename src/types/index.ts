// Main types index - exports all types from the types folder

// Profile interface
export interface IProfile {
  name: string;
  email: string;
  password: string;
  phone?: string;
  type: "client" | "hotel" | "admin";
  isVerified: boolean;
  createdAt: Date;
  user_id: string;
  isPhoneVerified: boolean;
  expoPushToken: string;
  blocked: boolean;
  source: string;
  notification: boolean;
  emailNotification: boolean;
  bookingUpdate: boolean;
  newMessage: boolean;
  marketing: boolean;
}

// Re-export all types from other files
export * from "./api";
export * from "./auth";
export * from "./brand";
export * from "./cart";
export * from "./components";
export * from "./dashboard";
export * from "./location";
export * from "./notifications";
export * from "./order";
export * from "./product";
export * from "./search";
export * from "./utils";
