// Authentication related types

export type UserRole = "client" | "vendor" | null;

export interface SessionData {
  token: string;
  role: UserRole;
  userId: string;
  name: string;
}

export interface AuthContextType {
  signIn: (sessionData: SessionData) => void;
  signOut: () => void;
  session?: SessionData | null;
  isLoading: boolean;
}

// Signup mutation parameters
export interface SignupParams {
  fullName: string;
  email: string;
  password: string;
  type: "client" | "vendor";
}

// Login mutation parameters
export interface LoginParams {
  email: string;
  password: string;
  type: string;
}

// Forget password parameters
export interface ForgetPasswordParams {
  email: string;
  type: "client" | "vendor";
}
