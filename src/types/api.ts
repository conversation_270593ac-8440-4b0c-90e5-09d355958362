// API and Query related types
export interface MessagePage<T = any> {
  data: {
    items: T[];
    totalItems: number;
    currentPage: number;
    totalPages: number;
    hasNextPage: boolean;
    hasPreviousPage: boolean;
  };
}

// Brand API Payloads
export interface AddBrandPayload {
  name: string;
  description: string;
  logo: string;
  email: string;
  phone: string;
  location: {
    address: string;
    latitude?: number;
    longitude?: number;
    city: string;
    state: string;
    country: string;
    zipCode: string;
  };
  category: string;
}

export interface EditBrandPayload extends AddBrandPayload {
  id: string;
}

// Product API Payloads
export interface AddProductPayload {
  name: string;
  description: string;
  category: string;
  brand: string;
  price: string;
  images: string[];
}

export interface EditProductPayload extends AddProductPayload {
  id: string;
}

// Category types
export interface ICategory {
  _id: string;
  id: string;
  name: string;
  subcategories: string[];
}
