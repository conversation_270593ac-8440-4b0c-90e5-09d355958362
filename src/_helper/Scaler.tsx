import { Dimensions, PixelRatio, Platform } from "react-native";

const { width: SCREEN_WIDTH, height: SCREEN_HEIGHT } = Dimensions.get("window");

// Base design dimensions
const BASE_WIDTH = 390;
const BASE_HEIGHT = 844;

// Calculate width and height scale factors
const scaleFactorWidth = SCREEN_WIDTH / BASE_WIDTH;
const scaleFactorHeight = SCREEN_HEIGHT / BASE_HEIGHT;

// Combined scale: average of width and height scales
const combinedScaleFactor = (scaleFactorWidth + scaleFactorHeight) / 2;

// Native combined scale function with precision rounding
const scaleNative = (size: number) => {
  const scaledValue = size * combinedScaleFactor;
  // Round to 2 decimal places to avoid precision issues with Reanimated
  return Math.round(scaledValue * 100) / 100;
};

// Font scaling using combined scale and PixelRatio rounding
const scaleFontNative = (size: number) =>
  Math.round(PixelRatio.roundToNearestPixel(scaleNative(size)));

// Web fallback (no scaling)
const identity = (size: number) => size;

// Helper function for properties that must be integers (zIndex, elevation)
const scaleInteger = (size: number) => {
  const scaledValue = Platform.OS === "web" ? size : scaleNative(size);
  return Math.round(scaledValue);
};

// Export unified scale and font scale
export const scale = Platform.OS === "web" ? identity : scaleNative;
export const scaleFont = Platform.OS === "web" ? identity : scaleFontNative;
export const scaleInt = scaleInteger;
