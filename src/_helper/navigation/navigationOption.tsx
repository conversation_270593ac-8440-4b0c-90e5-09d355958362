import { scaleFont } from "@/src/_helper/Scaler";
import CustomBackButton from "@/src/components/buttons/CustomBackButton";
import { Colors } from "@/src/constants/Colors";

export const getDefaultHeaderOptions = (
  theme: "light" | "dark" | null = "dark"
) => ({
  headerTitleStyle: {
    fontFamily: "TintBold",
    color: Colors[theme ?? "dark"].text,
    fontSize: scaleFont(18),
  },
  headerStyle: {
    backgroundColor: Colors[theme ?? "dark"].background,
  },
  headerLeft: () => <CustomBackButton />,
  headerTitleAlign: "center" as const,
  headerShown: true,
});
