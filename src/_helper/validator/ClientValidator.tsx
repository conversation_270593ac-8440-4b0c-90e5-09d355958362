import * as yup from "yup";
export const ReviewSchema = () =>
  yup.object().shape({
    comment: yup
      .string()
      .required("validation.required.comment")
      .min(1, "validation.comment.min")
      .max(500, "validation.comment.max"),
    rating: yup
      .number()
      .required("validation.required.rating")
      .min(1, "validation.rating.min")
      .max(5, "validation.rating.max"),
  });
