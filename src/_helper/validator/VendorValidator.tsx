import * as yup from "yup";

export const addBrandSchema = yup.object().shape({
  name: yup
    .string()
    .required("validation.required.brandName")
    .min(2, "validation.brandName.min"),
  description: yup
    .string()
    .required("validation.required.description")
    .min(10, "validation.description.min"),
  logo: yup.string().required("validation.required.logo"),
  email: yup
    .string()
    .required("validation.required.email")
    .email("validation.email.invalid"),
  phone: yup
    .string()
    .required("validation.required.phone")
    .matches(/^[+]?[\d\s\-()]+$/, "validation.phone.invalid"),
  location: yup
    .object()
    .shape({
      address: yup
        .string()
        .required("validation.required.address")
        .min(5, "validation.address.min"),
      latitude: yup.number().required("validation.required.latitude"),
      longitude: yup.number().required("validation.required.longitude"),
      city: yup
        .string()
        .required("validation.required.city")
        .min(2, "validation.city.min"),
      state: yup
        .string()
        .required("validation.required.state")
        .min(2, "validation.state.min"),
      country: yup
        .string()
        .required("validation.required.country")
        .min(2, "validation.country.min"),
      zipCode: yup
        .string()
        .required("validation.required.zipCode")
        .min(3, "validation.zipCode.min"),
    })
    .required("validation.required.location"),
  category: yup
    .string()
    .required("validation.required.category")
    .min(2, "validation.category.min"),
});

export const addProductSchema = yup.object().shape({
  name: yup.string().required("validation.required.productName"),
  description: yup.string().required("validation.required.description"),
  category: yup.string().required("validation.required.category"),
  brand: yup.string().required("validation.required.brand"),
  price: yup
    .number()
    .typeError("validation.price.typeError")
    .required("validation.required.price")
    .min(0, "validation.price.min"),

  images: yup
    .array()
    .min(1, "validation.images.min")
    .max(6, "validation.images.max")
    .required("validation.required.images"),
});
