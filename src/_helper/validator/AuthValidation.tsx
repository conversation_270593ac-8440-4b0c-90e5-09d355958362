import * as yup from "yup";

export const LoginSchema = () =>
  yup.object().shape({
    email: yup
      .string()
      .email("validation.email.invalid")
      .required("validation.required.email"),
    password: yup.string().required("validation.required.password"),
  });

export const ValidatePhone = () =>
  yup.object().shape({
    phone: yup
      .string()
      .required("validation.required.phone")
      .matches(/^[+]?[\d\s\-()]+$/, "validation.phone.invalid"),
  });

export const CodeValidation = () =>
  yup.object().shape({
    otp: yup
      .string()
      .required("validation.required.otp")
      .length(4, "validation.otp.length"),
  });

export const CodeToEnterValidation = () =>
  yup.object().shape({
    code: yup
      .string()
      .required("validation.required.code")
      .length(4, "validation.code.length"),
  });

export const ResetPasswordSchema = () =>
  yup.object().shape({
    password: yup
      .string()
      .min(8, "validation.password.min")
      .matches(/[A-Z]/, "validation.password.requirements")
      .matches(/[a-z]/, "validation.password.requirements")
      .matches(/[0-9]/, "validation.password.requirements")
      .matches(/[@$!%*?&]/, "validation.password.requirements")
      .required("validation.required.password"),
    confirmPassword: yup
      .string()
      .oneOf([yup.ref("password"), ""], "validation.password.matches")
      .required("validation.required.confirmPassword"),
  });

export const SignupSchema = () =>
  yup.object().shape({
    fullName: yup
      .string()
      .required("validation.required.fullName")
      .min(2, "validation.fullName.min")
      .max(50, "validation.fullName.max"),
    email: yup
      .string()
      .email("validation.email.invalid")
      .required("validation.required.email"),
    password: yup
      .string()
      .min(8, "validation.password.min")
      .matches(/[A-Z]/, "validation.password.requirements")
      .matches(/[a-z]/, "validation.password.requirements")
      .matches(/[0-9]/, "validation.password.requirements")
      .matches(/[@$!%*?&]/, "validation.password.requirements")
      .required("validation.required.password"),
    confirmPassword: yup
      .string()
      .oneOf([yup.ref("password"), ""], "validation.password.matches")
      .required("validation.required.confirmPassword"),
  });

export const ForgetPasswordSchema = () =>
  yup.object().shape({
    email: yup
      .string()
      .email("validation.email.invalid")
      .required("validation.required.email"),
  });

export const RoomSchema = () =>
  yup.object().shape({
    room: yup
      .string()
      .required("validation.required.room")
      .min(3, "validation.room.min"),
    message: yup
      .string()
      .required("validation.required.message")
      .min(1, "validation.message.min")
      .max(500, "validation.message.max"),
  });
