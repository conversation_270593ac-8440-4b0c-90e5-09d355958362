{"expo": {"name": "Locasa", "slug": "locasa", "version": "1.0.0", "orientation": "portrait", "icon": "./src/assets/images/icon.png", "scheme": "locasa", "userInterfaceStyle": "automatic", "newArchEnabled": true, "ios": {"supportsTablet": true, "bundleIdentifier": "com.locasa", "infoPlist": {"ITSAppUsesNonExemptEncryption": false, "NSAppTransportSecurity": {"NSAllowsArbitraryLoads": false, "NSExceptionDomains": {"**************": {"NSExceptionAllowsInsecureHTTPLoads": true, "NSIncludesSubdomains": true}}}, "NSPhotoLibraryUsageDescription": "Locasa needs access to your photo library to let you upload product or brand images.", "NSCameraUsageDescription": "<PERSON><PERSON><PERSON> uses your camera to capture media for your shop or profile.", "NSLocationWhenInUseUsageDescription": "Locasa uses your location to show relevant local brands and services near you.", "NSLocationAlwaysUsageDescription": "Locasa uses your location to continuously personalize your local discovery experience.", "NSUserNotificationUsageDescription": "Locasa sends notifications to keep you updated about offers, messages, and activities related to your account."}, "config": {"googleMapsApiKey": "AIzaSyAJHEBevis2G-hktl0hbd6MBoS_WQzuMBY"}}, "android": {"adaptiveIcon": {"foregroundImage": "./src/assets/images/adaptive-icon.png"}, "edgeToEdgeEnabled": true, "package": "com.locasa", "googleServicesFile": "./google-services.json", "permissions": ["CAMERA", "ACCESS_FINE_LOCATION", "ACCESS_COARSE_LOCATION", "READ_MEDIA_IMAGES", "POST_NOTIFICATIONS"], "config": {"googleMaps": {"apiKey": "AIzaSyAJHEBevis2G-hktl0hbd6MBoS_WQzuMBY"}}}, "web": {"bundler": "metro", "output": "static", "favicon": "./src/assets/images/favicon.png"}, "plugins": ["expo-router", ["expo-splash-screen", {"image": "./src/assets/images/splash-icon.png", "imageWidth": 200, "resizeMode": "contain"}], ["expo-notifications", {"icon": "./src/assets/images/icon.png", "color": "#ffffff", "defaultChannel": "default", "sounds": ["./src/assets/sounds/alert.wav"], "enableBackgroundRemoteNotifications": false, "iosDisplayInForeground": true}], ["expo-build-properties", {"ios": {"networkInspector": false}}], ["expo-image-picker", {"photosPermission": "$(PRODUCT_NAME) needs access to your photos so you can upload images of your shop, products, or stories.", "cameraPermission": "$(PRODUCT_NAME) needs access to your camera to let you take photos and videos directly from the app."}], ["expo-location", {"locationAlwaysAndWhenInUsePermission": "Allow $(PRODUCT_NAME) to access your location so we can show nearby local brands and personalized recommendations.", "locationWhenInUsePermission": "Allow $(PRODUCT_NAME) to use your location while you're using the app to explore local businesses around you."}], ["expo-maps", {"requestLocationPermission": true, "locationPermission": "Allow $(PRODUCT_NAME) to use your location so we show the map and autocomplete addresses when selecting your location."}], "expo-web-browser"], "experiments": {"typedRoutes": true}, "extra": {"router": {}, "eas": {"projectId": "4d40e98d-3d93-478c-8908-5228e3c0ca3e"}}, "owner": "ukps"}}