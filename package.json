{"name": "locasa", "main": "expo-router/entry", "version": "1.0.0", "scripts": {"start": "expo start", "dev": "expo start", "reset-project": "node ./scripts/reset-project.js", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web", "lint": "expo lint"}, "dependencies": {"@expo/vector-icons": "^14.1.0", "@react-native-community/netinfo": "^11.4.1", "@react-navigation/bottom-tabs": "^7.3.10", "@react-navigation/elements": "^2.3.8", "@react-navigation/native": "^7.1.6", "@shopify/flash-list": "1.7.6", "@tanstack/react-query": "^5.77.2", "axios": "^1.9.0", "expo": "53.0.15", "expo-application": "~6.1.5", "expo-blur": "~14.1.5", "expo-build-properties": "~0.14.8", "expo-constants": "~17.1.6", "expo-dev-client": "~5.2.2", "expo-device": "~7.1.4", "expo-font": "~13.3.1", "expo-haptics": "~14.1.4", "expo-image": "~2.3.2", "expo-image-picker": "^16.1.4", "expo-linking": "~7.1.6", "expo-location": "~18.1.6", "expo-maps": "~0.11.0", "expo-notifications": "~0.31.3", "expo-router": "~5.1.2", "expo-secure-store": "^14.2.3", "expo-splash-screen": "~0.30.9", "expo-status-bar": "~2.2.3", "expo-symbols": "~0.4.5", "expo-system-ui": "~5.0.9", "expo-web-browser": "~14.2.0", "formik": "^2.4.6", "i18n-js": "^4.5.1", "jwt-decode": "^4.0.0", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.4", "react-native-gesture-handler": "~2.24.0", "react-native-gifted-charts": "^1.4.61", "react-native-keyboard-controller": "^1.17.3", "react-native-maps": "1.20.1", "react-native-mmkv": "^3.2.0", "react-native-paper": "^5.14.5", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "^5.4.0", "react-native-screens": "~4.11.1", "react-native-toast-message": "^2.3.0", "react-native-web": "~0.20.0", "react-native-webview": "13.13.5", "yup": "^1.6.1", "react-native-svg": "15.11.2", "expo-linear-gradient": "~14.1.5"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "~19.0.10", "eslint": "^9.25.0", "eslint-config-expo": "~9.2.0", "typescript": "~5.8.3"}, "expo": {"doctor": {"reactNativeDirectoryCheck": {"exclude": ["jwt-decode"]}}}, "private": true}