import { router } from "expo-router";
import { Image, StyleSheet, View } from "react-native";

import { scale } from "@/src/_helper/Scaler";
import DefaultButton from "@/src/components/buttons/Default";
import { ThemedText } from "@/src/components/ui/ThemedText";
import { ThemedView } from "@/src/components/ui/ThemedView";
import { useLanguage } from "@/src/context/LanguageContext";

export default function NotFoundScreen() {
  const { t } = useLanguage();

  return (
    <ThemedView style={styles.container}>
      {/* Illustration */}
      <View style={styles.imageContainer}>
        <Image
          source={require("@/src/assets/ui/not_found.png")}
          style={styles.image}
          resizeMode="contain"
        />
      </View>

      {/* Title */}
      <ThemedText type="bold" style={styles.title} size={24}>
        {t("notFound.title")}
      </ThemedText>

      {/* Description */}
      <ThemedText style={styles.description}>
        {t("notFound.description")}
      </ThemedText>

      <DefaultButton
        title={t("notFound.goHome")}
        onPress={() => {
          router.replace("/");
        }}
      />
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingVertical: scale(20),
    marginTop: scale(35),
    backgroundColor: "transparent",
  },
  imageContainer: {
    marginBottom: scale(40),
    width: "100%",
  },
  image: {
    width: "100%",
    height: scale(200),
  },
  title: {
    textAlign: "center",
    marginBottom: scale(16),
  },
  description: {
    marginBottom: scale(40),
    paddingHorizontal: scale(20),
    opacity: 0.7,
    textAlign: "center",
  },
  link: {
    borderRadius: scale(8),
    overflow: "hidden",
  },
  button: {
    paddingVertical: scale(12),
    paddingHorizontal: scale(24),
    borderRadius: scale(8),
    minWidth: scale(120),
    alignItems: "center",
  },
  buttonText: {
    fontWeight: "600",
  },
});
