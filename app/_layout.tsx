import { MainContainer } from "@/src/components/containers/MainContainer";
import queryClient from "@/src/constants/QueryClient";
import { SessionProvider } from "@/src/context/AuthContext";
import { LanguageProvider } from "@/src/context/LanguageContext";
import { NavigationThemeWrapper } from "@/src/context/NavigationThemeProvider";
import { ThemeProviderContext } from "@/src/context/ThemeContext";
import useNotificationObserver from "@/src/hooks/useNotificationObserver";
import { QueryClientProvider } from "@tanstack/react-query";
import { useFonts } from "expo-font";
import { Slot } from "expo-router";
import * as SplashScreen from "expo-splash-screen";
import React from "react";
import { KeyboardProvider } from "react-native-keyboard-controller";
import "react-native-reanimated";
import Toast from "react-native-toast-message";
import NetworkContext from "../src/context/NetworkContext";
SplashScreen.preventAutoHideAsync();
export default function RootLayout() {
  useNotificationObserver();

  const [loaded] = useFonts({
    TintRegular: require("../src/assets/fonts/Inter_24pt-Regular.ttf"),
    TintSemiBold: require("../src/assets/fonts/Inter_24pt-SemiBold.ttf"),
    TintBold: require("../src/assets/fonts/Inter_24pt-Bold.ttf"),
  });
  React.useEffect(() => {
    if (loaded) {
      SplashScreen.hideAsync();
    }
  }, [loaded]);

  if (!loaded) {
    return null;
  }

  return (
    <ThemeProviderContext>
      <NavigationThemeWrapper>
        <QueryClientProvider client={queryClient}>
          <LanguageProvider>
            <KeyboardProvider>
              <NetworkContext>
                <SessionProvider>
                  <MainContainer>
                    <Slot />
                    <Toast />
                  </MainContainer>
                </SessionProvider>
              </NetworkContext>
            </KeyboardProvider>
          </LanguageProvider>
        </QueryClientProvider>
      </NavigationThemeWrapper>
    </ThemeProviderContext>
  );
}
