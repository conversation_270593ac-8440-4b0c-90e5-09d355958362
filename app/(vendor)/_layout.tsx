import NotificationPermission from "@/src/context/NotificationPermission";
import RoleProtectedLayout from "@/src/context/RoleProtectedLayout";
import { Stack } from "expo-router";
export default function AuthLayout() {
  return (
    <RoleProtectedLayout allowedRoles={["vendor"]}>
      <NotificationPermission>
        <Stack screenOptions={{ headerShown: false }}>
          <Stack.Screen name="(tabs)" />
          <Stack.Screen name="(settings)" />
          <Stack.Screen name="(screens)" />
        </Stack>
      </NotificationPermission>
    </RoleProtectedLayout>
  );
}
