import { scale, scaleFont } from "@/src/_helper/Scaler";
import Plus from "@/src/components/buttons/Plus";
import { Colors } from "@/src/constants/Colors";
import { useLanguage } from "@/src/context/LanguageContext";
import { useTheme } from "@/src/context/ThemeContext";
import { AntDesign, Feather, Octicons } from "@expo/vector-icons";
import Fontisto from "@expo/vector-icons/Fontisto";
import { DarkTheme } from "@react-navigation/native";
import { Tabs } from "expo-router";
import React from "react";
import { DefaultTheme, PaperProvider } from "react-native-paper";
export default function TabLayout() {
  const { currentTheme } = useTheme();
  const { t } = useLanguage();

  const paperTheme = currentTheme === "light" ? DefaultTheme : DarkTheme;
  return (
    <PaperProvider theme={paperTheme}>
      <Tabs
        screenOptions={({ route }) => ({
          tabBarActiveTintColor: Colors[currentTheme ?? "dark"].inactive_tab,
          tabBarInactiveTintColor: Colors[currentTheme ?? "dark"].tab,
          tabBarStyle: {
            backgroundColor: Colors[currentTheme ?? "dark"].background,
            display: "flex",
            height: scale(50),
            borderTopWidth: 0,
            elevation: 0,
            shadowOpacity: 0,
          },
          headerShown: false,
        })}
      >
        <Tabs.Screen
          name="index"
          options={{
            title: t("navigation.tabs.dashboard"),
            tabBarIcon: ({ color, focused }) => (
              <Fontisto
                name="home"
                color={color}
                size={focused ? scale(24) : scale(20)}
              />
            ),
          }}
        />
        <Tabs.Screen
          name="brands"
          options={{
            title: t("navigation.tabs.home"),
            headerShown: true,
            headerLeft: () => null,

            headerStyle: {
              backgroundColor: Colors[currentTheme ?? "dark"].background,
            },
            headerTitle: t("screens.headers.brands"),
            headerTintColor: Colors[currentTheme ?? "dark"].text,
            headerTitleAlign: "center",
            headerTitleStyle: {
              fontFamily: "TintBold",
              fontSize: scaleFont(18),
            },
            headerRight: () => <Plus />,
            tabBarIcon: ({ color, focused }) => (
              <Fontisto
                name="hashtag"
                color={color}
                size={focused ? scale(24) : scale(20)}
              />
            ),
          }}
        />
        <Tabs.Screen
          name="products"
          options={{
            title: t("navigation.tabs.products"),
            headerShown: false,
            tabBarIcon: ({ color, focused }) => (
              <Feather
                name="box"
                color={color}
                size={focused ? scale(24) : scale(20)}
              />
            ),
          }}
        />
        <Tabs.Screen
          name="orders"
          options={{
            title: t("navigation.tabs.orders"),
            headerShown: false,
            tabBarIcon: ({ color, focused }) => (
              <Octicons
                name="note"
                color={color}
                size={focused ? scale(24) : scale(20)}
              />
            ),
          }}
        />

        <Tabs.Screen
          name="settings"
          options={{
            title: t("navigation.tabs.settings"),
            headerShown: false,

            tabBarIcon: ({ color, focused }) => (
              <AntDesign
                name="setting"
                color={color}
                size={focused ? scale(24) : scale(20)}
              />
            ),
          }}
        />
      </Tabs>
    </PaperProvider>
  );
}
