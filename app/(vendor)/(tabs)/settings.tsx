import React from "react";
import { StyleSheet, View } from "react-native";

import { MainContainerForScreensWithoutHeader } from "@/src/components/containers/MainContainerForScreensWithoutHeader";
import { AccountSection } from "@/src/components/settings/AccountSection";
import { AppSection } from "@/src/components/settings/AppSection";
import { SupportSection } from "@/src/components/settings/SupportSection";
import { ProfileHeader } from "@/src/components/shared/ProfileHeader";

export default function Settings() {
  return (
    <MainContainerForScreensWithoutHeader>
      <ProfileHeader />
      <View style={styles.container}>
        <AccountSection role={"vendor"} />
        <AppSection role={"vendor"} />
        <SupportSection role={"vendor"} />
      </View>
    </MainContainerForScreensWithoutHeader>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});
