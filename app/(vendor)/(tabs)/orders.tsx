import { MainContainerForScreensWithoutHeader } from "@/src/components/containers/MainContainerForScreensWithoutHeader";
import OrdersHeader from "@/src/components/vendor/orders/OrderHeader";
import OrdersList from "@/src/components/vendor/orders/OrderList";
import React from "react";
type Order = {
  id: number;
  userName: string;
  product: string;
  brand: string;
  userAvatar: any;
};

export default function OrdersScreen() {
  return (
    <MainContainerForScreensWithoutHeader noScroll>
      <OrdersHeader />
      <OrdersList />
    </MainContainerForScreensWithoutHeader>
  );
}
