import { getDefaultHeaderOptions } from "@/src/_helper/navigation/navigationOption";
import { useLanguage } from "@/src/context/LanguageContext";
import { useTheme } from "@/src/context/ThemeContext";
import { Stack } from "expo-router";
import React from "react";

export default function SettingsLayout() {
  const { currentTheme } = useTheme();
  const { t } = useLanguage();

  return (
    <Stack screenOptions={getDefaultHeaderOptions(currentTheme)}>
      <Stack.Screen
        name="order-details"
        options={{ title: t("screens.headers.order-details") }}
      />
    </Stack>
  );
}
