import { getDefaultHeaderOptions } from "@/src/_helper/navigation/navigationOption";
import { useLanguage } from "@/src/context/LanguageContext";
import { useTheme } from "@/src/context/ThemeContext";
import { Stack } from "expo-router";
import React from "react";

export default function SettingsLayout() {
  const { currentTheme } = useTheme();
  const { t } = useLanguage();

  return (
    <Stack screenOptions={getDefaultHeaderOptions(currentTheme)}>
      <Stack.Screen
        name="product-details"
        options={{ title: t("screens.headers.product-details") }}
      />
      <Stack.Screen
        name="edit-product"
        options={{ title: t("screens.headers.edit-product") }}
      />
    </Stack>
  );
}
