import { getDefaultHeaderOptions } from "@/src/_helper/navigation/navigationOption";
import { useLanguage } from "@/src/context/LanguageContext";
import { useTheme } from "@/src/context/ThemeContext";
import { DarkTheme } from "@react-navigation/native";
import { Stack } from "expo-router";
import React from "react";
import { DefaultTheme, PaperProvider } from "react-native-paper";

export default function SettingsLayout() {
  const { currentTheme } = useTheme();
  const { t } = useLanguage();

  const paperTheme = currentTheme === "light" ? DefaultTheme : DarkTheme;
  return (
    <PaperProvider theme={paperTheme}>
      <Stack screenOptions={getDefaultHeaderOptions(currentTheme)}>
        <Stack.Screen name="brand" options={{ headerShown: false }} />
        <Stack.Screen name="order" options={{ headerShown: false }} />
        <Stack.Screen name="product" options={{ headerShown: false }} />
        <Stack.Screen
          name="notification"
          options={{
            headerTitle: t("screens.headers.notification"),
          }}
        />
      </Stack>
    </PaperProvider>
  );
}
