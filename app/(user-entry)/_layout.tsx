import { getDefaultHeaderOptions } from "@/src/_helper/navigation/navigationOption";
import HelpButton from "@/src/components/buttons/HelpButton";
import { useTheme } from "@/src/context/ThemeContext";
import { Stack } from "expo-router";

export default function AuthLayout() {
  const { currentTheme } = useTheme();

  return (
    <Stack
      screenOptions={{
        ...getDefaultHeaderOptions(currentTheme),
        headerRight: () => <HelpButton />,
        headerLeft: () => null, // disable back button for auth screens
        headerTitle: "",
        headerBackVisible: false,
      }}
    >
      <Stack.Screen name="index" options={{ headerShown: false }} />
      <Stack.Screen name="home" />
      <Stack.Screen name="signin" />
      <Stack.Screen name="signup" />
      <Stack.Screen name="forget-password" />
      <Stack.Screen name="reset-password" />
      <Stack.Screen name="verify-email" />
      <Stack.Screen name="(help)" options={{ headerShown: false }} />
    </Stack>
  );
}
