import { getDefaultHeaderOptions } from "@/src/_helper/navigation/navigationOption";
import { useLanguage } from "@/src/context/LanguageContext";
import { useTheme } from "@/src/context/ThemeContext";
import { Stack } from "expo-router";

export default function HelpLayout() {
  const { currentTheme } = useTheme();
  const { t } = useLanguage();

  return (
    <Stack screenOptions={getDefaultHeaderOptions(currentTheme)}>
      <Stack.Screen
        name="help"
        options={{
          title: t("layouts.help"),
        }}
      />
      <Stack.Screen
        name="call-support"
        options={{
          title: t("layouts.callSupport"),
        }}
      />
      <Stack.Screen
        name="email-support"
        options={{
          title: t("layouts.emailSupport"),
        }}
      />
      <Stack.Screen
        name="login-issue"
        options={{
          title: t("layouts.loginIssue"),
        }}
      />
      <Stack.Screen
        name="password-recovery"
        options={{
          title: t("layouts.passwordRecovery"),
        }}
      />
      <Stack.Screen
        name="signup-issue"
        options={{
          title: t("layouts.signupIssue"),
        }}
      />
      <Stack.Screen
        name="language"
        options={{
          title: t("layouts.language"),
        }}
      />
      <Stack.Screen
        name="theme"
        options={{
          title: t("layouts.theme"),
        }}
      />
    </Stack>
  );
}
