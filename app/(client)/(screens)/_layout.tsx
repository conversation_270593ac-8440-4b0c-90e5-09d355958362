import { getDefaultHeaderOptions } from "@/src/_helper/navigation/navigationOption";
import Search from "@/src/components/buttons/Search";
import { useLanguage } from "@/src/context/LanguageContext";
import { useTheme } from "@/src/context/ThemeContext";
import { DarkTheme } from "@react-navigation/native";
import { Stack } from "expo-router";
import React from "react";
import { DefaultTheme, PaperProvider } from "react-native-paper";

export default function SettingsLayout() {
  const { currentTheme } = useTheme();
  const { t } = useLanguage();
  const paperTheme = currentTheme === "light" ? DefaultTheme : DarkTheme;
  return (
    <PaperProvider theme={paperTheme}>
      <Stack screenOptions={getDefaultHeaderOptions(currentTheme)}>
        <Stack.Screen
          name="notification"
          options={{
            headerTitle: t("screens.headers.notification"),
          }}
        />
        <Stack.Screen
          name="brands-list"
          options={{
            headerRight: () => <Search />,
            headerTitle: t("screens.headers.brands") || "Brands",
          }}
        />
        <Stack.Screen
          name="products-list"
          options={{
            headerTitle: t("screens.headers.products"),
            headerRight: () => <Search />,
          }}
        />
        <Stack.Screen name="brands" options={{ headerShown: false }} />
        <Stack.Screen name="orders" options={{ headerShown: false }} />
        <Stack.Screen name="products" options={{ headerShown: false }} />
      </Stack>
    </PaperProvider>
  );
}
