import { getDefaultHeaderOptions } from "@/src/_helper/navigation/navigationOption";
import Share from "@/src/components/buttons/Share";
import { useLanguage } from "@/src/context/LanguageContext";
import { useTheme } from "@/src/context/ThemeContext";
import { Stack } from "expo-router";
import React from "react";

export default function SettingsLayout() {
  const { currentTheme } = useTheme();
  const { t } = useLanguage();

  return (
    <Stack screenOptions={getDefaultHeaderOptions(currentTheme)}>
      <Stack.Screen
        name="brand-details"
        options={{
          title: t("screens.headers.brand-details"),
          headerRight: () => <Share type="brand" />,
        }}
      />
      <Stack.Screen
        name="products-brand"
        options={{ title: t("screens.headers.products-brand") }}
      />
    </Stack>
  );
}
