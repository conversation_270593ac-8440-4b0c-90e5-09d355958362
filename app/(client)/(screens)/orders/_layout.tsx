import { getDefaultHeaderOptions } from "@/src/_helper/navigation/navigationOption";
import { useTheme } from "@/src/context/ThemeContext";
import { Stack } from "expo-router";
import React from "react";

export default function OrdersLayout() {
  const { currentTheme } = useTheme();

  return (
    <Stack screenOptions={getDefaultHeaderOptions(currentTheme)}>
      <Stack.Screen name="[id]" options={{ headerShown: false }} />
    </Stack>
  );
}
