import NotificationPermission from "@/src/context/NotificationPermission";
import RoleProtectedLayout from "@/src/context/RoleProtectedLayout";
import { CartProvider } from "@/src/hooks/useCart";
import { Stack } from "expo-router";

export default function AuthLayout() {
  return (
    <RoleProtectedLayout allowedRoles={["client"]}>
      <NotificationPermission>
        <CartProvider>
          <Stack screenOptions={{ headerShown: false }}>
            <Stack.Screen name="(tabs)" />
            <Stack.Screen name="(settings)" />
            <Stack.Screen name="(screens)" />
          </Stack>
        </CartProvider>
      </NotificationPermission>
    </RoleProtectedLayout>
  );
}
