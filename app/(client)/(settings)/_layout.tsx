import { getDefaultHeaderOptions } from "@/src/_helper/navigation/navigationOption";
import { useLanguage } from "@/src/context/LanguageContext";
import { useTheme } from "@/src/context/ThemeContext";
import { Stack } from "expo-router";
import React from "react";

export default function SettingsLayout() {
  const { currentTheme } = useTheme();
  const { t } = useLanguage();

  return (
    <Stack screenOptions={getDefaultHeaderOptions(currentTheme)}>
      <Stack.Screen
        name="about"
        options={{
          headerTitle: t("screens.headers.about"),
        }}
      />
      <Stack.Screen
        name="help"
        options={{
          headerTitle: t("screens.headers.help"),
        }}
      />
      <Stack.Screen
        name="language"
        options={{
          headerTitle: t("screens.headers.language"),
        }}
      />

      <Stack.Screen
        name="notifications"
        options={{
          headerTitle: t("screens.headers.notifications"),
        }}
      />
      <Stack.Screen
        name="profile"
        options={{
          headerTitle: t("screens.headers.profile"),
        }}
      />
      <Stack.Screen
        name="security"
        options={{
          headerTitle: t("screens.headers.security"),
        }}
      />
      <Stack.Screen
        name="theme"
        options={{
          headerTitle: t("screens.headers.theme"),
        }}
      />
      <Stack.Screen
        name="orders"
        options={{
          headerTitle: t("screens.headers.orders"),
        }}
      />
      <Stack.Screen
        name="locations"
        options={{
          headerTitle: t("screens.headers.locations"),
        }}
      />
    </Stack>
  );
}
