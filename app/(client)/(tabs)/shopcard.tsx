// screens/CartScreen.tsx

import CartLayout from "@/src/components/cart/CartLayout";
import { CartWithItems } from "@/src/components/client/cart/CartWithItems";
import { MainContainerForScreens } from "@/src/components/containers/MainContainerForScreens";

export default function CartScreen() {
  return (
    <CartLayout hideFloatingButton={true}>
      <MainContainerForScreens noScroll>
        <CartWithItems />
      </MainContainerForScreens>
    </CartLayout>
  );
}
