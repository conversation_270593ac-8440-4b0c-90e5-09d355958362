import { scale, scaleFont } from "@/src/_helper/Scaler";
import Search from "@/src/components/buttons/Search";
import SearchAndNotification from "@/src/components/buttons/SearchAndNotification";
import { Colors } from "@/src/constants/Colors";
import { useLanguage } from "@/src/context/LanguageContext";
import { useTheme } from "@/src/context/ThemeContext";
import AntDesign from "@expo/vector-icons/AntDesign";
import Entypo from "@expo/vector-icons/Entypo";
import FontAwesome from "@expo/vector-icons/FontAwesome";
import Fontisto from "@expo/vector-icons/Fontisto";
import { DarkTheme } from "@react-navigation/native";
import { Tabs } from "expo-router";
import React from "react";
import { DefaultTheme, PaperProvider } from "react-native-paper";
export default function TabLayout() {
  const { currentTheme } = useTheme();
  const { t } = useLanguage();

  const paperTheme = currentTheme === "light" ? DefaultTheme : DarkTheme;
  return (
    <PaperProvider theme={paperTheme}>
      <Tabs
        screenOptions={() => ({
          tabBarActiveTintColor: Colors[currentTheme ?? "dark"].inactive_tab,
          tabBarInactiveTintColor: Colors[currentTheme ?? "dark"].tab,
          tabBarStyle: {
            backgroundColor: Colors[currentTheme ?? "dark"].background,
            display: "flex",
            height: scale(50),
            borderTopWidth: 0,
            elevation: 0,
            shadowOpacity: 0,
          },
          headerShown: true,
          headerRight: () => <SearchAndNotification />,
          headerStyle: {
            backgroundColor: Colors[currentTheme ?? "dark"].background,
          },
          headerLeft: () => null,
          headerTitle: t("app.name"),
          headerTintColor: Colors[currentTheme ?? "dark"].text,
          headerTitleAlign: "center",
          headerTitleStyle: {
            fontFamily: "TintBold",
            fontSize: scaleFont(18),
          },
        })}
      >
        <Tabs.Screen
          name="index"
          options={{
            title: t("navigation.tabs.home"),
            tabBarIcon: ({ color, focused }) => (
              <Entypo
                name="home"
                color={color}
                size={focused ? scale(24) : scale(20)}
              />
            ),
          }}
        />
        <Tabs.Screen
          name="favorite"
          options={{
            title: t("navigation.tabs.favorites"),
            headerTitle: t("navigation.tabs.favorites"),
            headerRight: () => <Search />,
            tabBarIcon: ({ color, focused }) => (
              <AntDesign
                name="heart"
                color={color}
                size={focused ? scale(24) : scale(20)}
              />
            ),
          }}
        />
        <Tabs.Screen
          name="shopcard"
          options={{
            title: t("navigation.tabs.shopc"),
            headerTitle: t("navigation.tabs.shopc"),
            headerRight: () => null,
            tabBarIcon: ({ color, focused }) => (
              <Fontisto
                name="shopping-bag-1"
                color={color}
                size={focused ? scale(24) : scale(20)}
              />
            ),
          }}
        />
        <Tabs.Screen
          name="settings"
          options={{
            title: t("navigation.tabs.settings"),
            headerShown: false,
            tabBarIcon: ({ color, focused }) => (
              <FontAwesome
                name="user"
                color={color}
                size={focused ? scale(24) : scale(20)}
              />
            ),
          }}
        />
      </Tabs>
    </PaperProvider>
  );
}
